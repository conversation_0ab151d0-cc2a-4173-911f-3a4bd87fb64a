﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMFile;

namespace HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement
{
    /// <summary>
    /// 素材接口服务类
    /// </summary>
    public interface IMMFileService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMFileGetByIdAsyncResponseDto> GetByIdAsync(MMFileGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<MMFileGetAllAsyncResponseDto> GetAllAsync(MMFileGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 上传素材信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMFileUploadResponseDto> UploadAsync(MMFileUploadRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMFileUpdateResponseDto> UpdateAsync(MMFileUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMFileDeleteResponseDto> DeleteAsync(MMFileDeleteRequestDto requestDto);
    }
}
