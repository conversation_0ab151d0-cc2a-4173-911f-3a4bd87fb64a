﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIBdFile;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicIBdFile;

namespace HdProject.Application.Services.Interfaces.SongBase.WallPainting
{
    /// <summary>
    /// 主题绑定素材接口
    /// </summary>
    public interface IWPTopicIBdFileService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicIBdFileGetByIdResponseDto> GetByIdAsync(WPTopicIBdFileGetByIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<WPTopicIBdFileGetAllResponseDto> GetAllAsync(WPTopicIBdFileGetAllRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicIBdFileAddResponseDto> AddAsync(WPTopicIBdFileAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicIBdFileUpdateResponseDto> UpdateAsync(WPTopicIBdFileUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicIBdFileDeleteResponseDto> DeleteAsync(WPTopicIBdFileDeleteRequestDto requestDto);
    }
}
