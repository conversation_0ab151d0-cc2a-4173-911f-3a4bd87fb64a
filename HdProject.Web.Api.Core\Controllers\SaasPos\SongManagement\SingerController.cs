﻿using HdProject.Application.Services.Interfaces.SaasPos.SongSyncCenter;
using HdProject.Application.Services.Interfaces.SaasPos;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using HdProject.Domain.Context;
using HdProject.Domain.Context.SongManagement;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.Song
{
    /// <summary>
    /// 歌星接口
    /// </summary>
    [Route("[controller]/[Action]")]
    public class SingerController : PublicControllerBase
    {
        private readonly ISingerService _singerService;


        public SingerController(ISingerService singerService)
            => _singerService = singerService;
        /// <summary>
        /// 新增歌星
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> NewSingerAdded(SingerContext context)
        {
            var res = await _singerService.NewSingerAdded(context);
            return ApiData(res);
        }
        /// <summary>
        ///歌星列表详细信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> SingerlistInfo([FromQuery] SingerContext context)
        {
            var res = await _singerService.SingerlistInfo(context);
            return ApiData(res);
        }
        /// <summary>
        ///歌星信息更新
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> SingerInfoUpdate(SingerContext context)
        {
            var res = await _singerService.SingerInfoUpdate(context);
            return ApiData(res);
        }
        /// <summary>
        /// 歌星删除
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> DeleteSinger(SingerContext context)
        {
            var res = await _singerService.DeleteSinger(context);
            return ApiData(res);
        }
        /// <summary>
        /// 获取歌星相关歌曲
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetSingerSong([FromQuery] SingerContext context)
        {
            var res = await _singerService.GetSingerSong(context);
            return ApiData(res);
        }
    }
}
