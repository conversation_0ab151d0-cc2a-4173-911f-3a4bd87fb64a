﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;

namespace HdProject.Infrastructure.Repositorys.Imp
{
    public class RepositoryDefault<T> : Repository<T>,IRepositoryDefault<T>
        where T : class, new()
    {
        public RepositoryDefault(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "Default";
        }
    }
}
