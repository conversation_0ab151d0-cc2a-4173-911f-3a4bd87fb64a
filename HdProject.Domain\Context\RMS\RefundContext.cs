﻿using HdProject.Domain.Result.Page;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Context.RMS
{
    public class RefundContext
    {
        public class ApplyRefundListContext
        {
            public string? ApplyNo { get; set; }
            public string? ConfirmNo { get; set; }
            public string? ApplyUserID { get; set; }
            public int? IsEnd { get; set; }
            public DateTime? BeginDate { get; set; }
            public DateTime? EndDate { get; set; }
            public int ShopID { get; set; }
            
            //public int PageIndex { get; set; }
            //public int PageSize { get; set; }
            public Pagination Paging { get; set; }//分页
        }
        public class CardContext
        {
            public string? PhoneNumber { get; set; }
            public int? NCodeNo { get; set; }
            public Pagination Paging { get; set; }//分页
        }
        public class ConfirmRefundContext
        {
            public string ApplyNo { get; set; }
            public string ConfirmNo { get; set; }
            public string ConfirmName { get; set; }
            public string CheckName { get; set; }
            public string OpenId { get; set; }
            public string Transaction_id { get; set; }
            public decimal TotalAmount { get; set; }
            public int RefundType { get; set; }//固定1
            public string? RefundRemark { get; set; }//退款说明
            //public string Openid { get; set; }

            //public Pagination Paging { get; set; }//分页
        }
        public class ApplyRefundContext
        {
            public string InCodeKey { get; set; }
            public int Torecharge { get; set; }
            public decimal ApplyMoney { get; set; }
            public int ApplyNum { get; set; }
            public string ApplyShopId { get; set; }
            public string ApplyUserID { get; set; }
            public string ApplyName { get; set; }
            public string ApplyReason { get; set; }
            public int NCodeNo { get; set; }

            //public Pagination Paging { get; set; }//分页
        }

    }
}
