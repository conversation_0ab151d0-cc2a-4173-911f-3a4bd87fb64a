﻿using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Infrastructure.Repositorys.Imp
{
    public class RepositoryRms<T> : Repository<T>, IRepositoryRms<T>
        where T : class, new()
    {
        public RepositoryRms(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "Rms";
        }
    }
}
