﻿using HdProject.Application.Services.Interfaces.SaasPos.SongManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice;
using HdProject.Domain.Context.SaasPos.SongFeedback;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.SongManagement
{
    /// <summary>
    /// 缺歌登记控制器
    /// </summary>
    public class SongFeedbackController : PublicControllerBase
    {
        private readonly ISongFeedbackService _songFeedbackService;
        private readonly ILogger<SongFeedbackController> _logger;

        public SongFeedbackController(ISongFeedbackService songFeedbackService, ILogger<SongFeedbackController> logger)
        {
            _songFeedbackService = songFeedbackService;
            _logger = logger;
        }


        /// <summary>
        /// 新增缺歌登记信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> SongFeedbackAdd([FromBody] SongFeedbackAddRequestDto request)
        {
            var result = await _songFeedbackService.AddAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }
    }
}
