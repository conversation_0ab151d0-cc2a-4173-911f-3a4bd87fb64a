﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos
{
    public partial class Shop_BookCacheInfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public int Id { get; set; }
        public int OrderId { get; set; }
        public int ShopId { get; set; }
        public string CustName { get; set; }
        public string CustTel { get; set; }
        public string ComeDate { get; set; }
        public string ComeTime { get; set; }
        public string Beg_Key { get; set; }
        public string Beg_Name { get; set; }
        public string End_Key { get; set; }
        public string End_Name { get; set; }
        public int Numbers { get; set; }
        public string BookingAgent { get; set; }
        public string AgentTel { get; set; }
        public int PayAmount { get; set; }
        public int BookStatus { get; set; }
        public string OpenId { get; set; }
        public System.DateTime CreateTime { get; set; }
        public string BookNo { get; set; }
        public bool IsAdvance { get; set; }
        public string RtNo { get; set; }
        public string RtName { get; set; }
        public int CtNo { get; set; }
        public string CtName { get; set; }
        public string CustSex { get; set; }
        public bool StayRoom { get; set; }
        public string Body { get; set; }
        public string CustRemark { get; set; }
        public string BookIKey { get; set; }
        public bool IsDeposit { get; set; }
        public Nullable<System.Guid> LeaderId { get; set; }
    }
}
