﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Threading.Tasks;
using Furion.DatabaseAccessor;
using Furion.LinqBuilder;
using HdProject.Application.Services.Interfaces.SaasPos.CommodityManagement;
using HdProject.Domain.Context;
using HdProject.Domain.Context.CommodityManagement;
using HdProject.Domain.DTOs;
using HdProject.Domain.DTOs.CommodityManagement;
using HdProject.Domain.Entities.CommodityManagement;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Entities.SaasPos.CommodityManagement;
using HdProject.Domain.Interfaces;
using SqlSugar;
using SqlSugar.Extensions;
using StackExchange.Profiling.Internal;

namespace HdProject.Application.Services.CommodityAdjustment
{
    //11asdsadas
    //asda
    public class CommodityAdjustmentService : ICommodityAdjustmentService
    {
        //私有字段（依赖的仓储接口），每个仓储对应一个数据库表
        private readonly IRepositorySaas<Commodity> _commodityRepository;
        private readonly IRepositorySaas<CommodityAdjustmentList> _adjustmentListRepository;
        private readonly IRepositorySaas<CommodityAdjustmentDetail> _adjustmentDetailRepository;
        private readonly IRepositorySaas<CommodityAdjustmentPackage> _commodityAdjustmentPackageRepository;
        private readonly IRepositorySaas<CommodityPackageDelivery> _packageDeliveryRepository;
        private readonly IRepositorySaas<CommodityType> _commodityTypeRepository;
        public CommodityAdjustmentService(IRepositorySaas<Commodity> commodityRepository,
            IRepositorySaas<CommodityAdjustmentList> adjustmentListRepository,
            IRepositorySaas<CommodityAdjustmentDetail> adjustmentDetailRepository,
            IRepositorySaas<CommodityAdjustmentPackage> commodityAdjustmentPackageRepository,
            IRepositorySaas<CommodityPackageDelivery> packageDeliveryRepository,
            IRepositorySaas<CommodityType> commodityTypeRepository
           )
        {
            _commodityRepository = commodityRepository;//商品表
            _adjustmentListRepository = adjustmentListRepository;//商品调整表
            _adjustmentDetailRepository = adjustmentDetailRepository;//商品调整详情表
            _commodityAdjustmentPackageRepository = commodityAdjustmentPackageRepository;//商品调整套餐表
            _packageDeliveryRepository = packageDeliveryRepository;//套餐配送表
            _commodityTypeRepository = commodityTypeRepository;//商品类别明细表




        }
        /// <summary>
        /// 获取草稿箱个人记录(根据用户ID获取）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<GetDraftRecordDto>> GetCommDraftRecord(DraftContext context)
        {
            try
            {
                //var adjustmentLists = await _adjustmentListRepository.GetListAsync(w =>
                //    w.UserID == context.UserId &&
                //    w.Status == 0);
                // 1. 构建基础查询条件
                Expression<Func<CommodityAdjustmentList, bool>> listExpression = w =>
                    w.UserID == context.UserId &&
                    w.Status == 0;

                if (!string.IsNullOrEmpty(context.Keyword))
                {
                    listExpression = listExpression.And(w => w.Name.Contains(context.Keyword));
                }

                //2.查询商品调整表并分页
                var adjustmentLists = await _adjustmentListRepository.GetPageListAsync(
                    context.Paging,
                    listExpression);

                adjustmentLists = adjustmentLists.OrderByDescending(a => a.CreationTime).ToList();

                if (!adjustmentLists.Any())
                {
                    return new List<GetDraftRecordDto>();
                }

                var adjustmentIds = adjustmentLists.Select(a => a.AdjustmentID).ToList();

                // 3. 查询每个调整单的详情数量
                var details = await _adjustmentDetailRepository.GetListAsync(d => adjustmentIds.Contains(d.AdjustmentID));
                var detailCounts = adjustmentIds
              .ToDictionary(
                  id => id,
                  id => details.Count(d => d.AdjustmentID == id)
              );
                //Expression<Func<CommodityAdjustmentList, bool>> listExpression = w =>
                //    w.UserID == context.UserId &&
                //    w.Status == 0;

                //var adjustmentLists2 = await _adjustmentListRepository.GetPageListAsync(
                //    context.Paging,
                //    listExpression);
                var result = adjustmentLists.Select(a => new GetDraftRecordDto
                {
                    Name = a.Name,
                    UserID = a.UserID,
                    CreateTime = a.CreationTime,
                    DetailCount = detailCounts.ContainsKey(a.AdjustmentID) ? detailCounts[a.AdjustmentID] : 0,
                    IsDeleted = a.IsDeleted,
                    AdjustmentID = a.AdjustmentID.ToString()
                }).ToList();
                //context.Paging.Records = result.Count;
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception("获取草稿箱记录时发生错误，请稍后重试", ex);
            }
        }
        /// <summary>
        /// 获取个人草稿详情记录(根据用户ID,草稿ID获取）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<GetDraftRecorListdDto>> GetCommDraftListRecord(DraftListContext context)
        {
            try
            {
                // 1. 查询商品调整表--
                var adjustmentLists = await _adjustmentListRepository.GetListAsync(w =>
                    w.UserID == context.UserId && w.Status == 0 && w.AdjustmentID == context.AdjustmentID)
                    ?? throw new InvalidOperationException($"用户 {context.UserId} 的{context.AdjustmentID}商品调整草稿不存在");

                var adjustmentIds = adjustmentLists.Select(a => a.AdjustmentID).ToList();

                //2. 构建基础查询条件并执行分页查询
                Expression<Func<CommodityAdjustmentDetail, bool>> baseExpression = x =>
                     adjustmentIds.Contains(x.AdjustmentID) &&
                    (string.IsNullOrEmpty(context.Keyword) ||
                     x.FdCName.Contains(context.Keyword) ||
                     x.ComboName.Contains(context.Keyword)
                     );

                var pageResult = await _adjustmentDetailRepository.GetPageListAsync(context.Paging, baseExpression);

                if (!pageResult.Any())
                {
                    return null;

                }

                // 3. 处理普通商品
                var regularItems = await ProcessRegularCommodities(pageResult.Where(d => d.Type == 1).ToList());
                if (regularItems == null)
                {
                    return [];
                }
                // 4. 处理套餐商品
                var comboItems = await ProcessComboCommodities(
                    pageResult.Where(d => d.Type == 2 && d.DetailID != null).ToList());
                if (comboItems == null)
                {
                    return [];
                }
                // 5. 合并所有结果并按创建时间降序排列
                return regularItems.Concat(comboItems)
                    .OrderByDescending(x => x.CreateTime)
                    .ToList();
            }
            catch (Exception ex)
            {
                throw new Exception("获取草稿详情记录时发生错误，请稍后重试", ex);
            }
        }


        /// <summary>
        /// 删除草稿个人记录(根据用户ID 和调整ID去删除）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<bool> DeleteCommDraftRecord(DelectDraftRecordContext context)
        {

            _adjustmentListRepository.BeginTran();
            try
            {

                // 1. 验证AdjustmentID是否存在且可删除
                var adjustmentRecord = await _adjustmentListRepository.GetFirstAsync(w =>
                    w.AdjustmentID == context.AdjustmentID && w.UserID == context.UserId &&
                    !w.IsDeleted &&
                    w.Status == 0);

                if (adjustmentRecord == null)
                    throw new InvalidOperationException($"未找到用户{context.UserId}AdjustmentID为 {context.AdjustmentID} 的记录");

                // 2. 获取所有相关的DetailID
                var relatedDetails = await _adjustmentDetailRepository.GetListAsync(
                    w => w.AdjustmentID == context.AdjustmentID && !w.IsDeleted);

                var detailIds = relatedDetails.Select(d => d.DetailID).ToList();

                // 3. 软删除CommodityAdjustmentPackage表中匹配的记录
                if (detailIds.Any())
                {
                    var packages = await _commodityAdjustmentPackageRepository.GetListAsync(
                        p => detailIds.Contains(p.DetailID) && !p.IsDeleted);

                    foreach (var package in packages)
                    {
                        package.IsDeleted = true;
                        await _commodityAdjustmentPackageRepository.UpdateAsync(package);
                    }
                }

                // 4. 软删除明细记录
                foreach (var detail in relatedDetails)
                {
                    detail.IsDeleted = true;
                    await _adjustmentDetailRepository.UpdateAsync(detail);
                }

                // 5. 软删除主表记录
                adjustmentRecord.IsDeleted = true;
                await _adjustmentListRepository.UpdateAsync(adjustmentRecord);

                // 提交事务
                _adjustmentListRepository.CommitTran();
                return true;
            }
            catch (Exception ex)
            {
                // 回滚事务
                _adjustmentListRepository.RollbackTran();

                // 记录日志
                throw new InvalidOperationException("调整ID不存在或无权限删除");

            }


        }
        /// <summary>
        /// 添加或更新草稿记录

        /// </summary>
        public async Task<bool> AddOrUpdateCommDraftRecords(List<AdjustmentRequestContext> requests)
        {


            var currentUserId = requests[0].UserId;

            _adjustmentListRepository.BeginTran();


            try
            {
                // 处理已有记录
                var existingAdjustmentIds = requests
                    .Where(c => c.AdjustmentID != null)
                    .Select(c => c.AdjustmentID)
                    .Distinct()
                    .ToList();

                if (existingAdjustmentIds.Count > 0)
                {
                    var userAdjustmentIds = (await _adjustmentListRepository.GetListAsync(
                        a => existingAdjustmentIds.Contains(a.AdjustmentID) &&
                             a.UserID == currentUserId))
                        .Select(a => a.AdjustmentID)
                        .ToList();

                    // 多表级联删除
                    var relatedDetailIds = (await _adjustmentDetailRepository.GetListAsync(
                        d => existingAdjustmentIds.Contains(d.AdjustmentID)))
                        .Select(d => d.DetailID)
                        .ToList();

                    if (relatedDetailIds.Count > 0)
                        await _commodityAdjustmentPackageRepository.DeleteAsync(p => relatedDetailIds.Contains(p.DetailID));
                    await _adjustmentDetailRepository.DeleteAsync(d => existingAdjustmentIds.Contains(d.AdjustmentID));
                    await _adjustmentListRepository.DeleteAsync(w => existingAdjustmentIds.Contains(w.AdjustmentID));
                }

                // 处理每个请求
                foreach (var request in requests)
                {
                    var adjustmentId = request.AdjustmentID == null || request.AdjustmentID == Guid.Empty
                        ? Guid.NewGuid()
                        : request.AdjustmentID.Value;

                    // 保存主表
                    await _adjustmentListRepository.InsertAsync(new CommodityAdjustmentList
                    {
                        AdjustmentID = adjustmentId,
                        UserID = currentUserId,
                        Name = request.Name,
                        Status = 0,
                        CreationTime = DateTime.Now,
                        ActionType = 1
                    });

                    // 处理明细
                    if (request.AddDraftRecordList?.Count > 0)
                    {
                        foreach (var detail in request.AddDraftRecordList)
                        {
                            var detailId = detail.DetailID == null || detail.DetailID == Guid.Empty
                                ? Guid.NewGuid()
                                : detail.DetailID.Value;

                            // 保存明细
                            await _adjustmentDetailRepository.InsertAsync(new CommodityAdjustmentDetail
                            {
                                DetailID = detailId,
                                AdjustmentID = adjustmentId,
                                Type = detail.Type == "1" ? (byte)1 : (byte)2,
                                FdNo = detail.FdNo,
                                FtNo = detail.FtNo,
                                FdCName = detail.FdCName,
                                ComboNo = detail.ComboNo,
                                ComboName = detail.ComboName,
                                FdQty = detail.FdQty,
                                MarketPrice = detail.MarketPrice,
                                SalePrice = detail.SalePrice,
                                PriceMode = detail.PriceMode,
                                ApplicableStores = detail.ApplicableStores,
                                CreateTime = DateTime.Now
                            });

                            // 处理套餐商品
                            if (detail.Type == "2" && detail.PackageItems?.Count > 0)
                            {
                                var packageItems = new List<CommodityAdjustmentPackage>();

                                foreach (var item in detail.PackageItems)
                                {
                                    // 获取商品数量
                                    int quantity = item.PGFdQty;

                                    // 根据数量插入对应数量的记录
                                    for (int i = 0; i < quantity; i++)
                                    {
                                        packageItems.Add(new CommodityAdjustmentPackage
                                        {
                                            ComboNo = detail.ComboNo,
                                            DetailID = detailId,
                                            FtNo = detail.FtNo,
                                            ComboName = detail.ComboName,
                                            FdNo = item.PGFdNo,
                                            CreateTime = DateTime.Now,
                                            IsDeleted = false,
                                        });
                                    }
                                }

                                await _commodityAdjustmentPackageRepository.InsertRangeAsync(packageItems);
                            }
                        }
                    }
                }

                _adjustmentListRepository.CommitTran();
                return true;
            }
            catch (Exception ex)
            {
                _adjustmentListRepository.RollbackTran();
                throw new Exception("保存草稿失败: " + ex.Message, ex);
            }
        }
        ///// <summary>
        ///// 查询已有商品
        ///// </summary>
        ///// <param name="context"></param>
        ///// <returns></returns>
        ///// <exception cref="NotImplementedException"></exception>
        public async Task<List<CommodityDto>> GetCommExistingGoods(GetExistingGoodsContext context)
        {
            try
            {


                // 2. 查询商品类别明细表并转换为字典提高查找效率
                var commodityTypes = await _commodityTypeRepository.GetListAsync(x => !x.IsDeleted);
                var commodityTypeDict = commodityTypes.ToDictionary(x => x.FtNo);

                // 3. 构建基础查询条件并执行分页查询（普通商品）
                Expression<Func<Commodity, bool>> baseExpression = x =>
                    !x.IsDeleted;
                if (context.Keyword != null)
                {
                    baseExpression = baseExpression.And(x => x.FdCName.Contains(context.Keyword));
                }
                var pageResult = await _commodityRepository.GetPageListAsync(context.Paging, baseExpression);
                var commodities = pageResult.ToList();

                var result = new List<CommodityDto>();

                // 处理普通商品
                foreach (var commodity in commodities)
                {
                    commodityTypeDict.TryGetValue(commodity.FtNo, out var typeInfo);

                    result.Add(new CommodityDto
                    {

                        Type = "1",
                        FdNo = commodity.FdNo,
                        FtNo = commodity.FtNo,
                        FtCName = typeInfo?.FtCName ?? string.Empty,
                        FdCName = commodity.FdCName,
                        FdQty = commodity.FdQty,
                        IsPackage = false,
                        CreateTime = commodity.CreateTime,
                        UpdateTime = commodity.UpdateTime
                    });
                }

                // 处理套餐数据
                var allPackageDeliveries = await _packageDeliveryRepository.GetListAsync(x => !x.IsDeleted || x.ComboName == context.Keyword);
                if (context.Keyword != null)
                {
                    allPackageDeliveries = allPackageDeliveries.Where(x => x.ComboName.Contains(context.Keyword)).ToList();
                }
                if (allPackageDeliveries.Count > 0)
                {
                    // 1. 获取套餐中所有涉及的商品编号
                    var packageItemFdNos = allPackageDeliveries
                        .Select(x => x.FdNo)
                        .Distinct()
                        .ToList();

                    // 2. 批量查询这些商品信息
                    var packageCommodities = await _commodityRepository.GetListAsync(x =>
                        !x.IsDeleted && packageItemFdNos.Contains(x.FdNo));

                    if (allPackageDeliveries.Count > 0)
                    {
                        // 构建商品编号到商品信息的映射字典，提高查询效率
                        var commodityDict = packageCommodities.ToDictionary(c => c.FdNo, c => c);

                        // 先按套餐分组
                        var packageGroups = allPackageDeliveries
                        .GroupBy(x => x.ComboNo) // 只按套餐编号分组
                        .ToList();

                        foreach (var packageGroup in packageGroups)
                        {
                            var firstItem = packageGroup.First();


                            commodityTypeDict.TryGetValue(firstItem.FtNo, out var typeInfo);

                            // 处理套餐内商品 - 按FdNo分组并合计数量
                            var packageItems = packageGroup
                                .GroupBy(item => item.FdNo) // 按商品编号分组
                                .Select(g =>
                                {
                                    var firstItem = g.First();
                                    commodityDict.TryGetValue(firstItem.FdNo, out var commodity);

                                    return new PackageItemDto
                                    {
                                        FdNo = firstItem.FdNo,
                                        FdQty = g.Sum(p => commodity.FdQty),
                                        FdCName = commodity.FdCName,

                                    };
                                })
                                .ToList();
                            result.Add(new CommodityDto
                            {
                                Type = "2",
                                ComboNo = firstItem.ComboNo,
                                FtNo = firstItem.FtNo,
                                FtCName = typeInfo?.FtCName ?? string.Empty,
                                ComboName = firstItem.ComboName,
                                IsPackage = true,
                                CreateTime = firstItem.CreateTime,
                                UpdateTime = firstItem.UpdateTime,
                                PackageItems = packageItems
                            });
                        }
                    }

                    // 筛选逻辑
                    if (!string.IsNullOrEmpty(context.FtNo))
                    {
                        result = result.Where(x => x.FtNo == context.FtNo).ToList();
                    }

                    if (context.IsPackage.HasValue)
                    {
                        result = result.Where(x => x.IsPackage == context.IsPackage.Value).ToList();
                    }
                }
                context.Paging.Records = result.Count();
                return result.OrderByDescending(x => x.CreateTime).ToList();
            }


            catch (Exception ex)
            {

                throw new Exception($"查询已有商品失败: {ex.Message}", ex);

            }
        }
        /// <summary>
        ///  获取（状态不为0）商品/套餐历史调价记录;
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<Dictionary<string, List<CommodityListDto>>> GetCommHistoricalPrice(CommodityPriceHistoryContext context)
        {
            try
            {
                Expression<Func<CommodityAdjustmentList, bool>> masterCondition = null;
                if (context.UserId == null)
                {

                    masterCondition = a => a.Status != 0 && !a.IsDeleted;//新增套餐里商品价格获取不需要用户ID
                }
                else
                {
                    masterCondition = a => a.UserID == context.UserId && a.Status != 0 && !a.IsDeleted;
                }
                // 1. 构建基础查询条件（未删除且状态不为0的记录）


                // 情况1：用户传入了FdNo或ComboNo编号
                if (!string.IsNullOrEmpty(context.FdNo) || !string.IsNullOrEmpty(context.ComboNo))
                {
                    // 2. 获取所有相关的主记录
                    //var allMasterRecords = await _adjustmentListRepository.GetPageListAsync(context.Paging, masterCondition);

                    var allMasterRecords = await _adjustmentListRepository.GetListAsync(masterCondition);

                    if (allMasterRecords == null || !allMasterRecords.Any())
                        return new Dictionary<string, List<CommodityListDto>>();

                    // 3. 获取关联的AdjustmentID列表
                    var adjustmentIds = allMasterRecords.Select(x => x.AdjustmentID).ToList();

                    // 4. 将主记录转换为字典备用
                    var adjustmentDict = allMasterRecords.ToDictionary(a => a.AdjustmentID, a => a);

                    Expression<Func<CommodityAdjustmentDetail, bool>> masterCondition2 = d => adjustmentIds.Contains(d.AdjustmentID) && !d.IsDeleted;
                    // 查询关联的详情记录
                    var detailResult = await _adjustmentDetailRepository.GetPageListAsync(context.Paging, masterCondition2);


                    if (detailResult == null || !detailResult.Any())
                        return new Dictionary<string, List<CommodityListDto>>();

                    // 处理传入的编号参数
                    var fdNos = !string.IsNullOrEmpty(context.FdNo) ? context.FdNo.Split(',', StringSplitOptions.RemoveEmptyEntries) : Array.Empty<string>();
                    var comboNos = !string.IsNullOrEmpty(context.ComboNo) ? context.ComboNo.Split(',', StringSplitOptions.RemoveEmptyEntries) : Array.Empty<string>();

                    // 过滤出符合条件的详情记录
                    var filteredDetails = detailResult
                        .Where(d => (fdNos.Length > 0 && d.Type == 1 && fdNos.Contains(d.FdNo)) ||
                                    (comboNos.Length > 0 && d.Type == 2 && comboNos.Contains(d.ComboNo)))
                        .ToList();

                    // 8. 关键字过滤
                    if (!string.IsNullOrEmpty(context.Keyword))
                    {
                        filteredDetails = filteredDetails.Where(d =>

                            (d.Type == 1 && (!string.IsNullOrEmpty(d.FdCName) && d.FdCName.Contains(context.Keyword))) ||
                            (d.Type == 2 && (!string.IsNullOrEmpty(d.ComboName) && d.ComboName.Contains(context.Keyword)))
                        ).ToList();
                    }
                    if (!filteredDetails.Any())
                        return new Dictionary<string, List<CommodityListDto>>();

                    // 分离普通商品和套餐商品
                    var regularCommodities = filteredDetails.Where(d => d.Type == 1).ToList();
                    var comboCommodities = filteredDetails.Where(d => d.Type == 2).ToList();

                    // 9. 处理普通商品
                    var regularCommodityDtos = await ProcessRegularCommodities(regularCommodities);

                    // 10. 处理套餐商品
                    var comboCommodityDtos = await ProcessComboCommodities(comboCommodities);

                    // 11. 构建结果字典
                    var result = new Dictionary<string, List<CommodityListDto>>();

                    // 处理普通商品记录
                    foreach (var group in regularCommodityDtos.GroupBy(d => d.FdNo))
                    {
                        var dtoList = group.OrderByDescending(d => d.CreateTime)
                            .Select(dto =>
                            {
                                adjustmentDict.TryGetValue(Guid.Parse(dto.AdjustmentID), out var masterRecord);

                                return new CommodityListDto
                                {
                                    //UserID = masterRecord?.UserID ?? string.Empty,
                                    Status = masterRecord?.Status ?? 0,
                                    ActionType = masterRecord?.ActionType,
                                    PublishTime = masterRecord?.PublishTime,
                                    SyncTime = masterRecord?.SyncTime,
                                    Type = int.Parse(dto.Type),
                                    FtNo = dto.FtNo,
                                    CreationTime = dto.CreateTime,
                                    MarketPrice = dto.MarketPrice,
                                    SalePrice = dto.SalePrice,
                                    PriceMode = dto.PriceMode,
                                    ApplicableStores = dto.ApplicableStores,
                                    FdNo = dto.FdNo,
                                    FdQty = dto.FdQty,
                                    FdCName = dto.FdCName
                                };
                            }).ToList();

                        var groupKey = $"单品-{group.First().FdCName}({group.Key})";
                        result.Add(groupKey, dtoList);
                    }

                    // 处理套餐记录 - 按ComboNo分组（不再按DetailID分组）
                    foreach (var group in comboCommodityDtos.GroupBy(d => d.ComboNo))
                    {
                        var firstDto = group.First();
                        var groupKey = $"套餐-{firstDto.ComboName}({firstDto.ComboNo})";

                        var dtoList = new List<CommodityListDto>();

                        // 合并相同套餐编号的所有记录
                        foreach (var dto in group.OrderByDescending(d => d.CreateTime))
                        {
                            adjustmentDict.TryGetValue(Guid.Parse(dto.AdjustmentID), out var masterRecord);

                            dtoList.Add(new CommodityListDto
                            {
                                //UserID = masterRecord?.UserID ?? string.Empty,
                                Status = masterRecord?.Status ?? 0,
                                ActionType = masterRecord?.ActionType,
                                PublishTime = masterRecord?.PublishTime,
                                SyncTime = masterRecord?.SyncTime,
                                Type = int.Parse(dto.Type),
                                FtNo = dto.FtNo,
                                FdQty = dto.FdQty,
                                CreationTime = dto.CreateTime,
                                ComboNo = dto.ComboNo,
                                ComboName = dto.ComboName,
                                MarketPrice = dto.MarketPrice,
                                SalePrice = dto.SalePrice,
                                PriceMode = dto.PriceMode,
                                ApplicableStores = dto.ApplicableStores,
                                ComboItems = dto.PackageItems,
                                DetailID = Guid.Parse(dto.DetailID)
                            });
                        }

                        // 添加到结果字典
                        if (result.ContainsKey(groupKey))
                        {
                            // 如果已存在相同套餐编号，合并列表
                            result[groupKey].AddRange(dtoList);
                            result[groupKey] = result[groupKey].OrderByDescending(x => x.CreationTime).ToList();
                        }
                        else
                        {
                            result.Add(groupKey, dtoList);
                        }
                    }

                    // 设置分页总记录数
                    context.Paging.Records = result.Values.Sum(list => list.Count);

                    return result;
                }
                // 情况2：用户没有传入编号，只传了UserId、Paging、Keyword
                else
                {
                    // 3. 查询主记录（使用分页）
                    var masterPageResult = await _adjustmentListRepository.GetListAsync(masterCondition);
                    //var  = await _adjustmentListRepository.GetPageListAsync(context.Paging, masterCondition);

                    if (masterPageResult == null || !masterPageResult.Any())
                        return new Dictionary<string, List<CommodityListDto>>();

                    // 4. 获取关联的AdjustmentID列表
                    var adjustmentIds = masterPageResult.Select(x => x.AdjustmentID).ToList();

                    // 5. 将主记录转换为字典备用
                    var adjustmentDict = masterPageResult.ToDictionary(a => a.AdjustmentID, a => a);

                    // 6. 构建详情表查询条件（关联主表ID且未删除）
                    Expression<Func<CommodityAdjustmentDetail, bool>> detailCondition =
                        d => adjustmentIds.Contains(d.AdjustmentID) && !d.IsDeleted;

                    // 7. 查询详情记录
                    var detailResult = await _adjustmentDetailRepository.GetPageListAsync(context.Paging, detailCondition);

                    if (detailResult == null || !detailResult.Any())
                        return new Dictionary<string, List<CommodityListDto>>();

                    // 8. 关键字过滤
                    if (!string.IsNullOrEmpty(context.Keyword))
                    {
                        var matchedMasterIds = masterPageResult
                            .Where(m => !string.IsNullOrEmpty(m.Name) && m.Name.Contains(context.Keyword))
                            .Select(m => m.AdjustmentID)
                            .ToList();

                        detailResult = detailResult.Where(d =>
                            matchedMasterIds.Contains(d.AdjustmentID) ||
                            (d.Type == 1 && (!string.IsNullOrEmpty(d.FdCName) && d.FdCName.Contains(context.Keyword))) ||
                            (d.Type == 2 && (!string.IsNullOrEmpty(d.ComboName) && d.ComboName.Contains(context.Keyword)))
                        ).ToList();
                    }

                    // 分离普通商品和套餐商品
                    var regularCommodities = detailResult.Where(d => d.Type == 1).ToList();
                    var comboCommodities = detailResult.Where(d => d.Type == 2).ToList();

                    // 9. 处理普通商品
                    var regularCommodityDtos = await ProcessRegularCommodities(regularCommodities);

                    // 10. 处理套餐商品
                    var comboCommodityDtos = await ProcessComboCommodities(comboCommodities);

                    // 11. 合并结果并按创建时间排序
                    var allItems = new List<CommodityListDto>();

                    // 处理普通商品
                    foreach (var dto in regularCommodityDtos.OrderByDescending(d => d.CreateTime))
                    {
                        adjustmentDict.TryGetValue(Guid.Parse(dto.AdjustmentID), out var masterRecord);

                        allItems.Add(new CommodityListDto
                        {
                            Name = masterRecord.Name,
                            UserID = masterRecord?.UserID ?? string.Empty,
                            Status = masterRecord?.Status ?? 0,
                            ActionType = masterRecord?.ActionType,
                            PublishTime = masterRecord?.PublishTime,
                            SyncTime = masterRecord?.SyncTime,
                            Type = int.Parse(dto.Type),
                            FtNo = dto.FtNo,
                            CreationTime = dto.CreateTime,
                            MarketPrice = dto.MarketPrice,
                            SalePrice = dto.SalePrice,
                            PriceMode = dto.PriceMode,
                            ApplicableStores = dto.ApplicableStores,
                            FdNo = dto.FdNo,
                            FdQty = dto.FdQty,
                            FdCName = dto.FdCName
                        });
                    }

                    // 处理套餐商品
                    foreach (var dto in comboCommodityDtos.OrderByDescending(d => d.CreateTime))
                    {
                        adjustmentDict.TryGetValue(Guid.Parse(dto.AdjustmentID), out var masterRecord);

                        allItems.Add(new CommodityListDto
                        {
                            Name = masterRecord.Name,
                            UserID = masterRecord?.UserID ?? string.Empty,
                            Status = masterRecord?.Status ?? 0,
                            ActionType = masterRecord?.ActionType,
                            PublishTime = masterRecord?.PublishTime,
                            SyncTime = masterRecord?.SyncTime,
                            Type = int.Parse(dto.Type),
                            FtNo = dto.FtNo,
                            FdQty = dto.FdQty,
                            CreationTime = dto.CreateTime,
                            ComboNo = dto.ComboNo,
                            ComboName = dto.ComboName,
                            MarketPrice = dto.MarketPrice,
                            SalePrice = dto.SalePrice,
                            PriceMode = dto.PriceMode,
                            ApplicableStores = dto.ApplicableStores,
                            ComboItems = dto.PackageItems,
                            DetailID = Guid.Parse(dto.DetailID)
                        });
                    }

                    // 12. 构建最终结果
                    var result = new Dictionary<string, List<CommodityListDto>>
                   {
                   { "查询结果", allItems.OrderByDescending(x => x.CreationTime).ToList() }
                    };


                    // 设置分页总记录数
                    context.Paging.Records = result.Values.Sum(list => list.Count);

                    return result;
                }

            }
            catch (Exception ex)
            {
                throw new Exception($"查询历史调价记录失败：{ex.Message}");
            }
        }
        /// <summary>
        /// 查看已发布,Status == 1;
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<List<CommodityPublishedDto>> GetCommPublished(GetCommPublishedContext context)
        {
            try
            {
                // 1. 构建基础查询条件（Status=1且未删除）
                Expression<Func<CommodityAdjustmentList, bool>> masterCondition = a => a.Status == 1 && !a.IsDeleted && a.UserID == context.UserId;

                // 4. 查询主记录（分页）
                var masterPageResult = await _adjustmentListRepository.GetListAsync(masterCondition);

                // 4.1 在内存中排序（确保最新在最前）
                masterPageResult = masterPageResult.OrderByDescending(a => a.CreationTime).ToList();


                if (masterPageResult == null || !masterPageResult.Any())
                    return new List<CommodityPublishedDto>();


                // 情况1：只传了用户ID，返回基本信息
                if (context.AdjustmentId == null)
                {
                    if (context.Keyword != null)
                    {
                        masterPageResult = masterPageResult.Where(d =>
                            (d.Name?.Contains(context.Keyword, StringComparison.OrdinalIgnoreCase) ?? false))
                            .ToList();
                    }
                    // 5. 获取关联的AdjustmentID列表
                    var adjustmentIds = masterPageResult.Select(x => x.AdjustmentID).ToList();

                    Expression<Func<CommodityAdjustmentDetail, bool>> masterCondition2 = d => adjustmentIds.Contains(d.AdjustmentID) && !d.IsDeleted;
                    // 查询关联的详情记录
                    var details = await _adjustmentDetailRepository.GetPageListAsync(context.Paging, masterCondition2);
                    // 6. 查询详情记录数量
                    //var details = await _adjustmentDetailRepository
                    //.GetListAsync(d => adjustmentIds.Contains(d.AdjustmentID) && !d.IsDeleted);

                    var detailCounts = adjustmentIds
                        .ToDictionary(
                            id => id,
                            id => details.Count(d => d.AdjustmentID == id)
                        );

                    // 7. 构建结果列表（已按创建时间排序）
                    var result = masterPageResult
                        .Select(master => new CommodityPublishedDto
                        {

                            Name = master.Name,
                            UserID = master.UserID,
                            AdjustmentId = master.AdjustmentID,
                            CreationTime = master.CreationTime,
                            DetailCount = detailCounts.GetValueOrDefault(master.AdjustmentID, 0)
                        }).ToList();

                    // 8. 更新分页信息
                    context.Paging.Records = result.Count;

                    return result;
                }
                // 情况2：传入了特定的AdjustmentId，返回详细信息
                else
                {
                    // 1. 查询指定AdjustmentId的主记录
                    var masterRecord = masterPageResult.FirstOrDefault(a => a.AdjustmentID == context.AdjustmentId);
                    if (masterRecord == null)
                        return new List<CommodityPublishedDto>();

                    // 2. 查询关联的详情记录
                    // 直接使用指定的AdjustmentId查询详情记录
                    Expression<Func<CommodityAdjustmentDetail, bool>> detailCondition =
                        d => d.AdjustmentID == context.AdjustmentId && !d.IsDeleted;

                    // 查询关联的详情记录
                    var details = await _adjustmentDetailRepository.GetPageListAsync(context.Paging, detailCondition);

                    // 多字段关键词过滤
                    if (context.Keyword != null)
                    {
                        details = details.Where(d =>
                            (d.FdCName?.Contains(context.Keyword, StringComparison.OrdinalIgnoreCase) ?? false) ||
                            (d.ComboName?.Contains(context.Keyword, StringComparison.OrdinalIgnoreCase) ?? false))
                            .ToList();
                    }

                    if (!details.Any())
                        return new List<CommodityPublishedDto>();

                    // 3. 处理普通商品
                    var regularItems = await ProcessRegularCommodities(details.Where(d => d.Type == 1).ToList());

                    // 4. 处理套餐商品
                    var comboItems = await ProcessComboCommodities(
                        details.Where(d => d.Type == 2 && d.DetailID != Guid.Empty).ToList());

                    // 5. 合并结果并添加主记录信息
                    var result = regularItems.Concat(comboItems)
                        .OrderByDescending(x => x.CreateTime)
                        .Select(dto => new CommodityPublishedDto
                        {
                            // 主记录信息
                            Name = masterRecord.Name,
                            UserID = masterRecord.UserID,
                            Status = masterRecord.Status,
                            ActionType = masterRecord.ActionType,
                            PublishTime = masterRecord.PublishTime,
                            SyncTime = masterRecord.SyncTime,
                            AdjustmentId = masterRecord.AdjustmentID,
                            // 商品通用信息
                            Type = int.Parse(dto.Type),
                            FtNo = dto.FtNo,
                            CreationTime = dto.CreateTime,
                            MarketPrice = dto.MarketPrice,
                            SalePrice = dto.SalePrice,
                            PriceMode = dto.PriceMode,
                            ApplicableStores = dto.ApplicableStores,

                            // 普通商品特有字段
                            FdNo = dto.FdNo,
                            FdQty = dto.FdQty,
                            FdCName = dto.FdCName,

                            // 套餐商品特有字段
                            ComboNo = dto.ComboNo,
                            ComboName = dto.ComboName,
                            ComboItems = dto.PackageItems
                        })
                        .ToList();

                    // 6. 更新分页信息
                    context.Paging.Records = result.Count;

                    return result;
                }
            }
            catch (Exception ex)
            {
                // 记录详细日志
                throw new Exception($"已发布商品查询失败：{ex.Message}");
            }
        }
        /// <summary>
        /// 同步门店//拿到该用户的调整ID,状态为1已发布的全部数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>

        public async Task<CommSyncResultDto> CommSynchronousStore(CommSynchronousStoreContext context)
        {
            try
            {
                // 1. 解析并转换传入的 AdjustmentIDs
                var adjustmentGuids = context.AdjustmentIDs?
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => id.Trim())
                    .Where(id => !string.IsNullOrEmpty(id))
                    .Select(id =>
                    {
                        if (Guid.TryParse(id, out var guid))
                            return (Guid?)guid;
                        return null;
                    })
                    .Where(g => g.HasValue)
                    .Select(g => g.Value)
                    .Distinct()
                    .ToList() ?? new List<Guid>();

                if (!adjustmentGuids.Any())
                {
                    throw new ArgumentException("AdjustmentIDs 不能为空或格式不正确");
                }

                // 2. 验证所有传入的 AdjustmentID 都存在
                var allExistingRecords = (await _adjustmentListRepository.GetListAsync(w =>
                    w.UserID == context.UserId &&
                    adjustmentGuids.Contains(w.AdjustmentID) &&
                    !w.IsDeleted)).ToList();

                // 检查是否有不存在的 AdjustmentID
                var existingIds = allExistingRecords.Select(r => r.AdjustmentID).ToList();
                var missingIds = adjustmentGuids.Except(existingIds).ToList();

                if (missingIds.Any())
                {
                    throw new Exception($"以下 AdjustmentID 不存在或不属于当前用户: {string.Join(", ", missingIds)}");
                }

                // 3. 获取符合条件的待同步记录(状态为1)
                var validRecords = allExistingRecords
                    .Where(r => r.Status == 1 && r.Status != 2)
                    .ToList();
                if (!validRecords.Any())
                {
                    throw new Exception("未找到符合条件的待同步记录");
                }
                _adjustmentListRepository.BeginTran();
                // 3. 更新状态为已同步(2)并保存
                var syncedAdjustmentIds = new List<string>(); // 存储成功同步的AdjustmentID
                //调整主表
                var syncedAdjustments = new List<CommodityAdjustmentList>();
                foreach (var record in validRecords)
                {
                    record.Status = 2; // 更新为已同步状态
                    record.SyncTime = DateTime.Now;
                    await _adjustmentListRepository.UpdateAsync(record);
                    syncedAdjustments.Add(record);
                    syncedAdjustmentIds.Add(record.AdjustmentID.ToString()); // 记录成功同步的ID
                }

                // 4. 处理关联的调整详情记录
                var adjustmentIdsToProcess = validRecords.Select(r => r.AdjustmentID).ToList();
                var detailRecords = await _adjustmentDetailRepository.GetListAsync(d =>
                    adjustmentIdsToProcess.Contains(d.AdjustmentID));

                var syncedAdjustmentDetails = detailRecords.ToList();


                var comboDetailList = new List<GetDraftRecorListdDto>();

                var comboItems = await ProcessComboCommodities(
                syncedAdjustmentDetails.Where(d => d.Type == 2 && d.DetailID != Guid.Empty).ToList());

                // 6. 准备要保存到JSON的数据结构
                var resultData = new
                {
                    SyncTime = DateTime.Now,
                    SyncedAdjustments = syncedAdjustments,
                    SyncedAdjustmentDetails = syncedAdjustmentDetails,
                    comboDetailList = comboItems

                };

                //7.将数据保存为JSON文件
                var jsonContent = JsonSerializer.Serialize(resultData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                _adjustmentListRepository.CommitTran();
                // 8. 返回同步结果
                return new CommSyncResultDto
                {
                    SyncTime = DateTime.Now,
                    AdjustmentIds = string.Join(",", syncedAdjustmentIds), // 返回成功同步的AdjustmentID
                };

            }
            catch (Exception ex)
            {
                _adjustmentListRepository.RollbackTran();
                // 记录错误日志
                throw new Exception($"同步门店失败：{ex.Message}");

            }
        }



        /// <summary>
        /// 处理普通商品（Type=1）
        /// </summary>
        private async Task<List<GetDraftRecorListdDto>> ProcessRegularCommodities(List<CommodityAdjustmentDetail> details)
        {
            return details.Select(d => new GetDraftRecorListdDto
            {
                Type = d.Type.ToString(),
                FdNo = d.FdNo,
                FtNo = d.FtNo,
                FdCName = d.FdCName,
                FdQty = d.FdQty,
                DetailID = d.DetailID.ToString(),
                AdjustmentID = d.AdjustmentID.ToString(),
                MarketPrice = d.MarketPrice,
                CreateTime = d.CreateTime,
                SalePrice = d.SalePrice,
                PriceMode = d.PriceMode,
                ApplicableStores = d.ApplicableStores
            }).ToList();
        }

        /// <summary>
        /// 处理套餐商品（Type=2）
        /// </summary>
        private async Task<List<GetDraftRecorListdDto>> ProcessComboCommodities(List<CommodityAdjustmentDetail> comboDetails)
        {
            if (!comboDetails.Any()) return new List<GetDraftRecorListdDto>();

            // 1. 获取套餐包含的所有商品关系
            var detailIDs = comboDetails.Select(d => d.DetailID).ToList();
            var packageDetails = await _commodityAdjustmentPackageRepository.GetListAsync(p =>
                detailIDs.Contains(p.DetailID));

            // 2. 获取所有套餐商品编号
            var fdNos = packageDetails.Select(p => p.FdNo).Distinct().ToList();

            // 3. 查询商品详情 - 创建时间最早且Status==1的记录
            var commodityDetailsForPackages = await _adjustmentDetailRepository.GetListAsync(d =>
                fdNos.Contains(d.FdNo) && d.Status == 1);

            if (!commodityDetailsForPackages.Any())
            {
                throw new Exception($"商品查不到或未发布");
            }

            // 4. 按FdNo分组并获取每组中创建时间最早的记录
            var earliestCommodityDetails = commodityDetailsForPackages
                .GroupBy(d => d.FdNo)
                .Select(g => g.OrderByDescending(d => d.CreateTime).First())
                .ToDictionary(d => d.FdNo, d => d);

            // 5. 构建套餐商品DTO
            return comboDetails.Select(d => new GetDraftRecorListdDto
            {
                Type = d.Type.ToString(),
                ComboNo = d.ComboNo,
                ComboName = d.ComboName,
                FtNo = d.FtNo,
                DetailID = d.DetailID.ToString(),
                AdjustmentID = d.AdjustmentID.ToString(),
                MarketPrice = d.MarketPrice,
                SalePrice = d.SalePrice,
                PriceMode = d.PriceMode,
                CreateTime = d.CreateTime,
                ApplicableStores = d.ApplicableStores,
                PackageItems = ProcessPackageItems(packageDetails, earliestCommodityDetails, d.DetailID)
            }).ToList();
        }

        /// <summary>
        /// 处理套餐包含的商品项（合并相同FdNo并累加数量）
        /// </summary>
        private List<ComboPackageItemDto> ProcessPackageItems(
            List<CommodityAdjustmentPackage> packageDetails,
            Dictionary<string, CommodityAdjustmentDetail> commodityDetails,
            Guid currentDetailID)
        {
            return packageDetails
                .Where(p => p.DetailID == currentDetailID)
                .GroupBy(p => p.FdNo) // 按FdNo分组
                .Select(g =>
                {
                    // 获取该FdNo对应的商品详情
                    commodityDetails.TryGetValue(g.Key, out var detail);

                    return new ComboPackageItemDto
                    {
                        PGFdNo = g.Key,
                        PGFdCName = detail?.FdCName ?? string.Empty,
                        PGFdQty = g.Sum(p => detail?.FdQty), // 计算该FdNo的总数量
                        CommodityPrice = detail?.SalePrice.ToString() ?? string.Empty
                    };
                })
                .ToList();
        }

    }
}

