﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Entities.GroupBase
{
    public class NGrouponInfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        // 主键，唯一标识
        public Guid GrouponKey { get; set; }

        // 项目关联键
        public Guid ProjectKey { get; set; }

        // 团购名称，最多50个字符
        public string GrouponName { get; set; }

        // 商品原价
        public decimal GoodsPrice { get; set; }

        // 团购价
        public decimal GrouponPrice { get; set; }

        // 图片URL，最多100个字符
        public string ImgUrl { get; set; }

        // 团购周期描述，最多100个字符
        public string Period { get; set; }

        // 团购说明
        public string Explain { get; set; }

        // 可能是商品编号，允许为空
        public string FdNo { get; set; }

        // 卡模式编号
        public int CardModelNo { get; set; }

        // 是否为特价商品
        public bool IsSpecialOffer { get; set; }

        // 是否可用
        public bool IsUse { get; set; }

        // 是否删除
        public bool IsDel { get; set; }

        // 可用门店ID列表，最多100个字符
        public string UseShopIds { get; set; }

        // 可用区域编号
        public int UseAreaNo { get; set; }

        // 排序号
        public int SortNumber { get; set; }

        // 用户ID，最多100个字符
        public string UserId { get; set; }

        // 用户名，最多100个字符
        public string UserName { get; set; }

        // 录入时间
        public DateTime InputTime { get; set; }

        // 有效期模式编号
        public int ValidModelNo { get; set; }

        // 生效时间
        public DateTime Valid { get; set; }

        // 失效时间
        public DateTime ValidEnd { get; set; }

        // 销售模式编号
        public int SaleModelNo { get; set; }

        // 销售开始时间
        public DateTime SaleStartTime { get; set; }

        // 销售结束时间
        public DateTime SaleEndTime { get; set; }

        // 是否无效，默认0（false）
        public bool IsInvalid { get; set; } = false;

        // 编号数量，默认0
        public int CodeNumber { get; set; } = 0;

        // 最小销售数量，默认0
        public int CodeSaleMin { get; set; } = 0;

        // 最大销售数量，默认0
        public int CodeSaleMax { get; set; } = 0;

        // 预订天数，默认0
        public int BookDay { get; set; } = 0;

        // 是否为会员专享，默认0（false）
        public bool IsMember { get; set; } = false;

        // 单次消费限制，默认0
        public int SingleConsume { get; set; } = 0;

        // 禁用天数，默认0
        public int DisableDay { get; set; } = 0;

        // 卡标签编号，默认0
        public int CardLableNo { get; set; } = 0;

        // 构建编号，默认0
        public int BuildNo { get; set; } = 0;

        // 编号格式，最多100个字符
        public string CodeFormat { get; set; }

        // 可用区域字段，最多200个字符
        public string UseAreaField { get; set; }

        // 使用模式编号，默认0
        public int UseModelNo { get; set; } = 0;

        // 选择类型，默认0
        public int SelType { get; set; } = 0;
        public int IsGive { get; set; }
       
    }
}
