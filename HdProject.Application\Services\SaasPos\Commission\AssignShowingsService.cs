﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Runtime.ConstrainedExecution;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Dm;
using Furion.DependencyInjection;
using Furion.Templates;
using HdProject.Application.Services.Interfaces.SaasPos.Commission;
using HdProject.Application.Services.Interfaces.SaasPos.PlaceAnOrder;
using HdProject.Domain.Context.SaasPos.Commission.AssignEmployee;
using HdProject.Domain.Context.SaasPos.Commission.AssignRecommend;
using HdProject.Domain.Context.SaasPos.Commission.AssignShowings;
using HdProject.Domain.Context.SaasPos.PlaceAnOrder;
using HdProject.Domain.DTOs.SaasPos.Commission.AssignShowings;
using HdProject.Domain.Entities.SaasPos.Commission;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using SqlSugar;
using StackExchange.Redis;

namespace HdProject.Application.Services.SaasPos.Commission
{
    /// <summary>
    /// 指派员工
    /// </summary>
    public class AssignShowingsService : IAssignShowingsService
    {
        private readonly IRepositorySaas<CommissionAssignShowing> _repositorySaasAssignShowing;//指派看房
        private readonly IWebHostEnvironment _env;
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IConfiguration _configuration;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IMapper _mapper;
        public readonly IPlaceAnOrderService _placeAnOrderService;
        public AssignShowingsService(IRepositorySaas<CommissionAssignShowing> repositorySaasAssignShowing, IWebHostEnvironment env, ISqlSugarClient sqlSugarClient, IConfiguration configuration, IHttpClientFactory httpClientFactory, IMapper mapper, IPlaceAnOrderService placeAnOrderService)
        {
            _repositorySaasAssignShowing = repositorySaasAssignShowing;
            _env = env;
            _sqlSugarClient = sqlSugarClient;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _mapper = mapper;
            _placeAnOrderService = placeAnOrderService;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }



        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AssignShowingsAddResponseDto> AddAsync(AssignShowingsAddRequestDto requestDto)
        {
            AssignShowingsAddResponseDto responseDto = new AssignShowingsAddResponseDto();
            try
            {
                if (requestDto.Model.EmployeeCreateDtos.Count == 0)
                {
                    throw new Exception("无法新增，未选择员工！");
                }

                var cfModel = await ShouldWeReassign(
                    new AssignShowingsSWRRequestDto
                    {
                        AssignID = requestDto.Model.AssignID,
                        RmNo = requestDto.Model.RmNo,
                        BillNumber = requestDto.Model.BillNumber,
                        assignEmployeeCreateDtos = requestDto.Model.EmployeeCreateDtos
                    });
                if (cfModel.Iscf)
                {
                    responseDto.CfMessage = $"无法新增，员工【{cfModel.UserName}】已重复指派！";
                    responseDto.CfStatus = 580;
                    return responseDto;
                }


                //var JZModel = await ComLastOrderSettled(
                //    new AssignShowingsCLOSRequestDto
                //    {
                //        ShopID = requestDto.Model.ShopID,
                //        BillNumber = requestDto.Model.BillNumber,
                //        assignEmployeeCreateDtos = requestDto.Model.EmployeeCreateDtos
                //    });

                //if (JZModel.IsWJz)//查询专员上间服务房间是否已结账
                //{
                //    responseDto.CfMessage = $"无法新增，员工【{JZModel.UserName}】，上次服务订单【{JZModel.BillNumber}】未结账！";
                //    responseDto.CfStatus = 580;
                //    return responseDto;
                //}

                var result = await _db.Ado.UseTranAsync(async () =>
                {

                    var caddata = await _db.Queryable<CommissionAssignData>()
                    .Where(a =>
                    a.ShopID == requestDto.Model.ShopID
                    && a.RmNo == requestDto.Model.RmNo
                    && a.BillNumber == requestDto.Model.BillNumber
                    )
                    .FirstAsync();

                    if (caddata == null)//限制重复将信息插入主表
                    {
                        var commissionAssignData = new CommissionAssignData()
                        {
                            ShopID = requestDto.Model.ShopID,
                            RmNo = requestDto.Model.RmNo,
                            BillNumber = requestDto.Model.BillNumber,
                            BookingAgentID = requestDto.Model.BookingAgentID,
                            BookingAgentName = requestDto.Model.BookingAgentName,
                            OperatorID = requestDto.Model.OperatorID,
                            OperatorName = requestDto.Model.OperatorName,
                            //CreationDate = DateTime.Now,
                        };

                        await _db.Insertable(commissionAssignData).ExecuteCommandAsync();//新增看房指派主表
                    }

                    var commissionAssign = new CommissionAssignShowing()
                    {
                        RmNo = requestDto.Model.RmNo,
                        BillNumber = requestDto.Model.BillNumber,
                        OperatorID = requestDto.Model.OperatorID,
                        OperatorName = requestDto.Model.OperatorName,
                        CreatedBy = requestDto.Model.OperatorName,
                        CreatedTime = DateTime.Now,
                        ShopID = requestDto.Model.ShopID,
                        BookingAgentID = requestDto.Model.BookingAgentID,
                        BookingAgentName = requestDto.Model.BookingAgentName,
                    };
                    var caID = await _db.Insertable(commissionAssign).ExecuteReturnIdentityAsync();//首先新增看房指派，并返回其自增列ID

                    var cae = new List<CommissionAssignEmployee>();

                    foreach (var eclist in requestDto.Model.EmployeeCreateDtos)
                    {
                        cae.Add(new CommissionAssignEmployee()
                        {
                            AssignID = caID,
                            EmployeeID = eclist.EmployeeID,
                            EmployeeName = eclist.EmployeeName,
                            IsCom = eclist.IsUnviewedProperty == true ? false : eclist.IsCom,
                            IsUnviewedProperty = eclist.IsUnviewedProperty,
                        });
                    }
                    await _db.Insertable(cae).ExecuteCommandAsync();//新增关联表，返回成功数

                    var car = new List<CommissionAssignRecommend>();
                    foreach (var rclist in requestDto.Model.recommendCreateDtos)
                    {
                        car.Add(new CommissionAssignRecommend()
                        {
                            AssignID = caID,
                            ReferenceID = rclist.ReferenceID,
                            ReferenceName = rclist.ReferenceName,
                            RecommendedID = rclist.RecommendedID,
                            RecommendedName = rclist.RecommendedName,
                        });
                    }
                    await _db.Insertable(car).ExecuteCommandAsync();//新增推荐关联表，返回成功数

                });

                if (result.IsSuccess)
                {
                    double c = 300;
                    if (requestDto.PlaceAnOrdeUser.ShopId.Trim() == "11") //名堂的专员费为400
                    {
                        c = 400;
                    }

                    var parRequest = new PlaceAnOrderRequestDto()
                    {
                        ShopId = requestDto.PlaceAnOrdeUser.ShopId,
                        RmNo = requestDto.Model.RmNo,
                        CashUserId = requestDto.PlaceAnOrdeUser.FdUid,
                        InputUserId = requestDto.PlaceAnOrdeUser.FdUid,
                        CashType = "N",
                        FdNo = "0814",
                        Qty = requestDto.Model.EmployeeCreateDtos.Where(e => e.IsUnviewedProperty != true).Count(),//只查询入房的指派专员
                        Price = c
                    };
                    var parResult = await _placeAnOrderService.GetOrderByOperation(parRequest);
                    if (parResult)
                    {
                        responseDto.Index = 1;
                    }
                    else
                    {
                        responseDto.Index = 0;
                    }
                    // responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }

            return responseDto;
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AssignShowingsDeleteResponseDto> DeleteAsync(AssignShowingsDeleteRequestDto requestDto)
        {
            AssignShowingsDeleteResponseDto responseDto = new AssignShowingsDeleteResponseDto();
            try
            {
                var resultModel = await _repositorySaasAssignShowing.GetFirstAsync(a => a.AssignID == requestDto.AssignID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法删除，请检查该看房记录是否存在！");
                }
                resultModel.IsActive = true;
                resultModel.DisabledBy = resultModel.CreatedBy;
                resultModel.DisabledTime = DateTime.Now;
                var result = await _repositorySaasAssignShowing.UpdateAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 根据房间ID查询看房信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<AssignShowingsGetAllResponseDto> GetAllAsync(AssignShowingsGetAllRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 根据ID查询，需要关联其他实体信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<AssignShowingsDto> GetAssociationBdDetailsAsync(int id)
        {
            var assignShowing = await _db.Queryable<CommissionAssignShowing>()
                .Where(c => c.AssignID == id && c.IsActive == false)
                .FirstAsync();//查询指派看房信息
            if (assignShowing == null)
            {
                return null;
            }
            var assignEmployeeModelList = await _db.Queryable<CommissionAssignEmployee>()
            .Where(c => c.AssignID == assignShowing.AssignID).Select(a => new AssignEmployeeDto
            {
                AEID = a.AEID,
                AssignID = a.AssignID,
                EmployeeID = a.EmployeeID,
                EmployeeName = a.EmployeeName,
                IsCom = a.IsCom,
                IsUnviewedProperty = a.IsUnviewedProperty

            }).ToListAsync();//查询指派员工信息

            var assignRecommendModelList = await _db.Queryable<CommissionAssignRecommend>()
            .Where(c => c.AssignID == assignShowing.AssignID).Select(a => new AssignRecommendDto
            {
                ARID = a.ARID,
                AssignID = a.AssignID,
                ReferenceID = a.ReferenceID,
                ReferenceName = a.ReferenceName,
                RecommendedID = a.RecommendedID,
                RecommendedName = a.RecommendedName,
            }).ToListAsync();//查询指派员工信息

            return new AssignShowingsDto
            {
                AssignID = assignShowing.AssignID,
                RmNo = assignShowing.RmNo,
                BillNumber = assignShowing.BillNumber,
                OperatorID = assignShowing.OperatorID,
                OperatorName = assignShowing.OperatorName,
                CreatedBy = assignShowing.CreatedBy,
                CreatedTime = assignShowing.CreatedTime,
                assignEmployeeList = assignEmployeeModelList,
                assignRecommends = assignRecommendModelList,
            };
        }
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AssignShowingsGetByIdResponseDto> GetByIdAsync(AssignShowingsGetByIdRequestDto requestDto)
        {
            AssignShowingsGetByIdResponseDto responseDto = new AssignShowingsGetByIdResponseDto();
            try
            {
                var result = await GetAssociationBdDetailsAsync(requestDto.AssignID);
                if (result != null)
                {
                    responseDto.Model = result;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 根据房间ID查询看房信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AssignShowingsGetByRoomResponseDto> GetByRoomAsync(AssignShowingsGetByRoomRequestDto requestDto)
        {
            AssignShowingsGetByRoomResponseDto responseDto = new AssignShowingsGetByRoomResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<CommissionAssignShowing>(true);
                predicate = predicate.And(it => it.ShopID.Equals(requestDto.ShopID) && it.BillNumber.Equals(requestDto.BillNo) && it.IsActive == false);//过滤数据
                var result = await GetByRmNoTolistAsync(predicate);
                //var result = await GetUserOpenIDTolistAsync(requestDto, predicate);
                var model = _mapper.Map<List<AssignShowingsDto>>(result);//使用AutoMapper进行对象属性映射
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 根据房间ID查询看房信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<CommissionAssignShowing>> GetByRmNoTolistAsync(Expression<Func<CommissionAssignShowing, bool>> whereExpression = null)
        {
            var commissionAssignslist = await _db.Queryable<CommissionAssignShowing>().Where(whereExpression).OrderByDescending(st => st.AssignID).ToListAsync();
            if (commissionAssignslist.Count == 0)
            {
                return null;
            }
            foreach (var commission in commissionAssignslist)
            {
                var commissionAssignsEmployee = await _db.Queryable<CommissionAssignEmployee>().Where(a => a.AssignID == commission.AssignID).OrderByDescending(b => b.IsCom).ToListAsync();
                var commissionAssignRecommends = await _db.Queryable<CommissionAssignRecommend>().Where(a => a.AssignID == commission.AssignID).ToListAsync();
                commission.assignEmployeeList = commissionAssignsEmployee;
                commission.assignRecommends = commissionAssignRecommends;
            }
            return commissionAssignslist;
        }

        public async Task<AssignShowingsUpdateResponseDto> UpdateAsync(AssignShowingsUpdateRequestDto requestDto)
        {
            AssignShowingsUpdateResponseDto responseDto = new AssignShowingsUpdateResponseDto();
            try
            {
                if (requestDto.Model.EmployeeCreateDtos.Count == 0)
                {
                    throw new Exception("无法修改，未绑定指派员工！");
                }
                var cfModel = await ShouldWeReassign(
                    new AssignShowingsSWRRequestDto
                    {
                        AssignID = requestDto.Model.AssignID,
                        RmNo = requestDto.Model.RmNo,
                        BillNumber = requestDto.Model.BillNumber,
                        assignEmployeeCreateDtos = requestDto.Model.EmployeeCreateDtos
                    });
                if (cfModel.Iscf)
                {
                    responseDto.CfMessage = $"无法修改，员工【{cfModel.UserName}】已重复指派！";
                    responseDto.CfStatus = 580;
                    return responseDto;
                }

                //var JZModel = await ComLastOrderSettled(
                //    new AssignShowingsCLOSRequestDto
                //    {
                //        ShopID = requestDto.Model.ShopID,
                //        BillNumber = requestDto.Model.BillNumber,
                //        assignEmployeeCreateDtos = requestDto.Model.EmployeeCreateDtos
                //    });

                //if (JZModel.IsWJz)//查询专员上间服务房间是否已结账
                //{
                //    responseDto.CfMessage = $"无法修改，员工【{JZModel.UserName}】，上次服务订单【{JZModel.BillNumber}】未结账！";
                //    responseDto.CfStatus = 580;
                //    return responseDto;
                //}

                var cas = await _db.Queryable<CommissionAssignShowing>().Where(a => a.AssignID == requestDto.Model.AssignID && a.IsActive == false).FirstAsync();
                if (cas == null)
                {
                    throw new Exception("无法修改，请检查该看房记录是否存在！");
                }

                //查询指派下面的员工数量
                var employeeData = await _db.Queryable<CommissionAssignEmployee>().Where(e => e.AssignID == requestDto.Model.AssignID && e.IsUnviewedProperty != true).ToListAsync();//查询已保存的看房专员数量
                var EmployeeCount = requestDto.Model.EmployeeCreateDtos.Where(e => e.IsUnviewedProperty != true).Count();//只过滤看房专员数量
                //判断修改是不是增加人数
                if (EmployeeCount > employeeData.Count)
                {
                    if (!string.IsNullOrEmpty(cas.BookingAgentID))
                    {
                        // 找出新增的员工
                        List<AssignEmployeeCreateDto> addedEmployees = requestDto.Model.EmployeeCreateDtos
                            .Where(newEmp => newEmp.IsUnviewedProperty != true && !employeeData.Any(oldEmp => oldEmp.EmployeeID == newEmp.EmployeeID))
                            .ToList();

                        var aes = addedEmployees.Where(ae => ae.EmployeeID == cas.BookingAgentID).FirstOrDefault();//判断修改增加的员工是否为代订人
                        if (aes != null)
                        {
                            responseDto.CfMessage = $"修改后，指派员工【{aes.EmployeeName}】将为自订自看，该操作存在错误！你需要新增单据指派该员工，或者禁用当前指派单！";
                            responseDto.CfStatus = 580;
                            return responseDto;
                        }
                    }

                }

                int del = 0;//成功行数
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    cas.ModifiedBy = cas.CreatedBy;
                    cas.ModifiedTime = DateTime.Now;
                    var casIndex = await _db.Updateable(cas).ExecuteCommandAsync();//修改看房

                    del = await _db.Deleteable<CommissionAssignEmployee>().AS("[Commission_AssignEmployee]").Where("AssignID=@AssignID", new { AssignID = requestDto.Model.AssignID }).ExecuteCommandAsync();

                    var caeList = new List<CommissionAssignEmployee>();

                    foreach (var assignEmployeeList in requestDto.Model.EmployeeCreateDtos)
                    {
                        caeList.Add(new CommissionAssignEmployee()
                        {
                            AssignID = assignEmployeeList.AssignID,
                            EmployeeID = assignEmployeeList.EmployeeID,
                            EmployeeName = assignEmployeeList.EmployeeName,
                            IsCom = assignEmployeeList.IsUnviewedProperty == true ? false : assignEmployeeList.IsCom,
                            IsUnviewedProperty = assignEmployeeList.IsUnviewedProperty
                        });

                    }
                    await _db.Insertable(caeList).ExecuteCommandAsync();//批量新增看房关联员工表信息

                    var delr = await _db.Deleteable<CommissionAssignRecommend>().AS("[Commission_AssignRecommend]").Where("AssignID=@AssignID", new { AssignID = requestDto.Model.AssignID }).ExecuteCommandAsync();

                    var car = new List<CommissionAssignRecommend>();

                    foreach (var rclist in requestDto.Model.recommendCreateDtos)
                    {
                        car.Add(new CommissionAssignRecommend()
                        {
                            AssignID = rclist.AssignID,
                            ReferenceID = rclist.ReferenceID,
                            ReferenceName = rclist.ReferenceName,
                            RecommendedID = rclist.RecommendedID,
                            RecommendedName = rclist.RecommendedName,
                        });
                    }
                    await _db.Insertable(car).ExecuteCommandAsync();//新增推荐关联表，返回成功数

                });

                if (result.IsSuccess)
                {
                    //responseDto.Index = 1;
                    double c = 300;
                    if (requestDto.PlaceAnOrdeUser.ShopId.Trim() == "11") //名堂的专员费为400
                    {
                        c = 400;
                    }
                    if (EmployeeCount > employeeData.Count())//如果传入的指派人员数大于修改前的人数，则需要插入差异数。
                    {
                        if (requestDto.PlaceAnOrdeUser != null)
                        {
                            var parRequest = new PlaceAnOrderRequestDto()
                            {
                                ShopId = requestDto.PlaceAnOrdeUser.ShopId,
                                RmNo = requestDto.Model.RmNo,
                                CashUserId = requestDto.PlaceAnOrdeUser.FdUid,
                                InputUserId = requestDto.PlaceAnOrdeUser.FdUid,
                                CashType = "N",
                                FdNo = "0814",
                                Qty = EmployeeCount - employeeData.Count(),//传入差异数
                                Price = c
                            };

                            var parResult = await _placeAnOrderService.GetOrderByOperation(parRequest);
                            if (parResult)
                            {
                                responseDto.Index = 1;
                            }
                            else
                            {
                                responseDto.Index = 0;
                            }
                            // responseDto.Index = 1;
                        }
                        else
                        {
                            responseDto.Index = 1;
                        }
                    }
                    else
                    {
                        responseDto.Index = 1;
                    }

                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 传入员工列表判断当前员工是否重复派房
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AssignShowingsSWRResponseDto> ShouldWeReassign(AssignShowingsSWRRequestDto requestDto)
        {
            AssignShowingsSWRResponseDto responseDto = new AssignShowingsSWRResponseDto();
            foreach (var item in requestDto.assignEmployeeCreateDtos)
            {
                string sql = "select CASE WHEN COUNT(*)>0 THEN 'true' else 'false' END AS IsCz from Commission_AssignShowings A" +
                    " LEFT JOIN Commission_AssignEmployee B ON A.AssignID = B.AssignID " +
                    " WHERE A.IsActive <> 1 AND A.AssignID <> @AssignID AND A.RmNo = @RmNo and A.BillNumber = @BillNumber and B.EmployeeID = @EmployeeID ";//SQL
                object pm = new { AssignID = requestDto.AssignID, RmNo = requestDto.RmNo, BillNumber = requestDto.BillNumber, EmployeeID = item.EmployeeID };//条件参数
                var result = await _db.Ado.SqlQuerySingleAsync<AssignShowingsDto>(sql, pm);
                if (Convert.ToBoolean(result.IsCz.ToString()))
                {
                    responseDto.Iscf = true;
                    responseDto.UserName = item.EmployeeName;
                    break;
                }
            }
            return responseDto;

        }
        /// <summary>
        /// 查询当前专员上次（最后一次）订单的结账状态
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AssignShowingsCLOSResponseDto> ComLastOrderSettled(AssignShowingsCLOSRequestDto requestDto)
        {
            AssignShowingsCLOSResponseDto responseDto = new AssignShowingsCLOSResponseDto();
            foreach (var item in requestDto.assignEmployeeCreateDtos)
            {
                string sql = " select ZP.ShopID,ZP.BillNumber," +
                    "(select CASE  WHEN(A.ComeDate<> null or A.ComeDate<> '')" +
                    " AND(f.AccDate<> null or f.AccDate<> '')" +
                    " THEN 'true' ELSE 'false' END AS IsJz  from cloudRms2019.rms2019.dbo.opencacheinfo A" +
                    " LEFT JOIN LinkedServerOD.OperateData.dbo.fdinv f ON A.ShopID = F.ShopID AND A.Invno = F.Invno" +
                    " WHERE A.ShopID = ZP.ShopID AND A.Invno = ZP.BillNumber) AS IsJz" +
                    " from (select TOP 1 A.ShopID, A.BillNumber from Commission_AssignShowings A" +
                    " LEFT JOIN Commission_AssignEmployee B ON A.AssignID = B.AssignID" +
                    " WHERE A.IsActive<> 1 AND A.AssignID<> @AssignID AND B.EmployeeID = @EmployeeID" +
                    " ORDER BY A.CreatedTime DESC) AS ZP";//SQL
                object pm = new { AssignID = requestDto.AssignID, EmployeeID = item.EmployeeID };//条件参数
                var result = await _db.Ado.SqlQuerySingleAsync<AssignShowingsDto>(sql, pm);
                if (result.ShopID != 0)
                {
                    if (!string.IsNullOrEmpty(result.IsJz))
                    {
                        if (!Convert.ToBoolean(result.IsJz.ToString()))
                        {
                            responseDto.IsWJz = true;
                            responseDto.UserName = item.EmployeeName;
                            responseDto.BillNumber = result.BillNumber;
                            break;
                        }
                    }
                }

            }
            return responseDto;
        }
    }
}
