﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos
{
    public partial class BookLeaderCommission
    {
        [SugarColumn(IsPrimaryKey = true)]
        public System.Guid CommissionId { get; set; }
        public System.Guid LeaderId { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal WithdrawnAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal AvailableAmount { get; set; }
        public decimal FrozenAmount { get; set; }
        public decimal WithdrawPendingAmount { get; set; }
        public bool IsDeleted { get; set; }
        public Nullable<System.DateTime> CreateTime { get; set; }
        public Nullable<System.DateTime> UpdateTime { get; set; }
        public Nullable<System.DateTime> DeleteTime { get; set; }
        public Nullable<System.Guid> DeletedBy { get; set; }

      
    }
}
