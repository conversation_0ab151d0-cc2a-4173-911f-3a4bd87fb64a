﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.MainFood.Room
{
    /// <summary>
    /// 房间配送
    /// </summary>
    [SugarTable("RtAuto")]
    public class RtAuto
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int IKey { get; set; }

        [SugarColumn(Length = 2)]
        public string RtNo { get; set; }

        [SugarColumn(Length = 5)]
        public string FdNo { get; set; }

        public short FdQty { get; set; }

        [SugarColumn(Length = 1)]
        public string CashType { get; set; }

        [SugarColumn(Length = 4)]
        public string CashUserId { get; set; }

        [SugarColumn(ColumnName = "msrepl_tran_version")]
        public Guid MsreplTranVersion { get; set; }
    }
}
