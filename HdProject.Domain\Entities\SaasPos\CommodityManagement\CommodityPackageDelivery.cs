﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.CommodityManagement
{
    // 套餐配送表（CommodityPackageDelivery）
   
    public partial class CommodityPackageDelivery
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public string ComboNo { get; set; }
        public string FtNo { get; set; }
        public string ComboName { get; set; }
        public string FdNo { get; set; }
      
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public bool IsDeleted { get; set; }
       
    }
}
