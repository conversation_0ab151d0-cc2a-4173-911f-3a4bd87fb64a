﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIFile;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplate;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplateDetails;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicITemplate;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Entities.SongBase.WallPainting;
using HdProject.Domain.Interfaces;
using LinqKit;
using SqlSugar;

namespace HdProject.Application.Services.SongBase.WallPainting
{
    /// <summary>
    /// 主题模板
    /// </summary>
    public class WPTopicITemplateService : IWPTopicITemplateService
    {
        private readonly IRepositorySongBase<WPTopicITemplate> _repositorySongBaseTemplate;//主题模板
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IMapper _mapper;
        public WPTopicITemplateService(IRepositorySongBase<WPTopicITemplate> repositorySongBaseTemplate, ISqlSugarClient sqlSugarClient, IMapper mapper)
        {
            _repositorySongBaseTemplate = repositorySongBaseTemplate;
            _sqlSugarClient = sqlSugarClient;
            _mapper = mapper;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("SongBase");
            }
        }
        public Task<WPTopicITemplateAddResponseDto> AddAsync(WPTopicITemplateAddRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicITemplateDeleteResponseDto> DeleteAsync(WPTopicITemplateDeleteRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 查询（无分页）
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WPTopicITemplateGetAllResponseDto> GetAll()
        {
            WPTopicITemplateGetAllResponseDto responseDto = new WPTopicITemplateGetAllResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<WPTopicITemplate>(true);
                predicate = predicate.And(it => it.IsActive == false);//过滤数据
                var result = await _repositorySongBaseTemplate.GetListAsync(predicate);
                var model = _mapper.Map<List<WPTopicITemplateDto>>(result);//使用AutoMapper进行对象属性映射
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WPTopicITemplateGetAllResponseDto> GetAllAsync(WPTopicITemplateGetAllRequestDto requestDto)
        {
            WPTopicITemplateGetAllResponseDto responseDto = new WPTopicITemplateGetAllResponseDto();
            try
            {
                Expression<Func<WPTopicITemplate, bool>> predicate = null;
                predicate = predicate.And(it => it.IsActive == false);//过滤数据
                var result = await _repositorySongBaseTemplate.GetPageListAsync(requestDto, predicate);
                var model = _mapper.Map<List<WPTopicITemplateDto>>(result);//使用AutoMapper进行对象属性映射
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 根据ID查询主题模板信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>

        public async Task<WPTopicITemplateGetByIdResponseDto> GetByIdAsync(WPTopicITemplateGetByIdRequestDto requestDto)
        {
            WPTopicITemplateGetByIdResponseDto responseDto = new WPTopicITemplateGetByIdResponseDto();
            try
            {
                var result = await GetAssociationBdDetailsAsync(requestDto.TemplateID);
                if (result != null)
                {
                    responseDto.Model = result;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 根据ID查询，需要关联其他实体信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<WPTopicITemplateDto> GetAssociationBdDetailsAsync(int id)
        {
            var template = await _db.Queryable<WPTopicITemplate>()
                .Where(c => c.TemplateID == id && c.IsActive == false)
                .FirstAsync();//查询主题模板
            if (template == null)
            {
                return null;
            }
            var fileModel = await _db.Queryable<WPTopicIFile>()
            .Where(c => c.FileID == template.FileID && c.IsActive == false).Select(a => new WPTopicIFileDto
            {
                FileID = a.FileID,
                FileName = a.FileName,
                FilePath = a.FilePath,
                FormatType = a.FormatType,
                FileSize = a.FileSize,
                Width = a.Width,
                Height = a.Height,
                ThumbnailPath = a.ThumbnailPath
            }).FirstAsync();//查询主题模板关联素材表

            var DetailsModel = await _db.Queryable<WPTopicITemplateDetails>()
            .Where(c => c.TemplateID == template.TemplateID).Select(a => new WPTopicITemplateDetailsDto
            {
                DetailsID = a.DetailsID,
                TemplateID = a.TemplateID,
                Proportion = a.Proportion,
                StartX = a.StartX,
                StartY = a.StartY,
                Width = a.Width,
                Height = a.Height,
                Sequence = a.Sequence
            }).ToListAsync();//查询主题模板关联明细

            return new WPTopicITemplateDto
            {
                TemplateID = template.TemplateID,
                TemplateName = template.TemplateName,
                TSupportFileNum = template.TSupportFileNum,
                FileID = template.FileID,
                Details = DetailsModel,
                topicFile = fileModel
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<WPTopicITemplateUpdateResponseDto> UpdateAsync(WPTopicITemplateUpdateRequestDto requestDto)
        {
            throw new NotImplementedException();
        }
    }
}
