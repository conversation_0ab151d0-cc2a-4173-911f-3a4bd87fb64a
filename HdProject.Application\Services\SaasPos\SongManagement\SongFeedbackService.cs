﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Furion.DatabaseAccessor;
using HdProject.Application.Services.Interfaces.SaasPos.SongManagement;
using HdProject.Domain.Context.SaasPos.SongFeedback;
using HdProject.Domain.DTOs.SaasPos.SongFeedback;
using HdProject.Domain.Entities.SaasPos.SongManagement;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.SaasPos.SongManagement
{
    /// <summary>
    /// 缺歌登记接口实现类
    /// </summary>
    public class SongFeedbackService : ISongFeedbackService
    {
        private readonly IRepositorySaas<SongFeedback> _repositorySaas;

        public SongFeedbackService(IRepositorySaas<SongFeedback> repositorySaas)
        {
            _repositorySaas = repositorySaas;
        }
        /// <summary>
        /// 新增缺歌登记信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<SongFeedbackAddResponseDto> AddAsync(SongFeedbackAddRequestDto requestDto)
        {
            SongFeedbackAddResponseDto responseDto = new SongFeedbackAddResponseDto();
            try
            {
                var model = new SongFeedback
                {
                    SongName = requestDto.Model.SongName,
                    SingerName = requestDto.Model.SingerName,
                    SongVersion = requestDto.Model.SongVersion,
                    CreateTime = DateTime.Now,
                    Remark = requestDto.Model.Remark,
                    ContactWay = requestDto.Model.ContactWay,
                    FeedbackByID = requestDto.Model.FeedbackByID
                };
                var result = await _repositorySaas.InsertAsync(model);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {

                throw new Exception("新增失败:" + ex.Message);
            }
            return responseDto;
        }
    }
}
