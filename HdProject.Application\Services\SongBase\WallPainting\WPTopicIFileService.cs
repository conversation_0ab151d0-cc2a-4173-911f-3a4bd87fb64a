﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Furion.DatabaseAccessor;
using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIFile;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicIFile;
using HdProject.Domain.Entities.SongBase.WallPainting;
using HdProject.Domain.Interfaces;
using SqlSugar;
using StackExchange.Redis;

namespace HdProject.Application.Services.SongBase.WallPainting
{
    /// <summary>
    /// 主题素材
    /// </summary>
    public class WPTopicIFileService : IWPTopicIFileService
    {
        private readonly IRepositorySongBase<WPTopicIFile> _repositorySaasTopicIFile;//主题素材
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IMapper _mapper;
        public WPTopicIFileService(IRepositorySongBase<WPTopicIFile> repositorySaasTopicIFile, ISqlSugarClient sqlSugarClient, IMapper mapper)
        {
            _repositorySaasTopicIFile = repositorySaasTopicIFile;
            _sqlSugarClient = sqlSugarClient;
            _mapper = mapper;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("SongBase");
            }
        }
        /// <summary>
        /// 保存素材信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WPTopicIFileAddResponseDto> AddAsync(WPTopicIFileAddRequestDto requestDto)
        {
            WPTopicIFileAddResponseDto responseDto = new WPTopicIFileAddResponseDto();
            // 保存到数据库
            var model = new WPTopicIFile()
            {
                FileName = requestDto.Model.FileName,
                FilePath = requestDto.Model.FilePath,
                FormatType = requestDto.Model.FormatType,
                FileSize = requestDto.Model.FileSize,
                UploadedBy = "",
                UploadedTime = DateTime.Now,
                Width = requestDto.Model.Width,
                Height = requestDto.Model.Height,
                ThumbnailPath = requestDto.Model.ThumbnailPath,
            };
            try
            {
                int fid = await _db.Insertable(model).ExecuteReturnIdentityAsync();
                responseDto.FileID = fid;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        public Task<WPTopicIFileDeleteResponseDto> DeleteAsync(WPTopicIFileDeleteRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicIFileGetAllResponseDto> GetAllAsync(WPTopicIFileGetAllRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicIFileGetByIdResponseDto> GetByIdAsync(WPTopicIFileGetByIdRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicIFileUpdateResponseDto> UpdateAsync(WPTopicIFileUpdateRequestDto requestDto)
        {
            throw new NotImplementedException();
        }
    }
}
