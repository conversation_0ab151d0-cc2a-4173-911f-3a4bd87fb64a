﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.MainFood.Room
{
    /// <summary>
    /// 房间管理业务实体类
    /// </summary>
    [SugarTable("Room")]
    public class Room
    {
        [SugarColumn(IsPrimaryKey = true, Length = 4)]
        public string RmNo { get; set; }

        [SugarColumn(Length = 50)]
        public string? RmName { get; set; }

        [SugarColumn(Length = 2)]
        public string RtNo { get; set; }

        [SugarColumn(Length = 1)]
        public string AreaNo { get; set; }

        [SugarColumn(Length = 9)]
        public string? InvNo { get; set; }

        [SugarColumn(Length = 2)]
        public string PriceNo { get; set; }

        [SugarColumn(Length = 8)]
        public string? WorkDate { get; set; }

        [SugarColumn(Length = 4)]
        public string? RsPos { get; set; }

        [SugarColumn(Length = 1)]
        public string RmStatus { get; set; }

        public bool IsSDate { get; set; }

        [SugarColumn(Length = 50)]
        public string? Rem { get; set; }

        [SugarColumn(Length = 8)]
        public string? BookDate { get; set; }

        [SugarColumn(Length = 5)]
        public string? BookTime { get; set; }

        [SugarColumn(Length = 8)]
        public string? InDate { get; set; }

        [SugarColumn(Length = 5)]
        public string? InTime { get; set; }

        public short? InNumbers { get; set; }

        [SugarColumn(Length = 4)]
        public string? OpenUserId { get; set; }

        [SugarColumn(Length = 4)]
        public string? AccUserId { get; set; }

        [SugarColumn(Length = 8)]
        public string? AccDate { get; set; }

        [SugarColumn(Length = 5)]
        public string? AccTime { get; set; }

        [SugarColumn(Length = 4)]
        public string? ContinueUserId { get; set; }

        [SugarColumn(Length = 13)]
        public string? ContinueTime { get; set; }

        [SugarColumn(Length = 7)]
        public string? MemberNo { get; set; }

        [SugarColumn(Length = 10)]
        public string? CustName { get; set; }

        [SugarColumn(Length = 4)]
        public string? OrderUserId { get; set; }

        public int DiscRate { get; set; }
        public int? Serv { get; set; }
        public int? FdCost { get; set; }
        public int? RmCost { get; set; }
        public int? Disc { get; set; }
        public int? ZD { get; set; }
        public int? BeerZD { get; set; }
        public int? BeerCash { get; set; }
        public int? Tax { get; set; }
        public int MorePayed { get; set; }
        public int? Tot { get; set; }
        public bool WC { get; set; }
        public bool Dance { get; set; }

        [SugarColumn(Length = 1)]
        public string PrnFIndex { get; set; }

        [SugarColumn(Length = 1)]
        public string PrnDIndex { get; set; }

        public short? PInvCount { get; set; }

        [SugarColumn(Length = 4)]
        public string? FromRmNo { get; set; }

        public short? OpenCount { get; set; }
        public bool ForceNoServ { get; set; }
        public int? Tag { get; set; }
        public int FixedDisc { get; set; }

        [SugarColumn(Length = 15)]
        public string? CarId { get; set; }

        public int? FdCost_InRmCost { get; set; }
        public int? FdCost_NotInRmCost { get; set; }
        public bool MembDisc { get; set; }
        public int? MembCard { get; set; }

        [SugarColumn(ColumnName = "Card_MNo", Length = 10)]
        public string? CardMNo { get; set; }

        public int? CardAmount { get; set; }

        [SugarColumn(Length = 16)]
        public string? CloseTime { get; set; }

        public bool CallAccount { get; set; }

        [SugarColumn(Length = 30)]
        public string? BadReason { get; set; }

        [SugarColumn(Length = 4)]
        public string? BadUserId { get; set; }

        public int AutoZD { get; set; }

        [SugarColumn(ColumnName = "rowguid")]
        public Guid Rowguid { get; set; }

        [SugarColumn(ColumnName = "msrepl_tran_version", IndexGroupNameList = new[] { "index_100195407" })]
        public Guid MsreplTranVersion { get; set; }
    }
}
