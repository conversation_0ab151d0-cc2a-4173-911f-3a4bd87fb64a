﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.SongSyncCenter
{
    //歌曲扩展信息表
    public partial class SongExtendedInfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public int SongId { get; set; }
        public string CopyrightOwner { get; set; }
        public string LicenseExpiryDate { get; set; }
      
        public int PlayCount { get; set; }
      
        public int TotalPlayTime { get; set; }
        public DateTime? LastPlayTime { get; set; }
        public decimal CopyrightFee { get; set; }
        public string DeleteReason { get; set; }
       
        public bool IsDeleted { get; set; }
    }
}
