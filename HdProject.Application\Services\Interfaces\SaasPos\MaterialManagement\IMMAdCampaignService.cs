﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMAdCampaign;

namespace HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement
{
    /// <summary>
    /// 节目投放业务接口类
    /// </summary>
    public interface IMMAdCampaignService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMAdCampaignGetByIdAsyncResponseDto> GetByIdAsync(MMAdCampaignGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 根据投放任务ID查询投放详情信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMAdCampaignGetCampaignDetailsAsyncResponseDto> GetCampaignDetails(MMAdCampaignGetCampaignDetailsAsyncRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<MMAdCampaignGetAllAsyncResponseDto> GetAllAsync(MMAdCampaignGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMAdCampaignAddResponseDto> AddAsync(MMAdCampaignAddRequestDto requestDto);
        /// <summary>
        /// 修改投放设备信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMAdCampaignUpdateLaunchDeviceResponseDto> UpdateLaunchDeviceAsyns(MMAdCampaignUpdateLaunchDeviceRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMAdCampaignUpdateResponseDto> UpdateAsync(MMAdCampaignUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMAdCampaignDeleteResponseDto> DeleteAsync(MMAdCampaignDeleteRequestDto requestDto);
    }
}
