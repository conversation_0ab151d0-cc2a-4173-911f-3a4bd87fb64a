﻿using HdProject.Common.Logging;
using HdProject.Domain.Result.Page;
using HdProject.Domain.WebApi;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using System.Security.Claims;

namespace HdProject.Web.Core
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ControllerApiBase : ControllerBase
    {
        #region Response Methods
        [NonAction]
        public IActionResult ApiSuccess(string message = "操作成功")
        {
            LogService.Info(message);
            
            string methodName = new System.Diagnostics.StackTrace().GetFrame(1)?.GetMethod()?.Name ?? "Unknown";
            LogService.LogApiCall(methodName, null, new { State = ResultType.success, Message = message }, 0);
            
            return Ok(new
            {
                state = ResultType.success,
                message
            });
        }

        [NonAction]
        public IActionResult ApiError(string message = "操作失败", int statusCode = 400)
        {
            LogService.Warn(message);
            
            // 记录详细的API调用信息
            string methodName = new System.Diagnostics.StackTrace().GetFrame(1)?.GetMethod()?.Name ?? "Unknown";
            LogService.LogApiCall(methodName, null, new { State = ResultType.error, Message = message, StatusCode = statusCode }, 0);
            
            return StatusCode(statusCode, new
            {
                state = statusCode,
                message
            });
        }

        [NonAction]
        public IActionResult ApiData<T>(T data, string message = "操作成功")
        {
            LogService.Info(message);
            
            // 记录详细的API调用信息
            string methodName = new System.Diagnostics.StackTrace().GetFrame(1)?.GetMethod()?.Name ?? "Unknown";
            LogService.LogApiCall(methodName, null, new { State = ResultType.success, Message = message, Data = data }, 0);
            
            return Ok(new
            {
                state = ResultType.success,
                message,
                data
            });
        }

        [NonAction]
        public IActionResult ApiPaged<T>(IEnumerable<T> list, Pagination paging)
        {
            // 只记录分页的基本信息，不记录数据内容
            string message = $"获取分页数据成功: 页码={paging.Page}, 页大小={paging.Rows}, 总记录数={paging.Records}";
            LogService.Info(message);
            
            // 仅记录分页的元数据，不记录数据内容
            string methodName = new System.Diagnostics.StackTrace().GetFrame(1)?.GetMethod()?.Name ?? "Unknown";
            LogService.LogApiCall(methodName, null, 
                new { 
                    State = ResultType.success, 
                    RecordCount = list?.Count() ?? 0,
                    PageInfo = new { 
                        Page = paging.Page, 
                        PageSize = paging.Rows, 
                        TotalRecords = paging.Records,
                        TotalPages = paging.Total
                    }
                }, 0);
            
            return Ok(new
            {
                state = ResultType.success,
                data = new
                {
                    list,
                    paging
                }
            });
        }

        #endregion

        #region User Helpers
        /// <summary>
        /// 获取当前登录用户ID
        /// </summary>
        protected int GetUserId()
        {
            var userIdClaim = User?.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return 0;
        }

        /// <summary>
        /// 获取当前登录用户名
        /// </summary>
        protected string GetUserName()
        {
            return User?.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty;
        }

        /// <summary>
        /// 检查用户是否拥有指定角色
        /// </summary>
        protected bool UserHasRole(string roleName)
        {
            return User?.IsInRole(roleName) ?? false;
        }

        /// <summary>
        /// 检查当前用户是否为管理员
        /// </summary>
        protected bool IsAdmin()
        {
            return UserHasRole("admin");
        }
        #endregion
    }

    /// <summary>
    /// 对外匿名访问接口基类
    /// </summary>
    [ApiController]
    [AllowAnonymous]
    public class PublicControllerBase : ControllerApiBase
    {
        // 继承所有ApiBase的方法，但允许匿名访问
    }

    /// <summary>
    /// 仅限管理员的API基类
    /// </summary>
    [Authorize(Roles = "admin")]
    public class AdminControllerBase : ControllerApiBase
    {
        // 仅限管理员访问
    }
}
