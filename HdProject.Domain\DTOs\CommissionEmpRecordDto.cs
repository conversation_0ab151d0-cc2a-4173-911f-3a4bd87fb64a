﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.DTOs
{
    public class GetOrderEmpRecordModel
    {
        public int OrderId { get; set; }

        public string OrderNo { get; set; }

        public string PayOrderNo { get; set; }

        public int OrderState { get; set; }

        public string OrderStateName { get; set; }

        public string UserId { get; set; }

        public decimal ComAmount { get; set; }

        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 门店ID
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        public string ShopName { get; set; }

        /// <summary>
        /// 预定日期
        /// </summary>
        public string ComeDate { get; set; }

        /// <summary>
        /// 预定时间
        /// </summary>
        public string ComeTime { get; set; }

        /// <summary>
        /// 房型编号
        /// </summary>
        public string RtNo { get; set; }

        /// <summary>
        /// 房型名称
        /// </summary>
        public string RtName { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public int PayAmount { get; set; }

        /// <summary>
        /// 套餐名称
        /// </summary>
        public string SpuName { get; set; }

        /// <summary>
        /// 房号
        /// </summary>
        public string RmNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string InvNo { get; set; }
    }

    public class GetPersonCommissionPosterModel
    {
        public byte[] Bytes { get; set; }
    }

    public class ExcelEmpRecordModel
    {

    }
}
