﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;
using HdProject.Domain.Entities.MainFood.Room;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.MainFood
{
    /// <summary>
    /// 房间区域服务接口实现类
    /// </summary>
    public class RmAreaService : IRmAreaService
    {
        private readonly IRepositoryMainFood<RmArea> _repositoryMainFood;
        private readonly IMapper _mapper;
        public RmAreaService(IRepositoryMainFood<RmArea> repositoryMainFood, IMapper mapper)
        {
            _repositoryMainFood = repositoryMainFood;
            _mapper = mapper;
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RmAreaAddResponseDto> AddAsync(RmAreaAddRequestDto requestDto)
        {
            try
            {
                RmAreaAddResponseDto responseDto = new RmAreaAddResponseDto();
                //var resultModel = _mapper.Map<RmArea>(requestDto.Model);//使用AutoMapper进行对象属性映射
                //resultModel.Rowguid = Guid.NewGuid(); //插入时自动生成新的Guid
                // resultModel.MsreplTranVersion = Guid.NewGuid();
                var resultModel = new RmArea()
                {
                    AreaNo = requestDto.Model.AreaNo,
                    AreaName = requestDto.Model.AreaName,
                    MsreplTranVersion = Guid.NewGuid()
                };
                //调用新增方法
                var result = await _repositoryMainFood.InsertAsync(resultModel);
                //responseDto.IsResult = result > 0 ? true : false;
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("新增操作失败！");
            }
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RmAreaDeleteResponseDto> DeleteAsync(RmAreaDeleteRequestDto requestDto)
        {
            try
            {
                RmAreaDeleteResponseDto responseDto = new RmAreaDeleteResponseDto();
                var result = await _repositoryMainFood.DeleteAsync(it => it.AreaNo.Equals(requestDto.RmAreaID));
                //responseDto.IsResult = result > 0 ? true : false;
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("删除操作失败！");
            }
        }
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<RmAreaGetAllAsyncResponseDto> GetAllAsync(RmAreaGetAllAsyncRequestDto requestDto)
        {
            RmAreaGetAllAsyncResponseDto responseDto = new RmAreaGetAllAsyncResponseDto();
            //var resultModel = await _repositoryMainFood.GetListAsync(it => it.InvNo.Contains(requestDto.QueryCriteria));
            //调用查询方法
            var result = await _repositoryMainFood.GetPageListAsync(requestDto, it => it.AreaName.Contains(requestDto.QueryCriteria));
            //var model = _mapper.Map<List<RmAreaDto>>(result);//使用AutoMapper进行对象属性映射
            var model = result.Select(r => new RmAreaDto
            {
                AreaNo = r.AreaNo,
                AreaName = r.AreaName,
            }).ToList();
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RmAreaGetByIdAsyncResponseDto> GetByIdAsync(RmAreaGetByIdAsyncRequestDto requestDto)
        {
            RmAreaGetByIdAsyncResponseDto responseDto = new RmAreaGetByIdAsyncResponseDto();
            //调用查询方法
            var result = await _repositoryMainFood.GetByIdAsync(requestDto.AreaNo);
            if (result == null) throw new Exception("查询信息失败！");
            //var model = _mapper.Map<RmAreaDto>(result);//使用AutoMapper进行对象属性映射
            RmAreaDto model = new RmAreaDto()
            {
                AreaNo = result.AreaNo,
                AreaName = result.AreaName,
            };
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RmAreaUpdateResponseDto> UpdateAsync(RmAreaUpdateRequestDto requestDto)
        {
            try
            {
                RmAreaUpdateResponseDto responseDto = new RmAreaUpdateResponseDto();
                //var resultModel = _mapper.Map<RmArea>(requestDto.Model);//使用AutoMapper进行对象属性映射
                var resultModel = new RmArea()
                {
                    AreaNo = requestDto.Model.AreaNo,
                    AreaName = requestDto.Model.AreaName
                };
                var existing = await _repositoryMainFood.GetFirstAsync(r => r.AreaNo == resultModel.AreaNo);
                // 保留原有rowguid
                //resultModel.Rowguid = existing.Rowguid;
                resultModel.MsreplTranVersion = existing.MsreplTranVersion;
                var result = await _repositoryMainFood.UpdateAsync(resultModel);
                //responseDto.IsResult = result > 0 ? true : false;
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("修改操作失败！");
            }
        }
    }
}
