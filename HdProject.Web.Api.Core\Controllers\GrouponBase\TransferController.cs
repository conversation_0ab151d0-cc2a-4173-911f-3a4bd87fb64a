﻿using HdProject.Application.Services.Interfaces.GrouponBase;
using HdProject.Domain.Context.GrouponBase;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Security.Cryptography.Xml;
using static HdProject.Domain.Context.RMS.RefundContext;

namespace HdProject.Web.Api.Core.Controllers.GrouponBase
{
    /// <summary>
    /// 卡卷转增
    /// </summary>
    [Route("[controller]/[Action]")]
    public class TransferController : PublicControllerBase
    {
        private readonly ITransferService _transferService;

        public TransferController(ITransferService transferService)
            => _transferService = transferService;

        /// <summary>
        /// 可分享卡卷列表查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetSharedCardList([FromQuery] GetTransferCardContext context)
        {
            var res = await _transferService.GetSharedCardList(context);
            return ApiPaged(res, context.Paging);
        }
        /// <summary>
        /// 转赠卡卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> TransferCardRecord(TransferCardContext context)
        {

            var res = await _transferService.TransferCardRecord(context);
            if (res?.Any(x => x?.Msg == "该券已被领取过，不能再分享") == true)
            {
                return ApiError(message: "该券已被领取过，不能再分享", statusCode: 509);
            }
            else if (res?.Any(x => x?.Msg == "该券不支持转赠") == true)
            {
                return ApiError(message: "该券不支持转赠", statusCode: 510);
            }
            else if (res?.Any(x => x?.Msg == "该券状态不支持转赠") == true)
            {
                return ApiError(message: "该券状态不支持转赠", statusCode: 510);
            }
            else if (res?.Any(x => x?.Msg == "未找到相关券信息") == true)
            {
                return ApiError(message: "未找到相关券信息", statusCode: 510);
            }

            return ApiData(res);
            



        }
        /// <summary>
        /// 领取卡卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ClaimRecord(ClaimContext context)
        {
            var res = await _transferService.ClaimRecord(context);
            return ApiData(res);
        }
        /// <summary>
        /// 根据ClaimKey查询已转增相关卡卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetClaimKeyList([FromQuery] GetClaimKeyListContext context)
        {
            var res = await _transferService.GetClaimKeyList(context);

            return ApiData(res);
        }
        /// <summary>
        /// 已分享卡卷列表查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetNoSharedCardList([FromQuery] GetNoSharedCardListContext context)
        {
            var res = await _transferService.GetNoSharedCardList(context);
            return ApiPaged(res, context.Paging);
        }
    }
}
