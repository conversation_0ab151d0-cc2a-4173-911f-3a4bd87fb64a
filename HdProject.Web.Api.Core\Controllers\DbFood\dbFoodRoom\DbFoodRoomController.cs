﻿using HdProject.Application.Services.Interfaces.DbFood.DbFoodRoom;
using HdProject.Domain.Context.DbFood.Room;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.DbFood.dbFoodRoom
{
    /// <summary>
    /// 包间消费人数统计控制器接口
    /// </summary>
    public class DbFoodRoomController : PublicControllerBase
    {
        private readonly IDbFoodRoomService _dbFoodRoomService;
        private readonly ILogger<DbFoodRoomController> _logger;
        public DbFoodRoomController(IDbFoodRoomService dbFoodRoomService, ILogger<DbFoodRoomController> logger)
        {
            _dbFoodRoomService = dbFoodRoomService;
            _logger = logger;
        }

        /// <summary>
        /// 查询房间消费人数全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllDbFoodRoom([FromQuery] DbFoodRoomGetAllRequestDto request)
        {
            var result = await _dbFoodRoomService.GetAllAsync(request);
            return ApiData(result.Model.list);
        }


        /// <summary>
        /// 查询门店区域
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRegionAll")]
        public async Task<IActionResult> GetAllRegionAsync([FromQuery] DbFoodRoomRegionGetAllRequestDto request)
        {
            var result = await _dbFoodRoomService.GetAllRegionAsync(request);
            return ApiData(result.Model);
        }

        
    }
}
