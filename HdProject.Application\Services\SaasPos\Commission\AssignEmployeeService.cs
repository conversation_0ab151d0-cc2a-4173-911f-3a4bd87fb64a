﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SaasPos.Commission;
using HdProject.Domain.Context.SaasPos.Commission.AssignEmployee;
using HdProject.Domain.DTOs.SaasPos.Commission.AssignEmployee;
using HdProject.Domain.Interfaces;
using Newtonsoft.Json;
using SqlSugar;

namespace HdProject.Application.Services.SaasPos.Commission
{
    /// <summary>
    /// 指派看房
    /// </summary>
    public class AssignEmployeeService : IAssignEmployeeService
    {
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IHttpClientFactory _httpClientFactory;
        public AssignEmployeeService(ISqlSugarClient sqlSugarClient, IHttpClientFactory httpClientFactory)
        {
            _sqlSugarClient = sqlSugarClient;
            _httpClientFactory = httpClientFactory;
        }
        public Task<AssignEmployeeAddResponseDto> AddAsync(AssignEmployeeAddRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<AssignEmployeeDeleteResponseDto> DeleteAsync(AssignEmployeeDeleteRequestDto requestDto)
        {
            throw new NotImplementedException();
        }
        /// <summary>
        /// 查询员工
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<AssignEmployeeGetAllResponseDto> GetAllAsync(AssignEmployeeGetAllRequestDto requestDto)
        {
            AssignEmployeeGetAllResponseDto responseDto = new AssignEmployeeGetAllResponseDto();
            try
            {
                var url = $"http://hr.tang-hui.com.cn/services/getuser_simplelist.php?id=1000011";
                var client = _httpClientFactory.CreateClient();
                client.BaseAddress = new Uri(url);
                var response = await client.GetAsync(url);
                var model = new AssignEmployeeDto();
                if (response.IsSuccessStatusCode)
                {
                    var result = response.Content.ReadAsStringAsync();
                    model = JsonConvert.DeserializeObject<AssignEmployeeDto>(result.Result);
                }
                else
                {
                    throw new Exception("员工信息获取失败！");
                }
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        public Task<AssignEmployeeGetByIdResponseDto> GetByIdAsync(AssignEmployeeGetByIdRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<AssignEmployeeUpdateResponseDto> UpdateAsync(AssignEmployeeUpdateRequestDto requestDto)
        {
            throw new NotImplementedException();
        }
    }
}
