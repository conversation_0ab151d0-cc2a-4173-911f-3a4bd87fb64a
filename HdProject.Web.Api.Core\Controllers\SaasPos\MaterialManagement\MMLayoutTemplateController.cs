﻿
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Application.Services.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout;
using HdProject.Domain.WebApi;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement
{
    /// <summary>
    /// 布局模版接口控制器
    /// </summary>
    public class MMLayoutTemplateController : PublicControllerBase
    {
        private readonly IMMLayoutTemplateService _mMLayoutTemplateService;
        private readonly ILogger<MMLayoutTemplateController> _logger;
        public MMLayoutTemplateController(IMMLayoutTemplateService mMLayoutTemplateService, ILogger<MMLayoutTemplateController> logger)
        {
            _mMLayoutTemplateService = mMLayoutTemplateService;
            _logger = logger;
        }

        /// <summary>
        /// 查询布局模版全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async  Task<IActionResult> GetAllMMLayoutTemplate([FromQuery] MMLayoutTemplateGetAllRequestDto request)
        {
            var result = await _mMLayoutTemplateService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询布局模版的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async  Task<IActionResult> GetByIdMMLayoutTemplate([FromQuery] MMLayoutTemplateGetByIdRequestDto request)
        {
            var result = await _mMLayoutTemplateService.GetByIdAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 新增布局模版信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> MMLayoutTemplateAddValue([FromBody] MMLayoutTemplateAddRequestDto request)
        {
            var result = await _mMLayoutTemplateService.AddAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 修改布局模版信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async  Task<IActionResult> MMLayoutTemplateUpdateValue([FromBody] MMLayoutTemplateUpdateRequestDto request)
        {
            var result = await _mMLayoutTemplateService.UpdateAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 删除布局模版信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async  Task<IActionResult> MMLayoutTemplateDeletedValue([FromBody] MMLayoutTemplateDeleteRequestDto request)
        {
            var result = await _mMLayoutTemplateService.DeleteAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }
    }
}
