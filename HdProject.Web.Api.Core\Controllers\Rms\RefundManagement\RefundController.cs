﻿using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Domain.Context;
using HdProject.Domain.Context.RMS;
using HdProject.Domain.Context.RMS.SummaryStoreTimeSlotDailyList;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using static HdProject.Domain.Context.RMS.RefundContext;

namespace HdProject.Web.Api.Core.Controllers.Rms.RefundManagement
{
    [Route("[controller]/[Action]")]
    public class RefundController : PublicControllerBase
    {
        private readonly IRefundService _refundService;

        public RefundController(IRefundService refundService)
            => _refundService = refundService;
        /// <summary>
        /// 卡卷查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetRefundRecord([FromQuery] CardContext context)
        {
            var res = await _refundService.GetRefundRecord(context);
            return ApiPaged(res ,context.Paging);

        }
        /// <summary>
        /// 申请退款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ApplyRefundRecord(ApplyRefundContext context)
        {
            var res = await _refundService.ApplyRefundRecord(context);
            return ApiData(res);
        }
        /// <summary>
        /// 申请退款列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> ApplyRefundListRecord([FromQuery] ApplyRefundListContext context)
        {
            var res = await _refundService.ApplyRefundListRecord(context);
            return ApiData(res);

        }
        /// <summary>
        /// 退款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> RefundRecord(ConfirmRefundContext context)
        {
            var res = await _refundService.RefundRecord(context);
            return ApiData(res);
        }
    }
}
