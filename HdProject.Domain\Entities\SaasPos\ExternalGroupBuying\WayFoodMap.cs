﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.ExternalGroupBuying
{
    /// <summary>
    /// 外部团购表
    /// </summary>
    [SugarTable("Way_FoodMap")]
    public class WayFoodMap
    {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public string FdNo { get; set; }
        public string ThirdFdNo { get; set; }
        public short Platform { get; set; }
        public int StoreId { get; set; }
        public int Qty { get; set; }
        public bool? IsGiftMember { get; set; }

        public int? MemberCardLevel { get; set; }

        public string RedirectUrl { get; set; }
        public string GrouponKeys { get; set; }

        /// <summary>
        /// 门店
        /// </summary>
        /// 
        [Navigate(NavigateType.OneToOne, nameof(StoreId))]//一对一
        public GnrStore? store { get; set; }
    }
}
