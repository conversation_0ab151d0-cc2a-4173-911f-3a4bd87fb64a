﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.ExternalGroupBuying;
using HdProject.Domain.DTOs.SaasPos.ExternalGroupBuying;

namespace HdProject.Application.Services.Interfaces.SaasPos.ExternalGroupBuying
{
    public interface IWayFoodMapService
    {
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<WayFoodMapGetAllResponseDto> GetAllAsync(WayFoodMapGetAllRequestDto requestDto);
        /// <summary>
        /// 查询全部门店信息
        /// </summary>
        /// <returns></returns>
        Task<GnrStoreGetAllResponseDto> GetStoreAllAsync();
        /// <summary>
        /// 单行新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WayFoodMapAddResponseDto> AddAsync(WayFoodMapAddRequestDto requestDto);
        /// <summary>
        //修改
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WayFoodMapUpdateResponseDto> UpdateAsync(WayFoodMapUpdateRequestDto requestDto);
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WayFoodMapDeleteResponseDto> DeleteAsync(WayFoodMapDeleteRequestDto requestDto);
        /// <summary>
        /// 单条删除
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WayFoodMapDeleteResponseDto> SingleItemDeleteAsync(WayFoodMapSingleItemDeleteRequestDto requestDto);
        /// <summary>
        /// 批量导入新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WayFoodMapImportResponseDto> ImportAsync(WayFoodMapImportRequestDto requestDto);
    }
}
