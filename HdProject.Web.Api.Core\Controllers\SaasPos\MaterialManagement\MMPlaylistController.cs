﻿using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Application.Services.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist;
using HdProject.Domain.WebApi;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement
{
    /// <summary>
    /// 节目管理接口控制器
    /// </summary>
    public class MMPlaylistController : PublicControllerBase
    {
        private readonly IMMPlaylistService _mMPlaylistService;
        private readonly ILogger<MMPlaylistController> _logger;
        public MMPlaylistController(IMMPlaylistService mMPlaylistService, ILogger<MMPlaylistController> logger)
        {
            _mMPlaylistService = mMPlaylistService;
            _logger = logger;
        }

        /// <summary>
        /// 查询节目全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllMMPlaylist([FromQuery] MMPlaylistGetAllAsyncRequestDto request)
        {
            var result = await _mMPlaylistService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询节目单的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdMMPlaylist([FromQuery] MMPlaylistGetByIdAsyncRequestDto request)
        {
            var result = await _mMPlaylistService.GetByIdAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 新增节目信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> MMPlaylistAddValue([FromBody] MMPlaylistAddRequestDto request)
        {
            var result = await _mMPlaylistService.AddAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 修改节目信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> MMPlaylistUpdateValue([FromBody] MMPlaylistUpdateRequestDto request)
        {
            var result = await _mMPlaylistService.UpdateAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 删除节目信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> MMPlaylistDeletedValue([FromBody] MMPlaylistDeleteRequestDto request)
        {
            var result = await _mMPlaylistService.DeleteAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }
    }
}
