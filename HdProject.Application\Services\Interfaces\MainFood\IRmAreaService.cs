﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;

namespace HdProject.Application.Services.Interfaces.MainFood
{
    /// <summary>
    /// 房间区域业务接口类
    /// </summary>
    public interface IRmAreaService
    {
        /// <summary>
        /// 根据ID查询房间区域信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RmAreaGetByIdAsyncResponseDto> GetByIdAsync(RmAreaGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 查询房间区域全部信息
        /// </summary>
        /// <returns></returns>
        Task<RmAreaGetAllAsyncResponseDto> GetAllAsync(RmAreaGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 新增房间区域信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RmAreaAddResponseDto> AddAsync(RmAreaAddRequestDto requestDto);
        /// <summary>
        /// 修改房间区域信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RmAreaUpdateResponseDto> UpdateAsync(RmAreaUpdateRequestDto requestDto);
        /// <summary>
        /// 删除房间区域信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RmAreaDeleteResponseDto> DeleteAsync(RmAreaDeleteRequestDto requestDto);
    }
}
