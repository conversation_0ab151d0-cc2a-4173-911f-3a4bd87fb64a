﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplateDetails;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicITemplateDetails;

namespace HdProject.Application.Services.Interfaces.SongBase.WallPainting
{
    /// <summary>
    /// 模板明细接口
    /// </summary>
    public interface IWPTopicITemplateDetailsService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicITemplateDetailsGetByIdResponseDto> GetByIdAsync(WPTopicITemplateDetailsGetByIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<WPTopicITemplateDetailsGetAllResponseDto> GetAllAsync(WPTopicITemplateDetailsGetAllRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicITemplateDetailsAddResponseDto> AddAsync(WPTopicITemplateDetailsAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicITemplateDetailsUpdateResponseDto> UpdateAsync(WPTopicITemplateDetailsUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicITemplateDetailsDeleteResponseDto> DeleteAsync(WPTopicITemplateDetailsDeleteRequestDto requestDto);
    }
}
