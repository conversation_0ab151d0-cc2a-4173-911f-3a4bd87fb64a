﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.Rms
{
    public class bookhistory
    {
        /// <summary>
        /// 主键（GUID）
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)] // 标识主键
        public string Ikey { get; set; }

        /// <summary>
        /// 预约单号
        /// </summary>
        public string BookNo { get; set; }

        /// <summary>
        /// 店铺ID
        /// </summary>
        public int ShopId { get; set; } = 0; // 默认为0

        /// <summary>
        /// 客户主键（可选）
        /// </summary>
        public string CustKey { get; set; }

        /// <summary>
        /// 客户姓名
        /// </summary>
        public string CustName { get; set; }

        /// <summary>
        /// 客户电话
        /// </summary>
        public string CustTel { get; set; }

        /// <summary>
        /// 到店日期（格式：YYYY-MM-DD）
        /// </summary>
        public string ComeDate { get; set; }

        /// <summary>
        /// 到店时间（格式：HH:mm:ss）
        /// </summary>
        public string ComeTime { get; set; }

        /// <summary>
        /// 开始时段标识
        /// </summary>
        public string Beg_Key { get; set; }

        /// <summary>
        /// 开始时段名称
        /// </summary>
        public string Beg_Name { get; set; }

        /// <summary>
        /// 结束时段标识（长度调整为50）
        /// </summary>
        public string End_Key { get; set; }

        /// <summary>
        /// 结束时段名称
        /// </summary>
        public string End_Name { get; set; }

        /// <summary>
        /// 人数
        /// </summary>
        public int Numbers { get; set; }

        /// <summary>
        /// 房间类型编号（长度调整为50）
        /// </summary>
        public string Rtno { get; set; }

        /// <summary>
        /// 房间类型名称
        /// </summary>
        public string RtName { get; set; }

        /// <summary>
        /// 消费类型编号
        /// </summary>
        public int CtNo { get; set; } = 0; // 默认为0

        /// <summary>
        /// 消费类型名称（varchar类型）
        /// </summary>
        public string CtName { get; set; }

        /// <summary>
        /// 支付方式编号
        /// </summary>
        public int PtNo { get; set; } = 0; // 默认为0

        /// <summary>
        /// 支付方式名称
        /// </summary>
        public string PtName { get; set; }

        /// <summary>
        /// 预约备注
        /// </summary>
        public string BookMemory { get; set; }

        /// <summary>
        /// 预约状态（0=未确认，1=已确认）
        /// </summary>
        public int BookStatus { get; set; } = 0; // 默认为0

        /// <summary>
        /// 签到状态（0=未签到，1=已签到）
        /// </summary>
        public int CheckinStatus { get; set; } = 0; // 默认为0

        /// <summary>
        /// 预约店铺ID
        /// </summary>
        public int BookShopId { get; set; } = 0; // 默认为0

        /// <summary>
        /// 预约人ID
        /// </summary>
        public string BookUserId { get; set; }

        /// <summary>
        /// 预约人姓名
        /// </summary>
        public string BookUserName { get; set; }

        /// <summary>
        /// 预约时间（DateTime类型）
        /// </summary>
        public DateTime BookDateTime { get; set; }

        /// <summary>
        /// 数值字段1
        /// </summary>
        public int Val1 { get; set; } = 0; // 默认为0

        /// <summary>
        /// 软删除标识（0=未删除，1=已删除）
        /// </summary>
        public bool IsDelete { get; set; } = false; // 默认为false

        /// <summary>
        /// 删除人姓名
        /// </summary>
        public string DelUserName { get; set; }

        /// <summary>
        /// 最后修改店铺ID
        /// </summary>
        public int EditShopID { get; set; } = 0; // 默认为0

        /// <summary>
        /// 最后修改人ID
        /// </summary>
        public string EditUserID { get; set; }

        /// <summary>
        /// 最后修改人姓名
        /// </summary>
        public string EditUserName { get; set; }

        /// <summary>
        /// 最后修改时间（字符串格式）
        /// </summary>
        public string EditDateTime { get; set; }

        /// <summary>
        /// 下单人ID
        /// </summary>
        public string OrderUserID { get; set; }

        /// <summary>
        /// 下单人姓名
        /// </summary>
        public string OrderUserName { get; set; }

        /// <summary>
        /// 需求人数
        /// </summary>
        public int DemandNumber { get; set; } = 0; // 默认为0

        /// <summary>
        /// 微信OpenID
        /// </summary>
        public string OpenId { get; set; }

        /// <summary>
        /// 押金总额
        /// </summary>
        public int DepositTot { get; set; } = 0; // 默认为0

        /// <summary>
        /// 房间号（长度调整为50）
        /// </summary>
        public string RmNo { get; set; }
    }
}
