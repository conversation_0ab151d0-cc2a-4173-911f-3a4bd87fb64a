﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Furion.DependencyInjection;
using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Application.Services.Interfaces.SaasPos.CommodityManagement;
using HdProject.Domain.Context.CommodityManagement;
using HdProject.Domain.Context.RMS.SummaryStoreTimeSlotDailyList;
using HdProject.Domain.DTOs.CommodityManagement;
using HdProject.Domain.DTOs.RMS.SummaryStoreTimeSlotDailyList;
using HdProject.Domain.Entities.Rms;
using HdProject.Domain.Interfaces;
using static System.Runtime.InteropServices.JavaScript.JSType;

//using SummaryStoreTimeSlotDaily = HdProject.Domain.Context.RMS.SummaryStoreTimeSlotDaily;


namespace HdProject.Application.Services.Rms
{

    public class SummaryStoreTimeSlotDailyService : ISummaryStoreTimeSlotDailyService
    {
        //IRepositorySaas
        private readonly IRepositoryRms<SummaryStoreTimeSlotDaily> _summaryStoreTimeSlotDailyRepository;
        private readonly IRepositoryRms2019<bookcacheinfo> _bookcacheinfoRepository;
        private readonly IRepositoryRms2019<opencacheinfo> _opencacheinfoRepository;
        private readonly IRepositoryRms2019<shoptimeinfo> _shoptimeinfoRepository;
        private readonly IRepositoryRms2019<timeinfo> _timeinfoRepository;
        private readonly IRepositoryRms2019<shopinfo> _shopinfoRepository;
        private readonly IRepositoryRms2019<pricemodel> _pricemodelRepository;
        private readonly IRepositoryRms2019<workconfig> _workconfigRepository;
        private readonly IRepositoryRms2019<openhistory> _openhistoryRepository;
        private readonly IRepositoryRms2019<bookhistory> _bookhistoryRepository;

        private List<workconfig> _cachedWorkConfigs;
        private List<shopinfo> _cachedShopInfos;
        private List<pricemodel> _cachedPriceModels;
        private List<shoptimeinfo> _cachedShopTimeInfos;
        private List<timeinfo> _cachedTimeInfos;

        public SummaryStoreTimeSlotDailyService(IRepositoryRms<SummaryStoreTimeSlotDaily> summaryStoreTimeSlotDailyRepository,
            IRepositoryRms2019<bookcacheinfo> bookcacheinfoRepository,
            IRepositoryRms2019<opencacheinfo> opencacheinfoRepository,
            IRepositoryRms2019<shoptimeinfo> shoptimeinfoRepository,
            IRepositoryRms2019<timeinfo> timeinfoRepository,
            IRepositoryRms2019<shopinfo> shopinfoRepository,
            IRepositoryRms2019<pricemodel> pricemodelRepository,
            IRepositoryRms2019<workconfig> workconfigRepository,
            IRepositoryRms2019<openhistory> openhistoryRepository,
            IRepositoryRms2019<bookhistory> bookhistoryRepository



           )
        {
            _summaryStoreTimeSlotDailyRepository = summaryStoreTimeSlotDailyRepository;//预约时段统计表
            _bookcacheinfoRepository = bookcacheinfoRepository;
            _opencacheinfoRepository = opencacheinfoRepository;
            _shoptimeinfoRepository = shoptimeinfoRepository;
            _timeinfoRepository = timeinfoRepository;
            _shopinfoRepository = shopinfoRepository;
            _pricemodelRepository = pricemodelRepository;
            _workconfigRepository = workconfigRepository;
            _openhistoryRepository = openhistoryRepository;
            _bookhistoryRepository = bookhistoryRepository;


        }



        //获取预约时段统计
        public async Task<List<SummaryStoreTimeSlotDailyDto>> GetSummaryStoreTimeSlotDailyRecord(SummaryStoreTimeSlotDailyContext context)
        {
            try
            {
                Expression<Func<SummaryStoreTimeSlotDaily, bool>> listExpression = null;

                if (context.StoreID != null && context.StartTime != null && context.EndTime != null)
                {
                    listExpression = w =>
                   w.StoreID == context.StoreID &&
                   w.StatistDate >= context.StartTime &&
                   w.StatistDate <= context.EndTime;
                }
                else
                {
                    listExpression = x => true;

                }

                var ReservationTimeSlotLists = await _summaryStoreTimeSlotDailyRepository.GetPageListAsync(
                    context.Paging,
                    listExpression);

                if (!ReservationTimeSlotLists.Any())
                {
                    return new List<SummaryStoreTimeSlotDailyDto>();
                }

                var result = ReservationTimeSlotLists.Select(a => new SummaryStoreTimeSlotDailyDto
                {
                    Store = a.StoreID,
                    Date = a.StatistDate,
                    Week = a.Week,
                    TimeSegment = a.Period,
                    ReserveNum = a.ReservatCount,
                    CancelNum = a.CancelCount,
                    WalkinNum = a.StraightToCount,
                    DirectNum = a.StraightSetCount,
                    CheckinNum = a.RoomCount
                }).ToList();
                //context.Paging.Records = result.Count();
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception("获取预约时段详情失败，请稍后重试", ex);
            }
        }
        //统计预约时段数据
        public async Task<bool> GetSummaryStoreTimeSlotDailyListRecord(SummaryStoreTimeSlotDailyListContext context)
        {
            _summaryStoreTimeSlotDailyRepository.BeginTran();
            try
            {
                // 日期转为周几
                DateTime date = DateTime.ParseExact(context.Comedate, "yyyyMMdd", CultureInfo.InvariantCulture);
                string dayOfWeekChinese = date.ToString("dddd", new CultureInfo("zh-CN"));
                // 获取所有预约表订单
                var allBbookcacheinfoOrders = (await _bookcacheinfoRepository.GetListAsync(
                    b =>
                         b.ComeDate == context.Comedate && b.IsDelete == false
                         )) // 按日期和未删除过滤
                    .ToList();
                // 获取所有开房表订单
                var allOpencacheinfoOrders = (await _opencacheinfoRepository.GetListAsync(
                    b =>
                         b.ComeDate == context.Comedate
                         )) // 按日期过滤
                    .ToList();



                // 获取所有门店和时段组合（已按ShopId和Beg_Key分组排序）
                //var data = await _bookcacheinfoRepository.GetListAsync(b => b.IsDelete == false);
                var shopBegKeys = allBbookcacheinfoOrders
                    .GroupBy(b => new { b.ShopId, b.Beg_Key, b.Beg_Name })
                    .OrderBy(g => g.Key.ShopId)
                    .ThenBy(g => g.Key.Beg_Key)
                    .Select(g => new
                    {
                        ShopID = g.Key.ShopId,
                        BegKey = g.Key.Beg_Key,
                        BegName = g.Key.Beg_Name,
                        Date = context.Comedate
                    })
                    .ToList();

                //----——————————————————————————————————直落部分
                var timeList = await GetBookByNoDelunion1(context.Comedate);
                // 获取所有直落订单（Beg_Key != End_Key）
                //var allStraightThroughOrders = timeList
                //    .Where(o => o.Beg_Key != o.End_Key && o.BookStatus == 0 && o.IsDelete == false)
                //    .ToList();
                var allStraightThroughOrders = timeList
                    .Where(o => o.Beg_Key != o.End_Key && o.BookStatus == 0)
                    .ToList();

                var straightThroughCountDict = new Dictionary<(int ShopId, string TimeNo), int>();

                // 2. 统计每个直落订单跨越的时段
                foreach (var order in allStraightThroughOrders)
                {
                    //取出开始时段和结束时段之间的时段集合
                    var crossedTimeSlots = await GetContainTimes(
                        order.ShopId,
                        date,
                        order.Beg_Key,
                        order.End_Key);

                    if (crossedTimeSlots == null || !crossedTimeSlots.Any())
                        continue;

                    //遍历所有的时段名称
                    foreach (var timeSlot in crossedTimeSlots)
                    {
                        var key = (order.ShopId, timeSlot.TimeNo);
                        //从字典中获取该键的当前统计值，存在则累加，不存在则为1
                        straightThroughCountDict.TryGetValue(key, out var currentCount);
                        straightThroughCountDict[key] = currentCount + 1;
                    }
                }

                //----——————————————————————————————————直落部分

                // 遍历每个门店和时段组合
                foreach (var item in shopBegKeys)
                {

                    // 1. 查询正常预订数
                    var bookingCount = allBbookcacheinfoOrders.Count(b =>
                     b.ShopId == item.ShopID &&
                     b.Beg_Key == item.BegKey &&
                     b.ComeDate == item.Date &&
                     b.IsDelete == false &&
                     b.BookStatus == 0);

                    // 2. 查询取消数
                    var cancelCount = allBbookcacheinfoOrders.Count(b =>
                        b.ShopId == item.ShopID &&
                        b.Beg_Key == item.BegKey &&
                        b.ComeDate == item.Date &&
                        b.IsDelete == false &&
                        b.CheckinStatus != 3);

                    // 3. 查询自来客数
                    var walkInCount = allOpencacheinfoOrders.Count(o =>
                        o.ShopId == item.ShopID &&
                        o.Beg_Key == item.BegKey &&
                        o.ComeDate == item.Date &&
                        o.BookStatus == 1);

                    // 获取当前（门店，时段）的直落数
                    var straightThroughCount = straightThroughCountDict.TryGetValue(
                        (item.ShopID, item.BegKey), out var count) ? count : 0;

                    // 5. 查询开房数
                    var openedRoomCount = allOpencacheinfoOrders.Count(o =>
                        o.ShopId == item.ShopID &&
                        o.Beg_Key == item.BegKey &&
                        o.ComeDate == item.Date);

                    // 创建统计记录
                    var summaryRecord = new SummaryStoreTimeSlotDaily
                    {
                        Ikey = Guid.NewGuid(),
                        StoreID = item.ShopID,
                        StatistDate = date,
                        Week = dayOfWeekChinese,
                        Period = item.BegName,
                        ReservatCount = bookingCount,
                        CancelCount = cancelCount,
                        StraightToCount = walkInCount,
                        StraightSetCount = straightThroughCount,
                        RoomCount = openedRoomCount,
                        IsFull = 0,
                        Remarks = string.Empty,
                        StaffID = string.Empty,
                        CreateTime = DateTime.Now
                    };

                    await _summaryStoreTimeSlotDailyRepository.InsertAsync(summaryRecord);
                }

                _summaryStoreTimeSlotDailyRepository.CommitTran();
                return true;
            }
            catch (Exception ex)
            {
                _summaryStoreTimeSlotDailyRepository.RollbackTran();
                throw new Exception("统计预约时段数据失败，请稍后重试", ex);
            }
        }

        //public async Task<List<bookcacheinfo>> GetBookByNoDelunion1(string ComeDate)
        //{
        //    // 1. 首先尝试从bookcacheinfo获取数据
        //    var cacheList = await _bookcacheinfoRepository.GetListAsync(x =>
        //        x.ComeDate == ComeDate);

        //    if (cacheList != null && cacheList.Count > 0)
        //    {
        //        return cacheList.Select(x => new bookcacheinfo
        //        {
        //            Ikey = x.Ikey,
        //            BookNo = x.BookNo,
        //            ShopId = x.ShopId,
        //            CustKey = x.CustKey,
        //            CustName = x.CustName,
        //            CustTel = x.CustTel,
        //            ComeDate = x.ComeDate,
        //            ComeTime = x.ComeTime,
        //            Beg_Key = x.Beg_Key,
        //            Beg_Name = x.Beg_Name,
        //            End_Key = x.End_Key,
        //            End_Name = x.End_Name,
        //            Numbers = x.Numbers,
        //            Rtno = x.Rtno,
        //            RtName = x.RtName,
        //            CtNo = x.CtNo,
        //            CtName = x.CtName,
        //            PtNo = x.PtNo,
        //            PtName = x.PtName,
        //            BookMemory = x.BookMemory,
        //            BookStatus = x.BookStatus,
        //            CheckinStatus = x.CheckinStatus,
        //            BookShopId = x.BookShopId,
        //            BookUserId = x.BookUserId,
        //            BookUserName = x.BookUserName,
        //            BookDateTime = x.BookDateTime,
        //            Val1 = x.Val1,
        //            IsDelete = x.IsDelete,
        //            DelUserName = x.DelUserName,
        //            EditShopID = x.EditShopID,
        //            EditUserID = x.EditUserID,
        //            EditUserName = x.EditUserName,
        //            EditDateTime = x.EditDateTime,
        //            OrderUserID = x.OrderUserID,
        //            OrderUserName = x.OrderUserName,
        //            DemandNumber = x.DemandNumber,
        //            OpenId = x.OpenId,
        //            DepositTot = x.DepositTot,
        //            RmNo = x.RmNo
        //        }).ToList();
        //    }

        //    // 2. 如果没结果，则去历史表查
        //    var historyList = await _bookhistoryRepository.GetListAsync(x =>
        //        x.ComeDate == ComeDate);

        //    return historyList?.Select(x => new bookcacheinfo
        //    {
        //        Ikey = x.Ikey,
        //        BookNo = x.BookNo,
        //        ShopId = x.ShopId,
        //        CustKey = x.CustKey,
        //        CustName = x.CustName,
        //        CustTel = x.CustTel,
        //        ComeDate = x.ComeDate,
        //        ComeTime = x.ComeTime,
        //        Beg_Key = x.Beg_Key,
        //        Beg_Name = x.Beg_Name,
        //        End_Key = x.End_Key,
        //        End_Name = x.End_Name,
        //        Numbers = x.Numbers,
        //        Rtno = x.Rtno,
        //        RtName = x.RtName,
        //        CtNo = x.CtNo,
        //        CtName = x.CtName,
        //        PtNo = x.PtNo,
        //        PtName = x.PtName,
        //        BookMemory = x.BookMemory,
        //        BookStatus = x.BookStatus,
        //        CheckinStatus = x.CheckinStatus,
        //        BookShopId = x.BookShopId,
        //        BookUserId = x.BookUserId,
        //        BookUserName = x.BookUserName,
        //        BookDateTime = x.BookDateTime,
        //        Val1 = x.Val1,
        //        IsDelete = x.IsDelete,
        //        DelUserName = x.DelUserName,
        //        EditShopID = x.EditShopID,
        //        EditUserID = x.EditUserID,
        //        EditUserName = x.EditUserName,
        //        EditDateTime = x.EditDateTime,
        //        OrderUserID = x.OrderUserID,
        //        OrderUserName = x.OrderUserName,
        //        DemandNumber = x.DemandNumber,
        //        OpenId = x.OpenId,
        //        DepositTot = x.DepositTot,
        //        RmNo = x.RmNo
        //    }).ToList() ?? new List<bookcacheinfo>();
        //}
        public async Task<List<opencacheinfo>> GetBookByNoDelunion1(string ComeDate)
        {
            // 1. 首先尝试从bookcacheinfo获取数据
            var cacheList = await _opencacheinfoRepository.GetListAsync(x =>
                x.ComeDate == ComeDate);

            if (cacheList != null && cacheList.Count > 0)
            {
                return cacheList.Select(x => new opencacheinfo
                {
                    Ikey = x.Ikey,
                    BookNo = x.BookNo,
                    ShopId = x.ShopId,
                    CustKey = x.CustKey,
                    CustName = x.CustName,
                    CustTel = x.CustTel,
                    ComeDate = x.ComeDate,
                    ComeTime = x.ComeTime,
                    Beg_Key = x.Beg_Key,
                    Beg_Name = x.Beg_Name,
                    End_Key = x.End_Key,
                    End_Name = x.End_Name,
                    Numbers = x.Numbers,
                    RtNo = x.RtNo,
                    RtName = x.RtName,
                    CtNo = x.CtNo,
                    CtName = x.CtName,
                    PtNo = x.PtNo,
                    PtName = x.PtName,
                    BookMemory = x.BookMemory,
                    BookStatus = x.BookStatus,
                    CheckinStatus = x.CheckinStatus,
                    BookShopId = x.BookShopId,
                    BookUserId = x.BookUserId,
                    BookUserName = x.BookUserName,
                    BookDateTime = x.BookDateTime,
                    Invno = x.Invno,
                    Openmemory = x.Openmemory,
                    OrderUserID = x.OrderUserID,
                    OrderUserName = x.OrderUserName,
                    RmNo = x.RmNo,
                    Val1 = x.Val1,
                    FromRmNo = x.FromRmNo,
                    IsBirthday = x.IsBirthday
                }).ToList();
            }

            // 2. 如果没结果，
            var historyList = await _openhistoryRepository.GetListAsync(x =>
                x.ComeDate == ComeDate);

            return historyList?.Select(x => new opencacheinfo
            {
                Ikey = x.Ikey,
                BookNo = x.BookNo,
                ShopId = x.ShopId,
                CustKey = x.CustKey,
                CustName = x.CustName,
                CustTel = x.CustTel,
                ComeDate = x.ComeDate,
                ComeTime = x.ComeTime,
                Beg_Key = x.Beg_Key,
                Beg_Name = x.Beg_Name,
                End_Key = x.End_Key,
                End_Name = x.End_Name,
                Numbers = x.Numbers,
                RtNo = x.RtNo,
                RtName = x.RtName,
                CtNo = x.CtNo,
                CtName = x.CtName,
                PtNo = x.PtNo,
                PtName = x.PtName,
                BookMemory = x.BookMemory,
                BookStatus = x.BookStatus,
                CheckinStatus = x.CheckinStatus,
                BookShopId = x.BookShopId,
                BookUserId = x.BookUserId,
                BookUserName = x.BookUserName,
                BookDateTime = x.BookDateTime,
                Invno = x.Invno,
                Openmemory = x.Openmemory,
                OrderUserID = x.OrderUserID,
                OrderUserName = x.OrderUserName,
                RmNo = x.RmNo,
                Val1 = x.Val1,
                FromRmNo = x.FromRmNo,
                IsBirthday = x.IsBirthday
            }).ToList() ?? new List<opencacheinfo>();
        }
        /// <summary>
        /// 获取跨越的时段列表
        /// 根据开始时段和结束时段获取中间所有时段
        /// </summary>
        /// <param name="shopId">门店ID</param>
        /// <param name="date">日期</param>
        /// <param name="begKey">开始时段编号</param>
        /// <param name="endKey">结束时段编号</param>
        /// <returns>时段列表</returns>
        public async Task<List<MShopTimeInfoJoin>> GetContainTimes(int ShopId, DateTime Date, string BegKey, string EndKey)
        {
            // 初始化结果列表，用于存储符合条件的时间段
            List<MShopTimeInfoJoin> list_Contain = new List<MShopTimeInfoJoin>();

            // 获取门店指定日期的所有时间段（已排序）
            var timeList = await GetNewTime_UpdCacheInfo(ShopId, Date);
            // 查找开始时间段（通过begKey匹配TimeNo）
            MShopTimeInfoJoin BeginInfo = timeList.Find(i => i.TimeNo == BegKey);
            // 如果未找到开始时间段，返回null
            if (BeginInfo == null)
            {
                return null;
            }
            // 将开始时间段添加到结果列表
            list_Contain.Add(BeginInfo);

            // 获取可加钟的后续时间段列表
            List<MShopTimeInfoJoin> list = await GetAddNewTime_Open(BeginInfo);

            // 如果加钟列表为空，跳过处理
            if (list == null)
            {

            }

            // 查找结束时间段（通过endKey匹配TimeNo）
            MShopTimeInfoJoin Sp_EndTime = list.Find(i => i.TimeNo == EndKey);

            // 如果结束时间段不在加钟列表中，直接返回当前结果（仅含开始时间段）
            if (!list.Contains(Sp_EndTime))
            {
                return list_Contain;
            }

            // 遍历加钟列表，添加到结果中，直到遇到结束时间段
            foreach (MShopTimeInfoJoin Sp in list)
            {
                list_Contain.Add(Sp);
                if (Sp == Sp_EndTime)
                {
                    break;// 找到结束时间段后终止循环
                }
            }

            return list_Contain;
        }

        /// <summary>
        /// 获取门店日期对应的时段信息(已排序)
        /// </summary>
        /// <param name="shopId">门店ID</param>
        /// <param name="date">日期</param>
        /// <returns>排序后的时段列表</returns>
        public async Task<List<MShopTimeInfoJoin>> GetNewTime_UpdCacheInfo(int ShopId, DateTime Date, List<MWork_MShop> List = null)
        { // 获取当前营业日（6点前算前一天）
            DateTime ntime = GetWordDate();
            // 1. 获取原始时段数据
            var result = await GetShopWordTime(ShopId, Date, List);

            // 2. 排序后返回(先按是否凌晨时段，再按开始时间)
            return result.OrderBy(i => i.IsBeforeDawn)
                        .ThenBy(i => i.BegTime)
                        .ToList();
        }

        /// <summary>
        /// 获取营业日(6点前算前一天)
        /// </summary>
        /// <returns>营业日期</returns>
        public static DateTime GetWordDate()
        {
            var now = DateTime.Now;
            return now.Hour < 6 ? now.AddDays(-1) : now;  // 6点前算前一天
        }

        /// <summary>
        /// 获取门店的所有时段信息(已区分营业日)
        /// </summary>
        /// <param name="shopId">门店ID</param>
        /// <param name="date">日期</param>
        /// <returns>时段列表</returns>
        private async Task<List<MShopTimeInfoJoin>> GetShopWordTime(int ShopId, DateTime Date, List<MWork_MShop> List = null)
        {

            // 1. 获取门店所有时段（排除IsClocks=true的）
            var shopallTile = (await GetAllInfo_Time())
                .FindAll(i => i.ShopId == ShopId && !i.IsClocks);

            // 处理每个时段，筛选符合营业日的时段
            var resultlist = new List<MShopTimeInfoJoin>();
            foreach (var item in shopallTile)
            {
                // 获取时段配置
                item.mwork = await GetConfig(item, Date, List);

                string WorkModelstring = item.mwork.WorkModel.ToString();

                // 判断时段是否应出现在营业日（匹配DayType）
                if (item.DayType.ToString().Contains("7") ||
                    item.DayType.ToString().Contains(WorkModelstring))
                {
                    resultlist.Add(item);
                }
            }

            return resultlist;
        }
        /// <summary>
        /// /// 获取门店工作配置（联表查询）
        /// </summary>
        /// <returns></returns>
        public async Task<List<MWork_MShop>> GetMWorkConfig()
        {
            // 1. 获取缓存或从数据库加载配置数据
            var workconfig = _cachedWorkConfigs ??= await _workconfigRepository.GetListAsync();
            var shopInfos = _cachedShopInfos ??= await _shopinfoRepository.GetListAsync();
            var pricemodel = _cachedPriceModels ??= await _pricemodelRepository.GetListAsync();

            // 2. 内存联表查询（
            var result = (from w in workconfig
                          join s in shopInfos on w.ShopId equals s.ShopId
                          join p in pricemodel on w.BillModel equals p.PriceModelNo
                          where w.WorkTimeEnd >= DateTime.Now
                          orderby w.WorkTimeStart descending
                          select new MWork_MShop
                          {
                              Ikey=w.Ikey,
                              ShopId = w.ShopId,
                              WorkTimeStart = w.WorkTimeStart,
                              WorkTimeEnd = w.WorkTimeEnd,
                              BillModel = w.BillModel,
                              WorkModel = w.WorkModel,
                              WorkName = w.WorkName,
                              RoomModel = w.RoomModel,
                              OutBookNumber = w.OutBookNumber,
                             
                              ShopName = s.ShopName,
                              ShopAddress= s.ShopAddress,
                              ShopTel=s.ShopTel,
                              ShopOrder=s.ShopOrder,
                              ShopAreaNo=s.ShopAreaNo,
                              ShopArea_x=s.ShopArea_x,
                              ShopArea_y=s.ShopArea_y,
                              Route=s.Route,
                              IsDel=s.IsDel,

                              PriceModelNo = p.PriceModelNo,
                              PriceModelName = p.PriceModelName,


                          }).ToList();

            return result;
        }
        /// <summary>
        /// 获取时段配置
        /// </summary>
        /// <param name="shopTime">时段信息</param>
        /// <param name="date">日期</param>
        /// <returns>时段工作配置</returns>
        private async Task<MWork_MShop> GetConfig(MShopTimeInfoJoin shopTime, DateTime date, List<MWork_MShop> list = null)
        {
            // 构造时间段的完整时间（日期 + 开始时间）
            DateTime fullDate = DateTime.ParseExact(
                date.ToString("yyyyMMdd") + (shopTime.BegTime < 1000 ? "0" + shopTime.BegTime : shopTime.BegTime.ToString()),
                "yyyyMMddHHmm",
                System.Globalization.CultureInfo.CurrentCulture
            );

            // 从缓存或传入列表中查找匹配的工作配置
            MWork_MShop mwork = null;

            if (list != null)
            {
                mwork = list.Find(i => fullDate >= i.WorkTimeStart && fullDate <= i.WorkTimeEnd);
            }
            else
            {

                var workConfigList = await GetMWorkConfig();
                mwork = workConfigList.Find(i => i.ShopId == shopTime.ShopId &&
                                              fullDate >= i.WorkTimeStart &&
                                              fullDate <= i.WorkTimeEnd);
            }

            // 若未找到配置，根据日期逻辑生成默认配置
            if (mwork == null)
            {
                mwork = new MWork_MShop();
                mwork.ShopId = shopTime.ShopId;

                // 判断是否为周末特殊时段
                if ((fullDate.DayOfWeek == DayOfWeek.Friday && shopTime.TimeMode == 2) ||
                    (fullDate.DayOfWeek == DayOfWeek.Sunday && shopTime.TimeMode == 1) ||
                    fullDate.DayOfWeek == DayOfWeek.Saturday)
                {
                    mwork.BillModel = 2;// 周末价格
                }
                else
                {
                    mwork.BillModel = 1;//平日价格
                }


                mwork.WorkModel = int.Parse(fullDate.DayOfWeek.ToString("d"));// 设置工作日类型
            }

            return mwork;
        }
        public async Task<List<MShopTimeInfoJoin>> GetAllInfo_Time()
        {
            // 1. 获取缓存或数据库中的时段数据
            var shopTimeInfos = _cachedShopTimeInfos ??= await _shoptimeinfoRepository.GetListAsync();

            var shopInfos = _cachedShopInfos ??= await _shopinfoRepository.GetListAsync();
            var timeInfos = _cachedTimeInfos ??= await _timeinfoRepository.GetListAsync();


            var query = (
                from st in shopTimeInfos
                join s in shopInfos on st.ShopId equals s.ShopId
                join ti in timeInfos on st.TimeNo equals ti.TimeNo
                select new MShopTimeInfoJoin
                {
                    Ikey = st.Ikey,
                    TimeNo = st.TimeNo,
                    ShopId = st.ShopId,
                    TimeType = st.TimeType,
                    TimeMode = st.TimeMode,
                    DayType = st.DayType,
                    ChangeMinute = st.ChangeMinute,
                    IsWechatBook = st.IsWechatBook,
                    ShopName = s.ShopName,
                    TimeName = ti.TimeName,
                    BegTime = ti.BegTime,
                    EndTime = ti.EndTime,
                    IsBeforeDawn = ti.EndTime < 600,// 计算字段：判断是否是凌晨时段
                    IsClocks = ti.IsClocks,
                    IsSpecial = ti.IsSpecial
                }
            ).ToList();

            // 3. 排序并返回结果
            return query
                .OrderBy(i => i.ShopId)
                 .ThenBy(i => i.IsBeforeDawn)
                .ThenBy(i => i.BegTime)
                .ToList();
        }
        /// <summary>
        /// 获取加钟时段列表
        /// </summary>
        /// <param name="selectTime">选择的时段</param>
        /// <returns>加钟时段列表</returns>
        private async Task<List<MShopTimeInfoJoin>> GetAddNewTime_Open(MShopTimeInfoJoin selectTime)
        {
            List<MShopTimeInfoJoin> list = null;

            if (selectTime != null)
            {
                // 获取门店所有时段
                var timeAddListall = await GetAllInfo_Time();

                // 筛选符合条件的加钟时段：
                // 1. 非当前时段
                // 2. 时段类型重叠
                // 3. 时间逻辑（非凌晨或晚于当前时段结束）
                // 4. 匹配营业日配置
                list = timeAddListall.FindAll(i =>
                    i.TimeNo != selectTime.TimeNo &&
                    (StringAnalysis.IsOverlap(i.TimeType, selectTime.TimeType) == true &&
                    ((selectTime.EndTime > 900 && i.EndTime > selectTime.EndTime) ||
                     i.EndTime < 900 ||
                     i.BegTime < 900) &&
                    i.ShopId == selectTime.mwork.ShopId &&
                    (i.DayType.ToString().Contains("7") ||
                     i.DayType.ToString().Contains(selectTime.mwork.WorkModel.ToString()))))
                    .ToList();


            }
            // 返回排序后的列表（先凌晨时段，再按开始时间）

            return list?.OrderBy(i => i.IsBeforeDawn).ThenBy(i => i.BegTime).ToList();
        }
    }

    /// <summary>
    /// 字符串分析工具类
    /// </summary>
    public static class StringAnalysis
    {
        /// <summary>
        /// 检查两个字符串是否有重叠字符
        /// </summary>
        public static bool IsOverlap(string string1, string string2)
        {
            return string1.Any(c => string2.Contains(c));
        }

        /// <summary>
        /// 检查两个整数的字符串表示是否有重叠字符
        /// </summary>
        public static bool IsOverlap(int string1, int string2)
        {
            return IsOverlap(string1.ToString(), string2.ToString());
        }
    }

}
