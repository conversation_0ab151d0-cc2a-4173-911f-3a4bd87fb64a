using System;

namespace HdProject.Common.DTOs
{
    public class LoginResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }
        public TokenResponseDto Token { get; set; }
        
        public static LoginResult Success(TokenResponseDto token)
        {
            return new LoginResult
            {
                IsSuccess = true,
                Token = token
            };
        }
        
        public static LoginResult Fail(string errorMessage)
        {
            return new LoginResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }
}