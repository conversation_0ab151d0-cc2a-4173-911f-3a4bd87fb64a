﻿using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicI;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplate;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SongBase.WallPainting
{
    /// <summary>
    /// 主题模板控制器接口
    /// </summary>
    public class WPTopicITemplateController : PublicControllerBase
    {
        private readonly IWPTopicITemplateService _wPTopicITemplateService;
        private readonly ILogger<WPTopicITemplateController> _logger;

        public WPTopicITemplateController(IWPTopicITemplateService wPTopicITemplateService, ILogger<WPTopicITemplateController> logger)
        {
            _wPTopicITemplateService = wPTopicITemplateService;
            _logger = logger;
        }


        /// <summary>
        /// 查询主题模板列表的接口(不含分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllTemplate()
        {
            var result = await _wPTopicITemplateService.GetAll();
            return ApiData(result.Model);
        }

        /// <summary>
        /// 根据ID查询主题模板信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdTemplate([FromQuery] WPTopicITemplateGetByIdRequestDto request)
        {
            var result = await _wPTopicITemplateService.GetByIdAsync(request);
            return ApiData(result.Model);
        }

    }
}
