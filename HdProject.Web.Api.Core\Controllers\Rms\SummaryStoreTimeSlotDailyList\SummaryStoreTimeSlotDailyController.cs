﻿using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Application.Services.Interfaces.SaasPos.CommodityManagement;
using HdProject.Application.Services.Rms;
using HdProject.Domain.Context.CommodityManagement;
using HdProject.Domain.Context.RMS.SummaryStoreTimeSlotDailyList;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;


namespace HdProject.Web.Api.Core.Controllers.Rms.SummaryStoreTimeSlotDailyList
{
    //预约时段统计
    [Route("[controller]/[Action]")]
    public class SummaryStoreTimeSlotDailyController : PublicControllerBase
    {
        private readonly ISummaryStoreTimeSlotDailyService _summaryStoreTimeSlotDailyService;

        public SummaryStoreTimeSlotDailyController(ISummaryStoreTimeSlotDailyService summaryStoreTimeSlotDailyService)
            => _summaryStoreTimeSlotDailyService = summaryStoreTimeSlotDailyService;
        /// <summary>
        /// 获取预约时段详情
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetSummaryStoreTimeSlotDailyRecord([FromQuery] SummaryStoreTimeSlotDailyContext context)
        {
            var res = await _summaryStoreTimeSlotDailyService.GetSummaryStoreTimeSlotDailyRecord(context);
            return ApiPaged(res, context.Paging);

        }
        /// <summary>
        /// 统计预约时段数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetSummaryStoreTimeSlotDailyListRecord([FromQuery] SummaryStoreTimeSlotDailyListContext context)
        {
            var res = await _summaryStoreTimeSlotDailyService.GetSummaryStoreTimeSlotDailyListRecord(context);
            return Content(res.ToString(), "application/json"); // 直接返回JSON内容

        }
    }
}

