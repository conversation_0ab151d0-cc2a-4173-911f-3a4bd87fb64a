using SqlSugar;
using System;

namespace HdProject.Domain.Entities
{
    [SugarTable("[Users]")]  
    public class User
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        
        [SugarColumn(Length = 50)]
        public string? UserName { get; set; }
        
        [SugarColumn(Length = 100)]
        public string PasswordHash { get; set; }
        
        [SugarColumn(Length = 50)]
        public string? Email { get; set; }
        
        [SugarColumn(Length = 20)]
        public string? PhoneNumber { get; set; }
        
        public bool IsActive { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public DateTime? LastLoginDate { get; set; }
        
        // 用于存储用户角色，可以是逗号分隔的字符串，如："admin,user"
        [SugarColumn(Length = 100)]
        public string Roles { get; set; }

        // 用于存储刷新令牌
        [SugarColumn(Length = 500)]
        public string RefreshToken { get; set; }

        // 刷新令牌的过期时间
        public DateTime? RefreshTokenExpiryTime { get; set; }
    }
}