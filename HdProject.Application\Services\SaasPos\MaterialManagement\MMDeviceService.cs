﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Furion.DatabaseAccessor;
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMDevice;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using SqlSugar;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
namespace HdProject.Application.Services.SaasPos.MaterialManagement
{
    /// <summary>
    /// 设备服务接口实现类
    /// </summary>
    public class MMDeviceService : IMMDeviceService
    {
        private readonly IRepositorySaas<MMDevice> _repositoryMMDevice;//设备表
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IMapper _mapper;
        public MMDeviceService(IRepositorySaas<MMDevice> repositoryMMDevice, ISqlSugarClient sqlSugarClient, IMapper mapper)
        {
            _repositoryMMDevice = repositoryMMDevice;
            _sqlSugarClient = sqlSugarClient;
            _mapper = mapper;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }


        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMDeviceAddResponseDto> AddAsync(MMDeviceAddRequestDto requestDto)
        {
            MMDeviceAddResponseDto responseDto = new MMDeviceAddResponseDto();
            try
            {
                var resultModel = new MMDevice()
                {
                    DeviceName = requestDto.Model.DeviceName,
                    DeviceModel = requestDto.Model.DeviceModel,
                    DeviceVersion = "1.0",//默认初始版本1.0
                    DeviceOrientation = requestDto.Model.DeviceOrientation,
                    DeviceHeight = requestDto.Model.DeviceHeight,
                    DeviceWidth = requestDto.Model.DeviceWidth,
                    DeviceResolution = requestDto.Model.DeviceResolution,
                    StoreId = requestDto.Model.StoreId,
                    OnlineStatusCode = requestDto.Model.OnlineStatusCode,
                    CreatedBy = "张三",
                    CreatedTime = DateTime.Now
                };
                var result = await _repositoryMMDevice.InsertAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 删除信息（软删除）
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMDeviceDeleteResponseDto> DeleteAsync(MMDeviceDeleteRequestDto requestDto)
        {
            MMDeviceDeleteResponseDto responseDto = new MMDeviceDeleteResponseDto();
            try
            {
                var resultModel = await _repositoryMMDevice.GetFirstAsync(a => a.DeviceID == requestDto.DeviceID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法删除，请检查该设备是否存在！");
                }
                resultModel.IsActive = true;
                resultModel.DisabledBy = "张三";
                resultModel.DisabledTime = DateTime.Now;
                var result = await _repositoryMMDevice.UpdateAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMDeviceGetAllAsyncResponseDto> GetAllAsync(MMDeviceGetAllAsyncRequestDto requestDto)
        {
            MMDeviceGetAllAsyncResponseDto responseDto = new MMDeviceGetAllAsyncResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<MMDevice>(true);
                predicate = predicate.And(it => it.IsActive == false);//过滤非禁用的数据
                                                                      // 动态添加条件
                if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
                {
                    predicate = predicate.And(it => it.DeviceName.Contains(requestDto.QueryCriteria));
                }
                var result = await GetPageAllAssociationBdAsync(requestDto, predicate);
                //var result = await _repositoryMMDevice.GetPageListAsync(requestDto, predicate);
                var model = new List<MMDeviceDto>();
                foreach (var resultModel in result)
                {
                    var st = 0;
                    if (resultModel.finalVisit != null)
                    {
                        TimeSpan ts = DateTime.Now - Convert.ToDateTime(resultModel.finalVisit.LastVisitTime);
                        st = ts.TotalMinutes <= 5 ? 1 : 0;
                    }
                    model.Add(new MMDeviceDto
                    {
                        DeviceID = resultModel.DeviceID,
                        DeviceName = resultModel.DeviceName,
                        DeviceModel = resultModel.DeviceModel,
                        DeviceVersion = resultModel.DeviceVersion,
                        DeviceOrientation = resultModel.DeviceOrientation,
                        DeviceHeight = resultModel.DeviceHeight,
                        DeviceWidth = resultModel.DeviceWidth,
                        DeviceResolution = resultModel.DeviceResolution,
                        StoreId = resultModel.StoreId,
                        Status = st,
                        CreatedTime = resultModel.CreatedTime
                    });
                }
                // var model = _mapper.Map<List<MMDeviceDto>>(result);//使用AutoMapper进行对象属性映射
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 查询全部信息(分页)，需要绑定设备状态
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<MMDevice>> GetPageAllAssociationBdAsync(Pagination page, Expression<Func<MMDevice, bool>> whereExpression = null)
        {
            RefAsync<int> totalCount = 0;
            var query = _db.Queryable<MMDevice>()
            .Mapper(d => d.finalVisit, d => d.DeviceID).OrderByDescending(st => st.DeviceID);
            if (whereExpression != null)
            {
                query = query.Where(whereExpression);
            }

            var list = await query.ToPageListAsync(page.Page, page.Rows, totalCount);
            page.Records = totalCount;
            return list;
        }

        /// <summary>
        /// 根据ID查询，需要绑定设备状态
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MMDeviceDto> GetAssociationBdDetailsAsync(int id, string ver)
        {
            // 查询设备基本信息
            var device = await _db.Queryable<MMDevice>()
                .Where(d => d.DeviceID == id && d.IsActive == false)
                .FirstAsync();

            if (device == null)
            {
                return null;
            }
            //每调用一次该接口，需要更新访问时间
            var mMDeviceFinalVisit = await _db.Queryable<MMDeviceFinalVisit>()
           .Where(d => d.DeviceID == id).FirstAsync();
            if (mMDeviceFinalVisit == null)
            {
                var mdfv = new MMDeviceFinalVisit()
                {
                    DeviceID = id,
                    LastVisitTime = DateTime.Now,//最后访问时间
                };
                await _db.Insertable(mdfv).ExecuteCommandAsync();
            }
            else
            {
                var mdfv = new MMDeviceFinalVisit()
                {
                    FinalVisitID = mMDeviceFinalVisit.FinalVisitID,
                    DeviceID = mMDeviceFinalVisit.DeviceID,
                    LastVisitTime = DateTime.Now,//最后访问时间
                };
                await _db.Updateable(mdfv).ExecuteCommandAsync();
            }

            //var finalVisitDto = await _db.Queryable<MMDeviceFinalVisit>()
            //    .Where(f => f.DeviceID == id)
            //    .Select(a => new MMDeviceFinalVisitDto
            //    {
            //        FinalVisitID = a.FinalVisitID,
            //        DeviceID = a.DeviceID,
            //        LastVisitTime = a.LastVisitTime
            //    })
            //    .FirstAsync();


            // 查询设备关联的投放任务
            var campaign = await _db.Queryable<MMPlaybackDevice>()
                .Where(pd => pd.DeviceID == id)
                .OrderByDescending(pd => pd.CampaignID)
                .ToListAsync();

            if (campaign.Count == 0)
            {
                return new MMDeviceDto
                {
                    DeviceID = device.DeviceID,
                    DeviceName = device.DeviceName,
                    DeviceVersion = device.DeviceVersion,
                };
            }
            var version = "";
            var layoutInfo = new MMLayoutTemplateDto();
            var regions = new List<MMLayoutRegionDto>();
            foreach (var cp in campaign)
            {
                var plt = await _db.Queryable<MMAdCampaign>()
                    .Where(ac => ac.CampaignID == cp.CampaignID && ac.IsActive == false)
                    .Select(ac => new { ac.PlaylistID, ac.CampaignVersion, ac.StartTime, ac.EndTime, ac.IsItDaily })
                .ToListAsync();

                var playlist = plt.FirstOrDefault(ac =>
        ac.IsItDaily
    ? (Convert.ToDateTime(ac.StartTime).TimeOfDay <= DateTime.Now.TimeOfDay &&
       Convert.ToDateTime(ac.EndTime).TimeOfDay >= DateTime.Now.TimeOfDay)
    : (ac.StartTime <= DateTime.Now && ac.EndTime >= DateTime.Now)
        );


                if (playlist == null)
                {
                    continue;
                }
                version = playlist.CampaignVersion;
                if (ver == playlist.CampaignVersion) //如果版本号相同，则返回空
                {
                    return null;
                }

                // 查询节目单关联的布局模板
                var layoutTemplate = await _db.Queryable<MMPlaylist>()
                    .Where(p => p.PlaylistID == playlist.PlaylistID && p.IsActive == false)
                    .Select(p => p.LayoutID)
                    .FirstAsync();
                // 查询布局模板详情
                layoutInfo = await _db.Queryable<MMLayoutTemplate>()
               .Where(lt => lt.LayoutID == layoutTemplate && lt.IsActive == false)
               .Select(lt => new MMLayoutTemplateDto
               {
                   LayoutID = lt.LayoutID,
                   LayoutName = lt.LayoutName,
                   LayoutDescription = lt.LayoutDescription,
                   LayoutRows = lt.LayoutRows,
                   LayoutCols = lt.LayoutCols,
                   TemplateGridCount = lt.TemplateGridCount
               })
               .FirstAsync();

                // 查询布局区域
                regions = await _db.Queryable<MMLayoutRegion>()
               .Where(lr => lr.LayoutID == layoutTemplate)
               .Select(lr => new MMLayoutRegionDto
               {
                   RegionID = lr.RegionID,
                   RegionName = lr.RegionName,
                   LayoutID = lr.LayoutID,
                   StartX = lr.StartX,
                   StartY = lr.StartY,
                   RegionWidth = lr.RegionWidth,
                   RegionHeight = lr.RegionHeight,
                   HtmlTemplate = lr.HtmlTemplate
               })
               .ToListAsync();

                // 查询每个区域的节目详情
                foreach (var region in regions)
                {
                    // 查询节目编排明细
                    var playlistDetails = await _db.Queryable<MMPlaylistDetail>()
                        .Where(pd => pd.PlaylistID == playlist.PlaylistID && pd.RegionID == region.RegionID && pd.IsActive == false)
                        .Select(pd => new MMPlaylistDetailDto
                        {
                            DetailID = pd.DetailID,
                            ProgramName = pd.ProgramName,
                            PlaylistID = pd.PlaylistID,
                            RegionID = pd.RegionID
                        })
                        .FirstAsync();

                    // 先查询所有详情项
                    var detailItems = await _db.Queryable<MMPlaylistDetailXq>()
                        .Where(px => px.DetailID == playlistDetails.DetailID)
                        .OrderBy(px => px.Sequence)
                        .Select(px => new
                        {
                            px.DetailXqID,
                            px.Sequence,
                            px.AdjustedDuration,
                            px.DetailID,
                            px.FileID
                        })
                        .ToListAsync();

                    // 获取所有文件ID
                    var fileIds = detailItems.Select(x => x.FileID).Distinct().ToList();

                    // 批量查询所有文件
                    var files = await _db.Queryable<MMFile>()
                        .Where(f => fileIds.Contains(f.FileID) && f.IsActive == false)
                        .Select(f => new MMFileDto
                        {
                            FileID = f.FileID,
                            FileName = f.FileName,
                            FilePath = f.FilePath,
                            FormatType = f.FormatType,
                            FileSize = f.FileSize,
                            //ThumbnailPath = f.ThumbnailPath,
                            Width = f.Width,
                            Height = f.Height,
                            VideoDuration = f.VideoDuration
                        })
                        .ToListAsync();

                    // 组合结果
                    playlistDetails.PlaylistDetailXqList = detailItems.Select(px => new MMPlaylistDetailXqDto
                    {
                        DetailXqID = px.DetailXqID,
                        DetailID = px.DetailID,
                        FileID = px.FileID,
                        Sequence = px.Sequence,
                        AdjustedDuration = px.AdjustedDuration,
                        MMFile = files.FirstOrDefault(f => f.FileID == px.FileID)
                    }).ToList();

                    region.PlaylistDetails = playlistDetails;
                }
            }

            if (regions.Count == 0)
            {
                return null;
            }
            return new MMDeviceDto
            {
                DeviceID = device.DeviceID,
                DeviceName = device.DeviceName,
                DeviceVersion = version,
                LayoutTemplate = layoutInfo,
                LayoutRegions = regions
            };
        }

        /// <summary>
        /// 根据ID查询，需要绑定关联表信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MMDevice> GetByIdAssociationBdAsync(int id)
        {
            if (id == 0)
            {
                throw new Exception("无法查询，编码需大于0！");
            }
            var mdvModel = await _db.Queryable<MMDevice>()
                .Where(c => c.DeviceID == id && c.IsActive == false)
                .FirstAsync();
            var mpbdModel = await _db.Queryable<MMPlaybackDevice>()
                .Where(c => c.DeviceID == id)
                .ToListAsync();
            var madclist = new List<MMAdCampaign>();
            foreach (var mpbd in mpbdModel)
            {
                var mMAd = await _db.Queryable<MMAdCampaign>()
                .Where(c => c.CampaignID == mpbd.CampaignID && c.IsActive == false)
                .FirstAsync();
                madclist.Add(new MMAdCampaign()
                {
                    CampaignID = mMAd.CampaignID,
                    CampaignName = mMAd.CampaignName,
                    StartTime = mMAd.StartTime,
                    EndTime = mMAd.EndTime,
                });
            }
            return new MMDevice
            {
                DeviceID = mdvModel.DeviceID,
                DeviceName = mdvModel.DeviceName,
                mMAdCampaigns = madclist
            };
        }

        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMDeviceGetByIdAsyncResponseDto> GetByIdAsync(MMDeviceGetByIdAsyncRequestDto requestDto)
        {
            MMDeviceGetByIdAsyncResponseDto responseDto = new MMDeviceGetByIdAsyncResponseDto();
            //var result = await GetByIdAssociationBdAsync(requestDto.DeviceID);
            try
            {
                var result = await GetByIdAssociationBdAsync(requestDto.DeviceID);
                //var result = await _repositoryMMDevice.GetFirstAsync(a => a.DeviceID == requestDto.DeviceID && a.IsActive == false);
                if (result != null)
                {
                    var model = _mapper.Map<MMDeviceDto>(result);//使用AutoMapper进行对象属性映射
                    responseDto.Model = model;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMDeviceUpdateResponseDto> UpdateAsync(MMDeviceUpdateRequestDto requestDto)
        {
            MMDeviceUpdateResponseDto responseDto = new MMDeviceUpdateResponseDto();
            try
            {
                var resultModel = await _repositoryMMDevice.GetFirstAsync(a => a.DeviceID == requestDto.Model.DeviceID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法修改，请检查该设备是否存在！");
                }
                //var model = new MMDevice()
                //{
                //    DeviceID= requestDto.Model.DeviceID,
                //    DeviceName = requestDto.Model.DeviceName,
                //    DeviceModel = requestDto.Model.DeviceModel,
                //    DeviceVersion = requestDto.Model.DeviceVersion,
                //    DeviceOrientation = requestDto.Model.DeviceOrientation,
                //    DeviceHeight = requestDto.Model.DeviceHeight,
                //    DeviceWidth = requestDto.Model.DeviceWidth,
                //    DeviceResolution = requestDto.Model.DeviceResolution,
                //    StoreId = requestDto.Model.StoreId,
                //    OnlineStatusCode = requestDto.Model.OnlineStatusCode,
                //    CreatedBy = resultModel.CreatedBy,
                //    CreatedTime = resultModel.CreatedTime,
                //    DisabledBy = resultModel.DisabledBy,
                //    DisabledTime = resultModel.DisabledTime,
                //    ModifiedBy = "张三",
                //    ModifiedTime = DateTime.Now
                //};
                var model = _mapper.Map<MMDevice>(requestDto.Model);
                model.DeviceOrientation = resultModel.DeviceOrientation;
                model.DeviceHeight = resultModel.DeviceHeight;
                model.DeviceWidth = resultModel.DeviceWidth;
                model.DeviceResolution = resultModel.DeviceResolution;
                model.DeviceVersion = resultModel.DeviceVersion;
                model.CreatedBy = resultModel.CreatedBy;
                model.CreatedTime = resultModel.CreatedTime;
                model.DisabledBy = resultModel.DisabledBy;
                model.DisabledTime = resultModel.DisabledTime;
                model.ModifiedBy = "张三";
                model.ModifiedTime = DateTime.Now;
                var result = await _repositoryMMDevice.UpdateAsync(model);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 根据设备ID和版本查询详情信息的接口
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMDeviceGetDeviceDetailsAsyncResponseDto> GetDeviceDetails(MMDeviceGetDeviceDetailsAsyncRequestDto requestDto)
        {
            MMDeviceGetDeviceDetailsAsyncResponseDto responseDto = new MMDeviceGetDeviceDetailsAsyncResponseDto();
            try
            {
                var result = await GetAssociationBdDetailsAsync(requestDto.DeviceID, requestDto.Version);
                if (result != null)
                {
                    if (result.DeviceID > 0)
                    {
                        var model = _mapper.Map<MMDeviceDto>(result);//使用AutoMapper进行对象属性映射
                        responseDto.Model = result;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 接收前端传入设备的宽高，并保存至对应设备
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMDeviceSaveWhResponseDto> SaveDeviceWhAsync(MMDeviceSaveWhRequestDto requestDto)
        {
            MMDeviceSaveWhResponseDto responseDto = new MMDeviceSaveWhResponseDto();
            try
            {
                var Model = await _repositoryMMDevice.GetFirstAsync(a => a.DeviceID == requestDto.DeviceID && a.IsActive == false);
                if (Model == null)
                {
                    throw new Exception("无法修改，请检查该设备是否存在！");
                }
                Model.DeviceWidth = requestDto.Width;
                Model.DeviceHeight = requestDto.Height;
                Model.DeviceResolution = requestDto.Height + "*" + requestDto.Width;
                var result = await _repositoryMMDevice.UpdateAsync(Model);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 根据设备编号修改绑定的投放任务
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMDeviceUpdateLaunchCampaignResponseDto> UpdateLaunchAdCampaignAsyns(MMDeviceUpdateLaunchCampaignRequestDto requestDto)
        {
            MMDeviceUpdateLaunchCampaignResponseDto responseDto = new MMDeviceUpdateLaunchCampaignResponseDto();
            try
            {
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    var md = await _db.Queryable<MMDevice>().Where(a => a.DeviceID == requestDto.DeviceID && a.IsActive == false).FirstAsync();
                    if (md == null)
                    {
                        throw new Exception("无法修改，请检查该设备是否存在！");
                    }
                    //await _db.Updateable(new MMAdCampaign() { PlaybackDeviceID = p.PlaybackDeviceID }).ExecuteCommandAsync();

                    //var mMAdCampaign = new MMAdCampaign()
                    //{
                    //    LayoutID = requestDto.Model.MMLayoutTemplateEntity.LayoutID,
                    //    LayoutName = requestDto.Model.MMLayoutTemplateEntity.LayoutName,
                    //    LayoutDescription = requestDto.Model.MMLayoutTemplateEntity.LayoutDescription,
                    //    LayoutRows = requestDto.Model.MMLayoutTemplateEntity.LayoutRows,
                    //    LayoutCols = requestDto.Model.MMLayoutTemplateEntity.LayoutCols,
                    //    TemplateGridCount = requestDto.Model.MMLayoutTemplateEntity.TemplateGridCount,
                    //    CreatedBy = mlt.CreatedBy,
                    //    CreatedTime = mlt.CreatedTime,
                    //    DisabledBy = mlt.DisabledBy,
                    //    DisabledTime = mlt.DisabledTime,
                    //    ModifiedBy = "张三",
                    //    ModifiedTime = DateTime.Now,
                    //};
                    //var ver = Convert.ToInt32(mlt.CampaignVersion) + 1;//修改绑定节目同时需要修改节目版本
                    //mlt.CampaignVersion = Convert.ToString(ver);
                    //await _db.Updateable(mlt).UpdateColumns(it => it.CampaignVersion).ExecuteCommandAsync();//首先修改布局模板

                    var pbd = await _db.Queryable<MMPlaybackDevice>().Where(a => a.DeviceID == requestDto.DeviceID).ToListAsync();
                    var macpList = new List<MMAdCampaign>();
                    foreach (var p in pbd)
                    {
                        var mcount = requestDto.mMPlaybackDevice.Where(a => a.CampaignID == p.CampaignID).ToList();
                        if (mcount.Count == 0) //如果数据库中的投放节目不存在于参数对象中，则为删除
                        {
                            await _db.Deleteable(new MMPlaybackDevice() { PlaybackDeviceID = p.PlaybackDeviceID }).ExecuteCommandAsync();
                            var mlt = await _db.Queryable<MMAdCampaign>().Where(a => a.CampaignID == p.CampaignID && a.IsActive == false).FirstAsync();
                            if (mlt == null)
                            {
                                throw new Exception("无法修改，请检查该投放任务是否存在！");
                            }
                            var ver = Convert.ToInt32(mlt.CampaignVersion) + 1;//修改绑定节目同时需要修改节目版本
                            mlt.CampaignVersion = Convert.ToString(ver);
                            macpList.Add(mlt);
                        }
                    }
                    var mpbdlist = new List<MMPlaybackDevice>();
                    foreach (var m in requestDto.mMPlaybackDevice)
                    {
                        var mpbdcount = await _db.Queryable<MMPlaybackDevice>().Where(a => a.DeviceID == requestDto.DeviceID && a.CampaignID == m.CampaignID).ToListAsync();
                        if (mpbdcount.Count == 0) //如果参数对象中的设备不存在于数据库中，则为新增
                        {
                            mpbdlist.Add(new MMPlaybackDevice
                            {
                                CampaignID = m.CampaignID,
                                DeviceID = m.DeviceID,
                            });
                            var mlt = await _db.Queryable<MMAdCampaign>().Where(a => a.CampaignID == m.CampaignID && a.IsActive == false).FirstAsync();
                            if (mlt == null)
                            {
                                throw new Exception("无法修改，请检查该投放任务是否存在！");
                            }
                            var ver = Convert.ToInt32(mlt.CampaignVersion) + 1;//修改绑定节目同时需要修改节目版本
                            mlt.CampaignVersion = Convert.ToString(ver);
                            macpList.Add(mlt);
                        }
                    }
                    await _db.Updateable(macpList).UpdateColumns(it => it.CampaignVersion).ExecuteCommandAsync();//首先修改布局模板
                    await _db.Insertable(mpbdlist).ExecuteCommandAsync();
                });

                if (result.IsSuccess)
                {
                    responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
    }
}
