﻿using HdProject.Domain.Entities;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Infrastructure.Repositories
{
    public class UserRepository : RepositorySaas<UserTest>, IRepository<UserTest>
    {
        public UserRepository(ISqlSugarClient db) : base(db) { }

      

    }
}