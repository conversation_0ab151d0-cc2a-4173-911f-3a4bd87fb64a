﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using HdProject.Common.DTOs;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Result.Page;
using HdProject.Domain.DTOs;
using HdProject.Domain.Context;
using System.Linq.Expressions;

namespace HdProject.Application.Services.Interfaces.SaasPos
{
    public interface IShopBookLeaderService
    {
        /// <summary>
        /// 团长注册开团
        /// </summary>
        Task<LeaderInfoStatusDto> RegisterAsLeader(RegisterLeaderContext context);

        /// <summary>
        /// 获取团长个人信息
        /// </summary>
        Task<LeaderInfoWithCommissionDto> GetLeaderPersonalInfo(GetLeaderPersonalInfoContext context);

        /// <summary>
        /// 获取团长码生成海报
        /// </summary>
        //Task<GetPersonCommissionPosterModel> GetLeaderInvitationCode(GetLeaderInvitationCodeContext context);

        /// <summary>
        /// 团长提现
        /// </summary>
        Task<bool> ApplyForLeaderWithdrawal(ApplyForLeaderWithdrawalContext context);

        /// <summary>
        /// 获取团长预约记录
        /// </summary>
        Task<List<Shop_BookCacheInfo>> GetLeaderAppointmentRecords(GetLeaderAppointmentRecordsContext context);

        /// <summary>
        /// 获取团长提现记录
        /// </summary>
        Task<List<BookLeaderWithdrawRecord>> GetLeaderWithdrawalRecords(GetLeaderWithdrawalRecordsContext context);

        /// <summary>------------------------------------------
        /// 查询团长汇总报表
        /// </summary>
        Task<List<LeaderSummaryReportDto>> GetLeaderSummaryReport();
        /// <summary>
        /// 查询所有团长信息
        /// </summary>
        Task<List<LeaderInfoDto>> GetLeadersInfo(GetLeaderInfoListContext context);
        /// <summary>
        /// 查询所有团长订单信息
        /// </summary>
        Task<List<LeaderOrderInfoDto>> GetLeadersOrderInfo(GetOrderInfoListContext context);
        /// <summary>
        /// 查询所有团长佣金计提信息
        /// </summary>
        Task<List<LeaderCommissionAccrualDto>> GetLeadersAccrueCommission(GetCommissionRecordsContext context);

    }
}
