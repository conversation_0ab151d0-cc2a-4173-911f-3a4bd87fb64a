﻿using HdProject.Application.Services.Interfaces.SysService;
using HdProject.Domain.Context.SysService;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SysService
{
    /// <summary>
    /// 叫号系统接口控制器
    /// </summary>
    public class CallNumberController : PublicControllerBase
    {
        private readonly IQueuingSystemService _queuingSystemService;
        public CallNumberController(IQueuingSystemService queuingSystemService)
        {
            _queuingSystemService = queuingSystemService;
        }

        [HttpGet("Get")]
        public async Task<IActionResult> GetCallNumber([FromQuery] CallNumberRequestDto requestDto)
        {
            var response = await _queuingSystemService.GenerateNumberAsync(requestDto);
            return ApiData(response);
        }

    }
}
