﻿using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.Rms.Room
{
    /// <summary>
    /// 房间管理
    /// </summary>
    public class RmInfoController : PublicControllerBase
    {
        IRmInfoService _rmInfoService;
        public RmInfoController(IRmInfoService rmInfoService)
        {
            _rmInfoService = rmInfoService;
        }

        [HttpGet("GetList")]
        public async Task<IActionResult> GetList()
        {
            var rmList = await _rmInfoService.GetList();
            return ApiData(rmList);
        }

    }
}
