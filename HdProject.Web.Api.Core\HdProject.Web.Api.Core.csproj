﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
	
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageReference Include="Microsoft.Net.Sdk.Compilers.Toolset" Version="9.0.301" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.37" />
	<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
	<PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />


  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HdProject.Application\HdProject.Application.csproj" />
    <ProjectReference Include="..\HdProject.Domain\HdProject.Domain.csproj" />
    <ProjectReference Include="..\HdProject.Infrastructure\HdProject.Infrastructure.csproj" />
    <ProjectReference Include="..\HdProject.Web.Core\HdProject.Web.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\OperateData\" />
    <Folder Include="wwwroot\" />
  </ItemGroup>

</Project>
