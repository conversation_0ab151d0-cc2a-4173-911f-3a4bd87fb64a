using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.Swagger;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace HdProject.Web.Api.Core.Filters
{
    /// <summary>
    /// 为开发环境提供的Swagger自动认证过滤器
    /// 注意：仅在开发环境使用此过滤器
    /// </summary>
    //public class SwaggerDevAuthorizationFilter : IOperationFilter
    //{
    //    private readonly bool _isDevEnvironment;
    //    private readonly string _devToken;

    //    public SwaggerDevAuthorizationFilter(IWebHostEnvironment environment, IConfiguration configuration)
    //    {
    //        _isDevEnvironment = environment.IsDevelopment();
    //        _devToken = configuration["SwaggerSettings:DevToken"] ?? string.Empty;
    //    }

    //    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    //    {
    //        // 仅在开发环境中启用此功能
    //        if (!_isDevEnvironment || string.IsNullOrEmpty(_devToken))
    //            return;

    //        // 检查操作方法上是否有[Authorize]特性
    //        var authorizeAttributes = context.MethodInfo?.GetCustomAttributes<AuthorizeAttribute>(true)
    //            ?? Enumerable.Empty<AuthorizeAttribute>();

    //        // 检查控制器上是否有[Authorize]特性
    //        var controllerAuthorizeAttributes = context.MethodInfo?.DeclaringType?.GetCustomAttributes<AuthorizeAttribute>(true)
    //            ?? Enumerable.Empty<AuthorizeAttribute>();

    //        // 如果方法或控制器上有[Authorize]特性，并且没有[AllowAnonymous]特性
    //        var allowAnonymous = context.MethodInfo?.GetCustomAttributes<AllowAnonymousAttribute>(true).Any() == true;
    //        if (!allowAnonymous && (authorizeAttributes.Any() || controllerAuthorizeAttributes.Any()))
    //        {
    //            // 确保参数集合已初始化
    //            operation.Parameters ??= new List<OpenApiParameter>();

    //            // 添加一个隐藏的Authorization参数，它将自动使用开发Token
    //            operation.Parameters.Add(new OpenApiParameter
    //            {
    //                Name = "Authorization",
    //                In = ParameterLocation.Header,
    //                Description = "Bearer Token (自动添加)",
    //                Required = true,
    //                Schema = new OpenApiSchema { Type = "string" },
    //                Example = new Microsoft.OpenApi.Any.OpenApiString($"Bearer {_devToken}"),
    //            });
    //        }
    //    }

      
    //}
}