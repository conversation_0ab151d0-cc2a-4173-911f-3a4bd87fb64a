﻿
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Application.Services.SaasPos.MaterialManagement;
using HdProject.Common.Utility;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout;
using HdProject.Domain.WebApi;
using HdProject.Web.Api.Core.Utility;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement
{
    /// <summary>
    /// 素材管理接口控制器
    /// </summary>
    public class MMFileController : PublicControllerBase
    {
        private readonly IMMFileService _mMFileService;
        private readonly FileManagement _management;
        private readonly ILogger<MMFileController> _logger;
        public MMFileController(IMMFileService mMFileService, FileManagement management, ILogger<MMFileController> logger)
        {
            _mMFileService = mMFileService;
            _management = management;
            _logger = logger;
        }
        /// <summary>
        /// 查询素材全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllMMFile([FromQuery] MMFileGetAllAsyncRequestDto request)
        {
            var result = await _mMFileService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询素材的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdMMFile([FromQuery] MMFileGetByIdAsyncRequestDto request)
        {
            var result = await _mMFileService.GetByIdAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 上传素材信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Upload")]
        [DisableRequestSizeLimit]
        public async Task<IActionResult> MMFileAddValue([FromForm] List<IFormFile> files)
        
        {
            MMFileUploadRequestDto fileDto = new MMFileUploadRequestDto();

            var fileResult = await _management.FileUploadAsync(files);//上传文件
            var ModelFile = fileResult.FileEntity.Select(a => new MMFileDto
            {
                FileName = a.FileName,
                FilePath = a.FilePath,
                FormatType = a.FormatType,
                FileSize = a.FileSize,
                Width = a.Width,
                Height = a.Height,
                VideoDuration = 10,
                ThumbnailPath = a.ThumbnailPath,
            }).ToList();
            fileDto.Model = ModelFile;
            var result = await _mMFileService.UploadAsync(fileDto);
            string ma = "";
            if (result.Index != files.Count)
            {
                ma = "素材无法全部上传！";
            }
            else
            {
                ma = "素材全部上传成功！";
            }
            return ApiData(new
            {
                message = ma,
                FailedFiles = fileResult.FailedFiles
            });
        }

        /// <summary>
        /// 修改素材信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> MMFileUpdateValue([FromBody] MMFileUpdateRequestDto request)
        {
            var result = await _mMFileService.UpdateAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }

        }

        /// <summary>
        /// 删除素材信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> MMFileDeletedValue([FromBody] MMFileDeleteRequestDto request)
        {
            var result = await _mMFileService.DeleteAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }

        }
    }
}
