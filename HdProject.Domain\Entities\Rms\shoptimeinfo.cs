﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.Rms
{
    public class shoptimeinfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Ikey { get; set; }

        /// <summary>
        /// 时间编号
        /// </summary>
       
        public string TimeNo { get; set; }

        /// <summary>
        /// 店铺ID
        /// </summary>
      
        public int ShopId { get; set; }

        /// <summary>
        /// 时间类型
        /// </summary>
      
        public int TimeType { get; set; }

        /// <summary>
        /// 时间模式
        /// </summary>
       
        public int TimeMode { get; set; }

        /// <summary>
        /// 日期类型
        /// </summary>
      
        public int DayType { get; set; }

       
       
        public int ChangeMinute { get; set; }

        /// <summary>
        /// 是否微信预约
        /// </summary>
        
        public bool IsWechatBook { get; set; }
    }
}
