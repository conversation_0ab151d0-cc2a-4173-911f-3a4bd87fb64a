﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Result.Page;

namespace HdProject.Domain.Context.MainFood.Room
{
    /// <summary>
    /// 区域业务请求类
    /// </summary>
    public class RmAreaDto
    {
        /// <summary>
        /// 区域编号
        /// </summary>

        public string AreaNo { get; set; }
        /// <summary>
        /// 区域名称
        /// </summary>
        public string AreaName { get; set; }
    }
    /// <summary>
    /// 查询
    /// </summary>
    public class RmAreaGetAllAsyncRequestDto: Pagination
    {
        /// <summary>
        /// 区域分页查询请求参数
        /// </summary>
        //public string UserId { get; set; }
        public int? Status { get; set; }
        public DateTime? AllDates { get; set; }
        /// <summary>
        /// 查询条件字符串
        /// </summary>
        public string? QueryCriteria { get; set; }
    }

    /// <summary>
    /// 根据区域编号查询区域信息
    /// </summary>
    public class RmAreaGetByIdAsyncRequestDto
    {
        public string AreaNo { get; set; }
    }
    /// <summary>
    /// 新增区域信息请求类
    /// </summary>
    public class RmAreaAddRequestDto
    {
        public RmAreaDto Model { get; set; }
    }
    /// <summary>
    /// 删除区域信息请求类
    /// </summary>
    public class RmAreaDeleteRequestDto
    {
        public string? RmAreaID { get; set; }
    }
    /// <summary>
    /// 修改区域信息请求类
    /// </summary>
    public class RmAreaUpdateRequestDto
    {
        public RmAreaDto Model { get; set; }
    }
}
