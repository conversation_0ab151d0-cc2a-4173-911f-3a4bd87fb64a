﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Result.Page
{
    /// <summary>
    /// 分页信息
    /// </summary>
    public class Pagination
    {
        int _Rows = 10;
        int _Page = 1;
        #region 外部传递参数

        /// <summary>
        /// 每页行数
        /// </summary>
        public int Rows
        {
            get
            {
                if (_Rows == 0) return 10;
                return _Rows;
            }
            set => _Rows = value;
        }
        /// <summary>
        /// 当前页
        /// </summary>
        public int Page
        {
            get
            {
                if (_Page <= 0) return 1;
                return _Page;
            }
            set => _Page = value;
        }
        /// <summary>
        ///排序列
        /// </summary>
        /// 
        public string? Sidx { get; set; }
        /// <summary>
        /// 排序类型
        /// </summary>
        public string? Sord { get; set; }
        #endregion

        #region 返回参数
        /// <summary>
        /// 总记录数
        /// </summary>
        public int Records { get; set; }
        /// <summary>
        ///  总页数
        /// </summary>
        public int Total
        {

            get
            {
                if (Records > 0)
                {
                    return Records % this.Rows == 0 ? Records / this.Rows : Records / this.Rows + 1;
                }
                return 0;
            }
        }
        #endregion
    }
}