﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;


namespace HdProject.Application.Services.Interfaces.MainFood
{
    /// <summary>
    /// 房间
    /// </summary>
    public interface IRoomService
    {
        /// <summary>
        /// 根据ID查询房间信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RoomGetByIdAsyncResponseDto> GetByIdAsync(RoomGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 查询房间全部信息
        /// </summary>
        /// <returns></returns>
        Task<RoomGetAllAsyncResponseDto> GetAllAsync(RoomGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 新增房间信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RoomAddResponseDto> AddAsync(RoomAddRequestDto requestDto);
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RoomUpdateResponseDto> UpdateAsync(RoomUpdateRequestDto requestDto);
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RoomDeleteResponseDto> DeleteAsync(RoomDeleteRequestDto requestDto);

    }
}
