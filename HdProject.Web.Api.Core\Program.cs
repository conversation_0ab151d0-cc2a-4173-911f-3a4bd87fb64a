using HdProject.Application.Services;
using HdProject.Application.Services.CommodityAdjustment;
using HdProject.Application.Services.CommodityManagement;
using HdProject.Application.Services.DbFood.DbFoodRoom;
using HdProject.Application.Services.GrouponBase;
using HdProject.Application.Services.Interfaces;
using HdProject.Application.Services.Interfaces.CommodityManagement;
using HdProject.Application.Services.Interfaces.DbFood.DbFoodRoom;
using HdProject.Application.Services.Interfaces.GrouponBase;
using HdProject.Application.Services.Interfaces.MainFood;
//using HdProject.Application.Services.Interfaces.OperateData;
using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Application.Services.Interfaces.SaasPos;
using HdProject.Application.Services.Interfaces.SaasPos.Commission;
//using HdProject.Application.Services.CommodityAdjustment;
using HdProject.Application.Services.Interfaces.SaasPos.CommodityManagement;
using HdProject.Application.Services.Interfaces.SaasPos.ExternalGroupBuying;
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Application.Services.Interfaces.SaasPos.PlaceAnOrder;
using HdProject.Application.Services.Interfaces.SaasPos.SongManagement;

//using HdProject.Application.Services.CommodityAdjustment;
using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Application.Services.Interfaces.SysService;
using HdProject.Application.Services.MainFood;
//using HdProject.Application.Services.OperateData;
using HdProject.Application.Services.Rms;
using HdProject.Application.Services.SaasPos;
using HdProject.Application.Services.SaasPos.Commission;
using HdProject.Application.Services.SaasPos.ExternalGroupBuying;
using HdProject.Application.Services.SaasPos.MaterialManagement;
using HdProject.Application.Services.SaasPos.PlaceAnOrder;
using HdProject.Application.Services.SaasPos.SongManagement;
using HdProject.Application.Services.SongBase.WallPainting;
using HdProject.Application.Services.SysService;
using HdProject.Common.Config;
using HdProject.Application.Services.Interfaces.GrouponBase.Banking;
using HdProject.Application.Services.GrouponBase.Banking;
using HdProject.Common.Mapper.AutoMapperMappingProfile;
using HdProject.Common.Utility;
using HdProject.Domain.Context;
using HdProject.Domain.Entities.GroupBase;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.DbContext;
using HdProject.Infrastructure.Repositories.Imp;
using HdProject.Infrastructure.Repositorys.Imp;
using HdProject.Web.Api.Core.Utility;
using HdProject.Web.Api.Core.Utility.Filters;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using NLog;
using NLog.Web;
using SqlSugar;
using StackExchange.Redis;
using System.Net.Http.Headers;
using System.Text;


var builder = WebApplication.CreateBuilder(args);

// 配置 NLog 日志记录
// 参考 OSharp.NLog 的实现方式，配置 NLog 作为日志提供者
builder.Services.AddLogging(loggingBuilder =>
{
    loggingBuilder.ClearProviders(); // 清除默认的日志提供者
    loggingBuilder.AddNLogWeb(); // 添加 NLog 作为日志提供者，适用于 ASP.NET Core
});

// 设置 NLog 配置，从配置文件加载
NLog.LogManager.Setup().LoadConfigurationFromFile("nlog.config");

// Add services to the container.

builder.Services.AddControllers();
builder.Services.AddHttpClient<IWeChatCloudDbService, WeChatCloudDbService>(client =>
{
    client.BaseAddress = new Uri("https://api.weixin.qq.com/");
    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
});

//builder.Services.AddHttpClient();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
//builder.Services.AddOpenApi();

builder.Services.AddControllers(options =>
{
    options.Filters.Add<GlobalExceptionFilter>();
});
//设置multipart表单请求体的最大长度（上传文件）
builder.Services.Configure<FormOptions>(options =>
{
    // 设置为1GB，单位是字节
    options.MultipartBodyLengthLimit = 1073741824;
});
// 配置微信服务
builder.Services.Configure<WeChatOptions>(builder.Configuration.GetSection("WeChat"));

#region 返回数据格式大小写保持一致。
builder.Services.AddControllers().AddJsonOptions(
    config =>
    {
        config.JsonSerializerOptions.PropertyNamingPolicy = null;
    });
#endregion
// 配置AutoMapper
builder.Services.AddAutoMapper(typeof(AutoMapperMappingProfile));

#region Swagger配置

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    // 添加JWT认证的按钮
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    // 添加API文档信息
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "HD Project API",
        Version = "v1",
        Description = "HD Project Web API"
    });

    // 仅在开发环境下注册自动认证过滤器
    // if (builder.Environment.IsDevelopment())
    // {
    //     c.OperationFilter<HdProject.Web.Api.Core.Filters.SwaggerDevAuthorizationFilter>();
    // }
});

#endregion

// 配置JWT设置
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));

// 配置SQL日志设置
builder.Services.Configure<SqlLogSettings>(builder.Configuration.GetSection("SqlLog"));

// 添加JWT认证
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["JwtSettings:SecretKey"])),
        ValidateIssuer = true,
        ValidIssuer = builder.Configuration["JwtSettings:Issuer"],
        ValidateAudience = true,
        ValidAudience = builder.Configuration["JwtSettings:Audience"],
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };

    // 使用自定义的 JwtBearerEvents 处理认证事件
    options.Events = new HdProject.Web.Api.Core.Filters.CustomJwtBearerEvents
    {
        OnMessageReceived = context =>
        {
            var accessToken = context.Request.Headers["Authorization"].ToString();

            // 如果Authorization头存在但不是以"Bearer "开头，则认为整个值就是token
            if (!string.IsNullOrEmpty(accessToken) && !accessToken.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                context.Token = accessToken;
            }

            return Task.CompletedTask;
        }
    };
});

// 添加授权策略
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("RequireAdminRole", policy =>
        policy.RequireRole("admin"));

    options.AddPolicy("RequireUserRole", policy =>
        policy.RequireRole("user"));
});

// 注册Redis连接
builder.Services.AddSingleton<IConnectionMultiplexer>(sp =>
    ConnectionMultiplexer.Connect(builder.Configuration.GetConnectionString("Redis")));

// 注册 SqlSugar
builder.Services.AddScoped<ISqlSugarClient>(_ => SqlSugarDbContext.InitDb(builder.Configuration));

// 注册应用层服务
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IShopBookLeaderService, ShopBookLeaderService>();
builder.Services.AddScoped<IRoomService, RoomService>();
builder.Services.AddScoped<IRoomTypeService, RoomTypeService>();
builder.Services.AddScoped<IRmAreaService, RmAreaService>();
builder.Services.AddScoped<IRtAutoService, RtAutoService>();
builder.Services.AddScoped<IRtTimePriceService, RtTimePriceService>();
//builder.Services.AddScoped<ICommodityAdjustmentService, CommodityAdjustmentService>();
builder.Services.AddScoped<IMMAdCampaignService, MMAdCampaignService>();
builder.Services.AddScoped<IMMDeviceService, MMDeviceService>();
builder.Services.AddScoped<IMMFileService, MMFileService>();
builder.Services.AddScoped<IMMLayoutTemplateService, MMLayoutTemplateService>();
builder.Services.AddScoped<IMMPlaylistService, MMPlaylistService>();
builder.Services.AddScoped<FileManagement>();
builder.Services.AddScoped<IQueuingSystemService, QueuingSystemService>();
builder.Services.AddScoped<IMMFolderService, MMFolderService>();
builder.Services.AddScoped<IWayFoodMapService, WayFoodMapService>();
builder.Services.AddScoped<ISummaryStoreTimeSlotDailyService, SummaryStoreTimeSlotDailyService>();
builder.Services.AddScoped<ICommodityAdjustmentService, CommodityAdjustmentService>();
builder.Services.AddScoped<IMarketingActivityService, MarketingActivityService>();
builder.Services.AddScoped<IDbFoodRoomService, DbFoodRoomService>();
builder.Services.AddScoped<IRefundService, RefundService>();
builder.Services.AddScoped<ITransferService, TransferService>();
// 银行与商品券 CRUD 服务
builder.Services.AddScoped<IBankService, BankService>();
builder.Services.AddScoped<IBankDealService, BankDealService>();
//builder.Services.AddScoped<INewOldCustomersService, NewOldCustomersService>();
#region 专员提成
builder.Services.AddScoped<IAssignShowingsService, AssignShowingsService>();
builder.Services.AddScoped<IAssignEmployeeService, AssignEmployeeService>();
#endregion
builder.Services.AddScoped<IPlaceAnOrderService, PlaceAnOrderService>(); //下单公用类注册

builder.Services.AddScoped<IJwtService, JwtService>();
//Rms服务注入
builder.Services.AddScoped<IRmInfoService, RmInfoService>();
// 注册微信服务
builder.Services.AddScoped<IWeChatCloudDbService>(provider =>
{
    var options = provider.GetRequiredService<IOptions<WeChatOptions>>().Value;
    var httpClient = provider.GetRequiredService<IHttpClientFactory>().CreateClient();
    return new WeChatCloudDbService(
        httpClient,
        options.AppId,
        options.AppSecret,
        options.EnvId);
});

#region 壁画功能
builder.Services.AddScoped<IWPTopicIBdFileService, WPTopicIBdFileService>();
builder.Services.AddScoped<IWPTopicIFileService, WPTopicIFileService>();
builder.Services.AddScoped<IWPTopicIService, WPTopicIService>();
builder.Services.AddScoped<IWPTopicITemplateDetailsService, WPTopicITemplateDetailsService>();
builder.Services.AddScoped<IWPTopicITemplateService, WPTopicITemplateService>();
#endregion
#region 缺歌登记
builder.Services.AddScoped<ISongFeedbackService, SongFeedbackService>();
#endregion


// 注册仓储
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
builder.Services.AddScoped(typeof(IRepositorySaas<>), typeof(RepositorySaas<>));
builder.Services.AddScoped(typeof(IRepositoryMainFood<>), typeof(RepositoryMainFood<>));
builder.Services.AddScoped(typeof(IRepositoryRms<>), typeof(RepositoryRms<>));
builder.Services.AddScoped(typeof(IRepositorySongBase<>), typeof(RepositorySongBase<>));
builder.Services.AddScoped(typeof(IRepositoryOperateData<>), typeof(RepositoryOperateData<>));
builder.Services.AddScoped(typeof(IRepositoryGroupBase<>), typeof(RepositoryGroupBase<>));
builder.Services.AddScoped(typeof(IRepositoryGroupBase2<>), typeof(RepositoryGroupBase2<>));
builder.Services.AddScoped(typeof(IRepositoryRms2009<>), typeof(RepositoryRms2009<>));
builder.Services.AddScoped(typeof(IRepositoryRms2019<>), typeof(RepositoryRms2019<>));
builder.Services.AddScoped(typeof(IRepositoryDbFood<>), typeof(RepositoryDbFood<>));
builder.Services.AddScoped(typeof(IRepositoryMIMS<>), typeof(RepositoryMIMS<>));
builder.Services.AddScoped(typeof(IRepositoryWxInfo<>), typeof(RepositoryWxInfo<>));


// 配置跨域
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// 修改为只在开发环境下启用 Swagger
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "HD Project API V1");
        // 设置Swagger UI为默认启动页
        c.RoutePrefix = string.Empty;

        // 启用持久化授权功能，保存token到localStorage
        c.ConfigObject.PersistAuthorization = true;
    });
}

// 只在生产环境启用HTTPS重定向
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}
//启用静态资源文件
//app.UseStaticFiles(new StaticFileOptions
//{
//    FileProvider = new PhysicalFileProvider(
//        Path.Combine(builder.Environment.ContentRootPath, "admin")),
//    RequestPath = "/admin"
//});
app.UseStaticFiles(); // 启用静态文件服务
app.UseCors("AllowAll");
// 添加认证中间件（必须在授权中间件之前）
app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();

// 监听所有网络接口（包括内网IP）
//app.Run("http://*************:5010");