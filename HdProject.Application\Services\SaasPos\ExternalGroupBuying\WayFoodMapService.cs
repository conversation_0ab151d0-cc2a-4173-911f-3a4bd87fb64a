﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Furion.DatabaseAccessor;
using HdProject.Application.Services.Interfaces.SaasPos.ExternalGroupBuying;
using HdProject.Domain.Context.SaasPos.ExternalGroupBuying;
using HdProject.Domain.DTOs.SaasPos.ExternalGroupBuying;
using HdProject.Domain.Entities.SaasPos.ExternalGroupBuying;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using SqlSugar;
using StackExchange.Redis;

namespace HdProject.Application.Services.SaasPos.ExternalGroupBuying
{
    public class WayFoodMapService : IWayFoodMapService
    {
        private readonly IRepositorySaas<WayFoodMap> _repositorySaas;//团购表
        private readonly IRepositorySaas<GnrStore> _repositorySaasStore;//团购门店
        private readonly IMapper _mapper;
        private readonly ISqlSugarClient _sqlSugarClient;
        public WayFoodMapService(IRepositorySaas<WayFoodMap> repositorySaas, IRepositorySaas<GnrStore> repositorySaasStore, IMapper mapper, ISqlSugarClient sqlSugarClient)
        {
            _repositorySaas = repositorySaas;
            _repositorySaasStore = repositorySaasStore;
            _mapper = mapper;
            _sqlSugarClient = sqlSugarClient;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }
        /// <summary>
        /// 单条新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<WayFoodMapAddResponseDto> AddAsync(WayFoodMapAddRequestDto requestDto)
        {

            WayFoodMapAddResponseDto responseDto = new WayFoodMapAddResponseDto();
            try
            {
                if (requestDto.Model.FdNo.Length > 4)
                {
                    throw new Exception("天王食品编号不能超过四位字符！");
                }
                if (requestDto.Model.Qty <= 0)
                {
                    throw new Exception("数量需要大于0！");
                }
                var model = _mapper.Map<WayFoodMap>(requestDto.Model);
                responseDto.Index = await _repositorySaas.InsertAsync(model);
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WayFoodMapDeleteResponseDto> DeleteAsync(WayFoodMapDeleteRequestDto requestDto)
        {
            WayFoodMapDeleteResponseDto responseDto = new WayFoodMapDeleteResponseDto();
            try
            {
                var modelList = _mapper.Map<List<WayFoodMap>>(requestDto.Model).Select(a => new WayFoodMap
                {
                    ThirdFdNo = a.ThirdFdNo,

                }).ToList();
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    responseDto.Index = await _db.Deleteable<WayFoodMap>().WhereColumns(modelList, it => new { it.ThirdFdNo }).ExecuteCommandAsync(); //批量删除
                });
            }
            catch (Exception ex)
            {

                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 查询全部信息(分页)，需要绑定门店
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<WayFoodMap>> GetPageAllAssociationBdAsync(Pagination page, Expression<Func<WayFoodMap, bool>> whereExpression = null)
        {
            RefAsync<int> totalCount = 0;
            var query = _db.Queryable<WayFoodMap>()
                .LeftJoin<GnrStore>((x, y) => x.StoreId == y.StoreId).OrderByDescending(x => x.Id).Select((x, y) => new WayFoodMap
                {
                    Id = x.Id,
                    FdNo = x.FdNo,
                    ThirdFdNo = x.ThirdFdNo,
                    Platform = x.Platform,
                    StoreId = x.StoreId,
                    Qty = x.Qty,
                    store = new GnrStore
                    {
                        StoreName = y.StoreName
                    }

                });
            if (whereExpression != null)
            {
                query = query.Where(whereExpression);
            }

            var list = await query.ToPageListAsync(page.Page, page.Rows, totalCount);
            page.Records = totalCount;
            return list;
        }

        public async Task<WayFoodMapGetAllResponseDto> GetAllAsync(WayFoodMapGetAllRequestDto requestDto)
        {
            WayFoodMapGetAllResponseDto responseDto = new WayFoodMapGetAllResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<WayFoodMap>(true);
                // predicate = predicate.And(it => it.IsActive == false);//过滤非禁用的数据
                // 动态添加条件
                if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
                {
                    predicate = predicate.And(x => x.FdNo.Contains(requestDto.QueryCriteria));
                }
                var result = await GetPageAllAssociationBdAsync(requestDto, predicate);
                //var model = _mapper.Map<List<MMDeviceDto>>(result);//使用AutoMapper进行对象属性映射
                var model = new List<WayFoodMapDto>();
                foreach (var item in result)
                {
                    //var result2 = await _repositorySaas.GetFirstAsync(it => it.StoreId == item.StoreId);
                    var m = new WayFoodMapDto()
                    {
                        Id = item.Id,
                        FdNo = item.FdNo,
                        ThirdFdNo = item.ThirdFdNo,
                        Platform = item.Platform,
                        StoreId = item.StoreId,
                        StoreName = item.StoreId == -1 ? "所有门店" : item.store?.StoreName,
                        PlatformName = item.Platform switch
                        {
                            1 => "美团",
                            2 => "抖音",
                            3 => "内部",
                            _ => ""
                        },
                        Qty = item.Qty,
                    };
                    model.Add(m);
                }
                //var model = _mapper.Map<List<WayFoodMapDto>>(result);
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 批量导入新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WayFoodMapImportResponseDto> ImportAsync(WayFoodMapImportRequestDto requestDto)
        {

            WayFoodMapImportResponseDto responseDto = new WayFoodMapImportResponseDto();
            try
            {
                var model = _mapper.Map<List<WayFoodMap>>(requestDto.Model);
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    responseDto.Index = await _repositorySaas.InsertRangeAsync(model);
                });
            }
            catch (Exception ex)
            {

                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WayFoodMapUpdateResponseDto> UpdateAsync(WayFoodMapUpdateRequestDto requestDto)
        {
            WayFoodMapUpdateResponseDto responseDto = new WayFoodMapUpdateResponseDto();
            try
            {
                if (requestDto.Model.FdNo.Length > 4)
                {
                    throw new Exception("天王食品编号不能超过四位字符！");
                }
                if (requestDto.Model.Qty <= 0)
                {
                    throw new Exception("数量需要大于0！");
                }
                var model = _mapper.Map<WayFoodMap>(requestDto.Model);
                responseDto.Index = await _repositorySaas.UpdateAsync(model);
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 单条删除
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WayFoodMapDeleteResponseDto> SingleItemDeleteAsync(WayFoodMapSingleItemDeleteRequestDto requestDto)
        {
            WayFoodMapDeleteResponseDto responseDto = new WayFoodMapDeleteResponseDto();
            try
            {
                responseDto.Index = await _db.Deleteable(new WayFoodMap() { Id = requestDto.Id }).ExecuteCommandAsync(); //删除
            }
            catch (Exception ex)
            {

                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<GnrStoreGetAllResponseDto> GetStoreAllAsync()
        {
            GnrStoreGetAllResponseDto responseDto = new GnrStoreGetAllResponseDto();
            try
            {
                var response = await _repositorySaasStore.GetListAsync();
                responseDto.Model = response.Select(a => new GnrStoreDto
                {
                    StoreId = a.StoreId,
                    StoreName = a.StoreName,
                }).ToList();
            }
            catch (Exception ex)
            {

                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
    }
}
