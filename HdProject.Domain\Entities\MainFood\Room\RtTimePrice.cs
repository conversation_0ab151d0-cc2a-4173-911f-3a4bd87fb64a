﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.MainFood.Room
{
    /// <summary>
    /// 房间价格
    /// </summary>
    /// 
    [SugarTable("RtTimePrice")]
    public class RtTimePrice
    {
        [SugarColumn(IsPrimaryKey = true, Length = 2)]
        public string RtNo { get; set; }

        [SugarColumn(IsPrimaryKey = true, Length = 1)]
        public string DayOfWeek { get; set; }

        [SugarColumn(IsPrimaryKey = true, Length = 5)]
        public string FromTime { get; set; }

        [SugarColumn(IsPrimaryKey = true, Length = 5)]
        public string ToTime { get; set; }

        public int RmPrice { get; set; }

        public int SRmPrice { get; set; }

        public int WeekEndPrice { get; set; }
        public int DiscRate { get; set; }

        [SugarColumn(ColumnName = "msrepl_tran_version")]
        public Guid MsreplTranVersion { get; set; }
    }
}
