﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Entities.Rms;
using SqlSugar;

namespace HdProject.Domain.Context.RMS.SummaryStoreTimeSlotDailyList
{
    public  class SummaryStoreTimeSlotDailyListContext
    {
        public string Comedate { get; set; }
    }
    /// <summary>
    /// 门店-时段联合扩展表
    /// </summary>
    public class MShopTimeInfoJoin 
    {
        public string TimeNo { get; set; }  // 时段编号
        public string Ikey { get; set; }  
        public int ShopId { get; set; }  // 门店ID
        public int TimeType { get; set; }  // 时段类型
        public int TimeMode { get; set; }  // 时段模式
        public int DayType { get; set; }  // 适用日期类型
        public string TimeName { get; set; }  // 时段名称
        public int BegTime { get; set; }  // 开始时间
        public int EndTime { get; set; }  // 结束时间
        public bool IsClocks { get; set; }  // 是否加钟时段
        public string ShopName { get; set; }
        public bool IsBeforeDawn { get; set; }  // 是否为凌晨时段(根据EndTime计算)
        public bool IsSpecial { get; set; }
        public bool IsWechatBook { get; set; }
        public int ChangeMinute { get; set; }

        [SugarColumn(IsIgnore = true)]
        public MWork_MShop mwork { get; set; }  // 时段工作配置(不映射到数据库)

    }

    /// <summary>
    /// 时段工作配置实体
    /// 用于存储时段的计费模式和工作日模式
    /// </summary>
    public class MWork_MShop
    {
        /// <summary>
        /// 分店名称
        /// </summary>
        public string ShopName { get; set; }

        /// <summary>
        /// 多个门店ID的集合
        /// </summary>
        public string ShopIds { get; set; }

        /// <summary>
        /// 消费名称
        /// </summary>
        public string PriceModelName { get; set; }
        public int PriceModelNo { get; set; }
        /// <summary>
        /// 由Guid类型转过来的
        /// </summary>
        /// 

        public string Ikey { get; set; }
        /// <summary>
        /// 工作名称
        /// </summary>
        public string WorkName { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime WorkTimeStart { get; set; }
        public int OutBookNumber { get; set; }
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime WorkTimeEnd { get; set; }
        /// <summary>
        /// 收费模式（1：正常  2：假日）
        /// </summary>
        public int BillModel { get; set; }
        /// <summary>
        /// 营业日（使用星期标识，如：123）标识周一，周二，周三.....
        /// </summary>
        public int WorkModel { get; set; }
        /// <summary>
        /// 房费模式(此字段已经取消)
        /// </summary>
        public int RoomModel { get; set; }

        /// <summary>
        /// 门店ID
        /// </summary>
        public int ShopId { get; set; }


        /// <summary>
        /// 扩展字段 是否有效（0 有效 1 失效）
        /// </summary>
        public int Status
        {
            get
            {
                int today = int.Parse(DateTime.Now.ToString("yyyyMMdd"));
                int TimeEnd = int.Parse(WorkTimeEnd.ToString("yyyyMMdd"));
                if (today > TimeEnd)
                {
                    return 1;
                }
                else
                {
                    return 0;
                }
            }
        }
        public string ShopAddress { get; set; }
        public string ShopTel { get; set; }
        public int ShopOrder { get; set; }
        public int ShopAreaNo { get; set; }
        public int ShopArea_x { get; set; }
        public int ShopArea_y { get; set; }
        public string Route { get; set; }
        public bool IsDel { get; set; }
    }

}
