﻿using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Domain.Entities.Rms;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Application.Services.Rms
{
    /// <summary>
    /// 房间管理
    /// </summary>
    public class RmInfoService : IRmInfoService
    {
        private readonly IRepositoryRms<RmInfo> _Repository;
        public RmInfoService(IRepositoryRms<RmInfo> userRepository) => _Repository = userRepository;

        public async Task<List<RmInfo>> GetList()
        {
            return await _Repository.GetListAsync();
        }
    }
}
