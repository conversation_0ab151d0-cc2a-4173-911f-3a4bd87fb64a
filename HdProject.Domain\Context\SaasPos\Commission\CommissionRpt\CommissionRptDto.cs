﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Context.SaasPos.Commission.CommissionRpt
{
    public class CommissionRptDto
    {
        /// <summary>
        /// 门店编号
        /// </summary>
        public string ShopID { get; set; }
        /// <summary>
        /// 客服专员
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 自订自看VIP及以上
        /// </summary>
        public decimal SelfBookingVIPNum { get; set; }
        /// <summary>
        /// 自订自看VIP提成
        /// </summary>
        public decimal SelfBookingVIPAmount { get; set; }
        /// <summary>
        /// 自订自看大房
        /// </summary>
        public decimal SelfBookingNum { get; set; }
        /// <summary>
        /// 自订自看大房提成
        /// </summary>
        public decimal SelfBookingAmount { get; set; }
        /// 公司派房间数
        /// </summary>
        public decimal CompanyAssignNum { get; set; }
        /// <summary>
        /// 公司派房提成
        /// </summary>
        public decimal CompanyAssignAmount { get; set; }
        /// <summary>
        /// 同事推荐间数
        /// </summary>
        public decimal ColleaguesReNum { get; set; }
        /// <summary>
        /// 同事推荐看房提成
        /// </summary>
        public decimal ColleaguesReAmount { get; set; }
        /// <summary>
        /// 看房提成小计
        /// </summary>
        public decimal ViewingFee { get; set; }
        /// <summary>
        /// 推荐同事间数
        /// </summary>
        public decimal RecommendNum { get; set; }

        /// <summary>
        /// 推荐同事提成
        /// </summary>
        public decimal RecommendAmount { get; set; }
        /// <summary>
        /// 自订自看业绩
        /// </summary>
        public decimal SelfBookingTurnover { get; set; }
        /// <summary>
        /// 自订自看提成
        /// </summary>
        public decimal SelfBookingCom { get; set; }
        /// <summary>
        /// 自订非自看业绩
        /// </summary>
        public decimal PureOrderTurnover { get; set; }
        /// <summary>
        /// 自订非自看提成
        /// </summary>
        public decimal PureOrderCom { get; set; }
        /// <summary>
        /// 公司指派业绩
        /// </summary>
        public decimal CompanyAssignTurnover { get; set; }
        /// <summary>
        /// 公司指派提成
        /// </summary>
        public decimal CompanyAssignCom { get; set; }
        /// <summary>
        /// 同事推荐业绩
        /// </summary>
        public decimal ColleaguesReTurnover { get; set; }
        /// <summary>
        /// 同事推荐提成
        /// </summary>
        public decimal ColleaguesReCom { get; set; }
        /// <summary>
        /// 总业绩提成
        /// </summary>
        public decimal TotalComs { get; set; }
        /// <summary>
        /// 门店经费提取
        /// </summary>
        public decimal StoreExpenses { get; set; }
        /// <summary>
        /// 个人业绩小计
        /// </summary>
        public decimal PersonalPerformance { get; set; }
        /// <summary>
        /// 个人总提成
        /// </summary>
        public decimal PersonTotalComs { get; set; }
        /// <summary>
        /// 提成时间
        /// </summary>
        public string CommissionDate { get; set; }
    }
}
