﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.MarketingActivityManagement;
using HdProject.Domain.Context.RMS;
using HdProject.Domain.DTOs.RMS;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using static HdProject.Domain.Context.MarketingActivityManagement.MarketingActivityContext;
using static HdProject.Domain.DTOs.MarketingActivityManagement.MarketingActivityDto;

namespace HdProject.Application.Services.Interfaces.CommodityManagement
{
    /// <summary>
    /// 获取销售和卡卷管理信息
    /// </summary>
    public interface IMarketingActivityService
    {
       /// <summary>
       /// 查询
       /// </summary>
       /// <param name="context"></param>
       /// <returns></returns>
        Task<JObject> GetSalesAndCardsInfoRecord(GetSalesAndCardsInfoContext context);
        /// <summary>
        /// 新增、修改
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<EditorSalesAndCardsInfoDto>> EditorSalesAndCardsInfoRecord([FromForm] EditorSalesAndCardsInfoContext context);
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<bool> DeleteSalesAndCardsInfoRecord(DeleteSalesAndCardsInfoContext context);
        /// <summary>
        /// 获取卡卷列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<GetCardSheetListDto>> GetCardSheetListRecord(GetCardSheetListContext context);
        
       






    }
}
