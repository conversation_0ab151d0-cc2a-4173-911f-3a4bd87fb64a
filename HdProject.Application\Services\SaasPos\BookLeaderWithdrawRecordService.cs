﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SaasPos;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.SaasPos
{
    public class BookLeaderWithdrawRecordService:IBookLeaderWithdrawRecordService
    {
        private readonly IRepositorySaas<BookLeaderWithdrawRecord> _Repository;
        public BookLeaderWithdrawRecordService(IRepositorySaas<BookLeaderWithdrawRecord> userRepository) => _Repository = userRepository;
        
        //private readonly IRepositorySaas<BookLeaderWithdrawRecord> _leaderWithdrawRecordRepository;
        //public ShopBookLeaderService(IRepositorySaas<BookLeaderWithdrawRecord> leaderRepository) => _leaderWithdrawRecordRepository = leaderRepository;

        
    }
}
