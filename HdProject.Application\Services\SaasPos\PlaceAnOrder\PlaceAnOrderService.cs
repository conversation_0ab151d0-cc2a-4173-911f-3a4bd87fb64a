﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.SaasPos.PlaceAnOrder;
using HdProject.Domain.Context.SaasPos.PlaceAnOrder;
using HdProject.Domain.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace HdProject.Application.Services.SaasPos.PlaceAnOrder
{
    public class PlaceAnOrderService : IPlaceAnOrderService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        public PlaceAnOrderService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 传入下单参数进行下单操作，返回操作成功状态
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<bool>  GetOrderByOperation(PlaceAnOrderRequestDto requestDto)
        {
            var Xdurl = $"http://ktv{requestDto.ShopId}.tang-hui.com.cn:90/execuse.ashx?Ex=Th_BillOperation&t=1&" +
            $"RmNo={requestDto.RmNo}" +
            $"&CashUserId={requestDto.CashUserId}" +
            $"&InputUserId={requestDto.InputUserId}" +
            $"&CashType={requestDto.CashType}" +
            $"&listBill={requestDto.FdNo},{requestDto.Qty},{requestDto.Price},,0";
            var Xdclient = _httpClientFactory.CreateClient();
            Xdclient.BaseAddress = new Uri(Xdurl);
            var Xdresponse = await Xdclient.GetAsync(Xdurl);//get请求
            if (Xdresponse.IsSuccessStatusCode)
            {
                var Xdresult = Xdresponse.Content.ReadAsStringAsync();
                string cleanJson = Xdresult.Result.Trim().TrimStart('(').TrimEnd(')');
                dynamic result = JsonConvert.DeserializeObject(cleanJson);
                var success= (bool)result.Success;
                return success;
            }
            else
            {
                //throw new Exception("下单失败！");
                return false;
            }
        }
    }
}
