﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SongBase.WallPainting
{
    /// <summary>
    /// 主题绑定素材表
    /// </summary>
    /// 
    [SugarTable("WP_TopicIBdFile")]
    public class WPTopicIBdFile
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int TbfID { get; set; }
        public int TopicID { get; set; }
        public int DetailsID { get; set; }
        public int FileID { get; set; }

        // 导航属性
        [SugarColumn(IsIgnore = true)]
        public WPTopicI wPTopicI { get; set; }
        [SugarColumn(IsIgnore = true)]
        public WPTopicITemplateDetails wPTopicITemplateDetails { get; set; }
        [SugarColumn(IsIgnore = true)]
        public WPTopicIFile wPTopicIFile { get; set; }
    }
}
