using System;
using System.Collections.Generic;

namespace HdProject.Common.Config
{
    /// <summary>
    /// SQL日志记录配置
    /// </summary>
    public class SqlLogSettings
    {
        /// <summary>
        /// 是否启用SQL日志记录
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 是否记录到控制台
        /// </summary>
        public bool LogToConsole { get; set; } = false;

        /// <summary>
        /// 是否记录到日志文件
        /// </summary>
        public bool LogToFile { get; set; } = true;

        /// <summary>
        /// 慢SQL阈值（毫秒）
        /// </summary>
        public int SlowSqlThresholdMs { get; set; } = 500;

        /// <summary>
        /// CRUD操作记录配置
        /// </summary>
        public CrudLogSettings LogCrud { get; set; } = new CrudLogSettings();

        /// <summary>
        /// 要记录的特定表名列表
        /// </summary>
        public List<string> LogSpecificTables { get; set; } = new List<string>();
    }

    /// <summary>
    /// CRUD操作记录配置
    /// </summary>
    public class CrudLogSettings
    {
        /// <summary>
        /// 是否记录查询操作
        /// </summary>
        public bool Select { get; set; } = true;

        /// <summary>
        /// 是否记录插入操作
        /// </summary>
        public bool Insert { get; set; } = true;

        /// <summary>
        /// 是否记录更新操作
        /// </summary>
        public bool Update { get; set; } = true;

        /// <summary>
        /// 是否记录删除操作
        /// </summary>
        public bool Delete { get; set; } = true;
    }
}