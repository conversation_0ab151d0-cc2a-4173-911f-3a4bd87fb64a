﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MaterialManagement
{
    /// <summary>
    /// 节目投放表
    /// </summary>
    [SugarTable("MM_AdCampaign")]
    public class MMAdCampaign
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int CampaignID { get; set; }
        public string CampaignName { get; set; }
        public int PlaylistID { get; set; }
        /// <summary>
        /// 投放节目
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(PlaylistID))]//一对一
        public MMPlaylist MMPlayEntity { get; set; }
        public int? DeviceID { get; set; }
        /// <summary>
        /// 投放设备
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(CampaignID), nameof(MMDevice.DeviceID))]
        public List<MMDevice> MMDeviceList { get; set; }
        //public List<MMPlaybackDevice> MMPlaybackDeviceList { get; set; }
        /// <summary>
        /// 投放布局模板
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(LayoutID))]//一对一
        public MMLayoutTemplate MMLayoutTemplateEntity { get; set; }
        public int LayoutID { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool IsActive { get; set; }
        public string? DisabledBy { get; set; }
        public DateTime? DisabledTime { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedTime { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedTime { get; set; }
        public bool IsItDaily { get; set; }
        public string? CampaignVersion { get; set; }
    }

    /// <summary>
    /// 投放设备表
    /// </summary>
    [SugarTable("MM_PlaybackDevice")]
    public class MMPlaybackDevice
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int PlaybackDeviceID { get; set; }
        /// <summary>
        /// 投放任务
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(CampaignID))]//一对一
        public MMAdCampaign mMAdCampaign { get; set; }
        public int CampaignID { get; set; }
        public int DeviceID { get; set; }

    }
}
