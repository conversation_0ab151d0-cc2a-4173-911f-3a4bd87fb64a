using HdProject.Application.Services.Interfaces;
using HdProject.Common.Config;
using HdProject.Common.DTOs;
using HdProject.Domain.Entities;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;

namespace HdProject.Application.Services
{
    public class JwtService : IJwtService
    {
        private readonly JwtSettings _jwtSettings;

        public JwtService(IOptions<JwtSettings> jwtSettings)
        {
            _jwtSettings = jwtSettings.Value;
        }

        public TokenResponseDto GenerateToken(User user)
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var roles = !string.IsNullOrEmpty(user.Roles)
                ? user.Roles.Split(',')
                : new string[] { };

            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.UniqueName, user.UserName),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.Name, user.UserName)
            }.Concat(roles.Select(role => new Claim(ClaimTypes.Role, role))).ToArray();

            var accessTokenExpires = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes);
            var refreshTokenExpires = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpirationDays);

            var accessToken = new JwtSecurityToken(
                issuer: _jwtSettings.Issuer,
                audience: _jwtSettings.Audience,
                claims: claims,
                expires: accessTokenExpires,
                signingCredentials: credentials
            );

            var refreshClaims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            var refreshToken = new JwtSecurityToken(
                issuer: _jwtSettings.Issuer,
                audience: _jwtSettings.Audience,
                claims: refreshClaims,
                expires: refreshTokenExpires,
                signingCredentials: credentials
            );

            return new TokenResponseDto
            {
                AccessToken = new JwtSecurityTokenHandler().WriteToken(accessToken),
                ExpiresAt = accessTokenExpires,
                UserName = user.UserName,
                Roles = roles,
                RefreshToken = new JwtSecurityTokenHandler().WriteToken(refreshToken),
                RefreshTokenExpiresAt = refreshTokenExpires
            };
        }

        public ClaimsPrincipal ValidateToken(string token)
        {
            return ValidateTokenInternal(token, true);
        }

        public ClaimsPrincipal ValidateRefreshToken(string refreshToken)
        {
            return ValidateTokenInternal(refreshToken, false);
        }

        private ClaimsPrincipal ValidateTokenInternal(string token, bool validateLifetime)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSettings.SecretKey);
            
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _jwtSettings.Issuer,
                ValidateAudience = true,
                ValidAudience = _jwtSettings.Audience,
                ValidateLifetime = validateLifetime,
                ClockSkew = TimeSpan.Zero
            };

            try
            {
                var principal = tokenHandler.ValidateToken(token, validationParameters, out _);
                return principal;
            }
            catch
            {
                return null;
            }
        }

        public async Task<ClaimsPrincipal> RefreshClaims(ClaimsPrincipal principal)
        {
            // 提取用户信息
            var userIdClaim = principal.FindFirst(JwtRegisteredClaimNames.Sub);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return null;
            }

            // 这里可以添加逻辑以从数据库或其他服务获取最新的用户信息
            // 例如，通过依赖注入一个用户服务来获取最新的用户角色和声明
            // 由于当前实现中没有用户服务，我们将返回原始 principal
            // 在实际应用中，可以添加更多逻辑来刷新声明

            return principal;
        }
    }
}