﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.Commission.AssignEmployee;
using HdProject.Domain.Context.SaasPos.Commission.AssignRecommend;
using SqlSugar;

namespace HdProject.Domain.Context.SaasPos.Commission.AssignShowings
{
    /// <summary>
    /// 指派看房
    /// </summary>
    public class AssignShowingsDto
    {
        public int AssignID { get; set; }

        /// <summary>
        /// 房间号
        /// </summary>
        public string? RmNo { get; set; }

        /// <summary>
        /// 账单号
        /// </summary>
        public string? BillNumber { get; set; }

        /// <summary>
        /// 操作员ID
        /// </summary>
        public string? OperatorID { get; set; }

        /// <summary>
        /// 操作员名称
        /// </summary>
        public string? OperatorName { get; set; }
        public int ShopID { get; set; }

        /// <summary>
        /// 代订人ID
        /// </summary>
        public string? BookingAgentID { get; set; }

        /// <summary>
        /// 代订人名称
        /// </summary>
        public string? BookingAgentName { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreatedTime { get; set; }

        public string? IsCz { get; set; }

        public string? IsJz { get; set; }

        public List<AssignEmployeeDto> assignEmployeeList { get; set; }
        public List<AssignRecommendDto> assignRecommends { get; set; }
    }
}
