﻿using HdProject.Domain.Context.FileManagement;
using HdProject.Domain.DTOs.FileManagement;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;

namespace HdProject.Common.Utility
{
    /// <summary>
    /// 文件管理类
    /// </summary>
    public class FileManagement
    {
        private readonly IWebHostEnvironment _env;
        private readonly IConfiguration _configuration;
        public FileManagement(IWebHostEnvironment env,
            IConfiguration configuration)
        {
            _env = env;
            _configuration = configuration;
        }

        /// <summary>
        /// 上传素材
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<FileManagementResponseDto> FileUploadAsync(List<IFormFile> files, bool IsUpPath = false)
        {
            var responseDto = new FileManagementResponseDto()
            {
                FailedFiles = new List<string>(),//素材上传返回提示信息
                FileEntity = new List<FileManagementDto>()//素材返回对象集合
            };
            var uploadFolder = _configuration["FileUpload:UploadFolder"] ?? "uploads";
            if (IsUpPath)//需要变更路径则进入
            {
                uploadFolder = _configuration["FileUpload:WallPaintingUploadFolder"] ?? "wpuploads";
            }
            var maxFileSize = _configuration.GetValue<long>("FileUpload:MaxFileSize", 1024 * 1024 * 1024); //1G
            var allowedImageTypes = _configuration.GetSection("FileUpload:AllowedImageTypes").Get<List<string>>();
            var allowedVideoTypes = _configuration.GetSection("FileUpload:AllowedVideoTypes").Get<List<string>>();
            var allowedAudioTypes = _configuration.GetSection("FileUpload:AllowedAudioTypes").Get<List<string>>();
            foreach (var file in files)
            {
                try
                {
                    // 验证文件大小
                    if (file.Length > maxFileSize)
                    {
                        responseDto.FailedFiles.Add($"{file.FileName} - 文件大小超过限制");
                        continue;
                    }

                    var fileType = file.ContentType.ToLower();
                    var fileExtension = Path.GetExtension(file.FileName).ToLower();

                    // 验证文件类型
                    if (!IsAllowedFileType(fileType, fileExtension, allowedImageTypes, allowedVideoTypes, allowedAudioTypes))
                    {
                        responseDto.FailedFiles.Add($"{file.FileName} - 不支持的文件类型");
                        continue;
                    }

                    // 创建上传目录
                    var uploadPath = Path.Combine(_env.WebRootPath, uploadFolder);
                    if (!Directory.Exists(uploadPath))
                    {
                        Directory.CreateDirectory(uploadPath);
                    }

                    // 生成唯一文件名
                    var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                    var filePath = Path.Combine(uploadFolder, uniqueFileName);
                    var fullPath = Path.Combine(_env.WebRootPath, filePath);

                    // 保存文件
                    using (var stream = new FileStream(fullPath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }

                    // 处理图片/视频的额外信息
                    string? thumbnailPath = null;
                    int? width = null;
                    int? height = null;
                    string? duration = null;

                    if (fileType.StartsWith("image/"))
                    {
                        // 生成缩略图
                        thumbnailPath = await GenerateThumbnailAsync(fullPath, uploadFolder);

                        // 获取图片尺寸
                        using (var image = await Image.LoadAsync(fullPath))
                        {
                            width = image.Width;
                            height = image.Height;
                        }
                    }
                    // 将数据存到响应对象
                    responseDto.FileEntity.Add(new FileManagementDto
                    {
                        FileName = file.FileName,
                        FilePath = filePath,
                        FormatType = fileType,
                        ThumbnailPath = thumbnailPath,
                        FileSize = file.Length,
                        Width = width,
                        Height = height,
                    });
                }
                catch (Exception ex)
                {

                    throw new Exception($"素材名为{file.FileName}的素材，上传失败：" + ex.Message); ;
                }
            }

            return responseDto;
        }
        /// <summary>
        /// 判断素材类型
        /// </summary>
        /// <param name="fileType"></param>
        /// <param name="fileExtension"></param>
        /// <param name="allowedImageTypes"></param>
        /// <param name="allowedVideoTypes"></param>
        /// <param name="allowedAudioTypes"></param>
        /// <returns></returns>

        private bool IsAllowedFileType(string fileType, string fileExtension, List<string> allowedImageTypes, List<string> allowedVideoTypes, List<string> allowedAudioTypes)
        {
            if (fileType.StartsWith("image/"))
            {
                return allowedImageTypes?.Contains(fileType) == true ||
                       allowedImageTypes?.Contains(fileExtension) == true;
            }
            else if (fileType.StartsWith("video/"))
            {
                return allowedVideoTypes?.Contains(fileType) == true ||
                       allowedVideoTypes?.Contains(fileExtension) == true;
            }
            else if (fileType.StartsWith("audio/"))
            {
                return allowedAudioTypes?.Contains(fileType) == true ||
                       allowedAudioTypes?.Contains(fileExtension) == true;
            }

            return false;
        }

        /// <summary>
        /// 生成缩略图
        /// </summary>
        /// <param name="originalFilePath"></param>
        /// <param name="uploadFolder"></param>
        /// <returns></returns>

        public async Task<string?> GenerateThumbnailAsync(string originalFilePath, string uploadFolder)
        {
            try
            {
                var thumbnailSize = _configuration.GetValue<int>("FileUpload:ThumbnailSize", 200);
                var thumbnailFolder = Path.Combine(uploadFolder, "thumbnails");
                var fullThumbnailFolder = Path.Combine(_env.WebRootPath, thumbnailFolder);

                if (!Directory.Exists(fullThumbnailFolder))
                {
                    Directory.CreateDirectory(fullThumbnailFolder);
                }

                var thumbnailFileName = $"thumb_{Path.GetFileName(originalFilePath)}";
                var thumbnailPath = Path.Combine(thumbnailFolder, thumbnailFileName);
                var fullThumbnailPath = Path.Combine(_env.WebRootPath, thumbnailPath);

                using (var image = await Image.LoadAsync(originalFilePath))
                {
                    var resizeOptions = new ResizeOptions
                    {
                        Size = new Size(thumbnailSize, thumbnailSize),
                        Mode = ResizeMode.Max
                    };

                    image.Mutate(x => x.Resize(resizeOptions));
                    await image.SaveAsync(fullThumbnailPath);
                }

                return thumbnailPath;
            }
            catch
            {
                // 生成缩略图失败不影响主流程
                return null;
            }
        }
    }
}
