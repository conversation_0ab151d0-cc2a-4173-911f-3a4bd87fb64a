﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SongBase.WallPainting
{
    /// <summary>
    /// 主题表
    /// </summary>
    /// 
    [SugarTable("WP_TopicI")]
    public class WPTopicI
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int TopicID { get; set; }
        public string TopicName { get; set; }
        public int TemplateID { get; set; }
        public string? Remarks { get; set; }
        public string? DisabledBy { get; set; }
        public DateTime? DisabledTime { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedTime { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedTime { get; set; }
        public int? FileID { get; set; }
        public bool IsActive { get; set; }

        // 导航属性
        [SugarColumn(IsIgnore = true)]
        public WPTopicITemplate wPTopicITemplate { get; set; }
        [SugarColumn(IsIgnore = true)]
        public WPTopicIFile wPTopicIFile { get; set; }
    }
}
