﻿
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.WebApi;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement
{
    /// <summary>
    /// 投放任务接口控制器
    /// </summary>
    public class MMAdCampaignController : PublicControllerBase
    {
        private readonly IMMAdCampaignService _mMAdCampaignService;
        private readonly ILogger<MMAdCampaignController> _logger;
        public MMAdCampaignController(IMMAdCampaignService mMAdCampaignService, ILogger<MMAdCampaignController> logger)
        {
            _mMAdCampaignService = mMAdCampaignService;
            _logger = logger;
        }

        /// <summary>
        /// 查询投放任务全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllMMAdCampaign([FromQuery] MMAdCampaignGetAllAsyncRequestDto request)
        {
            var result = await _mMAdCampaignService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询投放任务的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdMMAdCampaign([FromQuery] MMAdCampaignGetByIdAsyncRequestDto request)
        {
            var result = await _mMAdCampaignService.GetByIdAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 新增投放任务的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> MMAdCampaignAddValue([FromBody] MMAdCampaignAddRequestDto request)
        {
            var result = await _mMAdCampaignService.AddAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 修改投放任务的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> MMAdCampaignUpdateValue([FromBody] MMAdCampaignUpdateRequestDto request)
        {

            var result = await _mMAdCampaignService.UpdateAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }

        }


        /// <summary>
        /// 修改投放任务绑定设备的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("UpdateLaunchDevice")]
        public async Task<IActionResult> MMUpdateLaunchDevice([FromBody] MMAdCampaignUpdateLaunchDeviceRequestDto request)
        {
            var result = await _mMAdCampaignService.UpdateLaunchDeviceAsyns(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }
        /// <summary>
        /// 删除投放任务的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> MMAdCampaignDeletedValue([FromBody] MMAdCampaignDeleteRequestDto request)
        {
            var result = await _mMAdCampaignService.DeleteAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }


        /// <summary>
        /// 根据ID投放任务详情信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetDetails")]
        public async Task<IActionResult> GetCampaignDetails([FromQuery] MMAdCampaignGetCampaignDetailsAsyncRequestDto request)
        {
            var result = await _mMAdCampaignService.GetCampaignDetails(request);
            return ApiData(result.Model);
        }
    }
}
