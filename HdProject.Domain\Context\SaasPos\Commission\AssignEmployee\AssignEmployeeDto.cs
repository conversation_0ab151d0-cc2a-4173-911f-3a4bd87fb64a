﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Context.SaasPos.Commission.AssignEmployee
{
    /// <summary>
    /// 员工指派
    /// </summary>
    public class AssignEmployeeDto
    {
        /// <summary>
        /// 员工指派ID，主键自增
        /// </summary>
        public int AEID { get; set; }

        /// <summary>
        /// 关联指派看房ID，外键
        /// </summary>
        public int AssignID { get; set; }

        /// <summary>
        /// 员工编码
        /// </summary>
        public string? EmployeeID { get; set; }

        /// <summary>
        /// 员工名称
        /// </summary>
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 是否享受提成，不可为空
        /// </summary>
        public bool IsCom { get; set; }
        /// <summary>
        /// 是否未看房
        /// </summary>
        public bool? IsUnviewedProperty { get; set; }
        public List<List<AssignEmployeeListDto>>? wechat { get; set; }
    }
}
