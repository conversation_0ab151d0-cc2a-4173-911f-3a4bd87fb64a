﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.Commission.CommissionRpt;
using HdProject.Domain.DTOs.SaasPos.Commission.CommissionRpt;

namespace HdProject.Application.Services.Interfaces.SaasPos.Commission
{
    /// <summary>
    /// 看房提成报表接口
    /// </summary>
    public interface IAssignCommissionRptService
    {
        /// <summary>
        /// 根据日期查询提成报表统计信息
        /// </summary>
        /// <returns></returns>
        Task<CommissionRptResponseDto> GetRptAllAsync(CommissionRptRequestDto requestDto);

    }
}
