﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SysService;
using HdProject.Domain.DTOs.SysService;

namespace HdProject.Application.Services.Interfaces.SysService
{
    /// <summary>
    /// 叫号系统业务接口类
    /// </summary>
    public interface IQueuingSystemService
    {
        /// <summary>
        /// 拿到设备key与叫号日期，生成编码
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<CallNumberResponseDto> GenerateNumberAsync(CallNumberRequestDto request);
    }
}
