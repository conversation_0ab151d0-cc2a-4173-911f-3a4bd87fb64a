﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Furion.DatabaseAccessor;
using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;
using HdProject.Domain.Entities.MainFood.Room;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Interfaces;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace HdProject.Application.Services.MainFood
{
    /// <summary>
    /// 房间
    /// </summary>
    public class RoomService : IRoomService
    {
        private readonly IRepositoryMainFood<Room> _repositoryMainFood;
        private readonly IMapper _mapper;
        public RoomService(IRepositoryMainFood<Room> repositoryMainFood, IMapper mapper)
        {
            _repositoryMainFood = repositoryMainFood;
            _mapper = mapper;
        }
        /// <summary>
        /// 删除房间信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RoomDeleteResponseDto> DeleteAsync(RoomDeleteRequestDto requestDto)
        {
            try
            {
                RoomDeleteResponseDto responseDto = new RoomDeleteResponseDto();
                var result = await _repositoryMainFood.DeleteAsync(it => it.RmNo.Equals(requestDto.RoomID));
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("删除操作失败！");
            }
        }

        /// <summary>
        /// 根据编码查询房间信息/查询全部房间信息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RoomGetAllAsyncResponseDto> GetAllAsync(RoomGetAllAsyncRequestDto requestDto)
        {
            RoomGetAllAsyncResponseDto responseDto = new RoomGetAllAsyncResponseDto();
            //var resultModel = await _repositoryMainFood.GetListAsync(it => it.InvNo.Contains(requestDto.QueryCriteria));
            //调用查询方法
            var result = await _repositoryMainFood.GetPageListAsync(requestDto, it => it.RmName.Contains(requestDto.QueryCriteria));
            var model = _mapper.Map<List<RoomDto>>(result);//使用AutoMapper进行对象属性映射
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 根据ID查询房间信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RoomGetByIdAsyncResponseDto> GetByIdAsync(RoomGetByIdAsyncRequestDto requestDto)
        {
            RoomGetByIdAsyncResponseDto responseDto = new RoomGetByIdAsyncResponseDto();
            //调用查询方法
            var result = await _repositoryMainFood.GetByIdAsync(requestDto.RmNo);
            if (result == null) throw new Exception("查询房间信息失败！");
            var model = _mapper.Map<RoomDto>(result);//使用AutoMapper进行对象属性映射
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 新增房间信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RoomAddResponseDto> AddAsync(RoomAddRequestDto requestDto)
        {
            try
            {
                RoomAddResponseDto responseDto = new RoomAddResponseDto();
                var resultModel = _mapper.Map<Room>(requestDto.Model);//使用AutoMapper进行对象属性映射
                resultModel.Rowguid = Guid.NewGuid(); //插入时自动生成新的Guid
                resultModel.MsreplTranVersion = Guid.NewGuid();
                //调用新增方法
                var result = await _repositoryMainFood.InsertAsync(resultModel);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("新增操作失败！");
            }
        }
        /// <summary>
        /// 修改房间信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RoomUpdateResponseDto> UpdateAsync(RoomUpdateRequestDto requestDto)
        {
            try
            {
                RoomUpdateResponseDto responseDto = new RoomUpdateResponseDto();
                var resultModel = _mapper.Map<Room>(requestDto.Model);//使用AutoMapper进行对象属性映射
                var existing = await _repositoryMainFood.GetFirstAsync(r => r.RmNo == resultModel.RmNo);
                // 保留原有rowguid
                resultModel.Rowguid = existing.Rowguid;
                resultModel.MsreplTranVersion = existing.MsreplTranVersion;
                var result = await _repositoryMainFood.UpdateAsync(resultModel);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("修改操作失败！");
            }
        }
    }
}
