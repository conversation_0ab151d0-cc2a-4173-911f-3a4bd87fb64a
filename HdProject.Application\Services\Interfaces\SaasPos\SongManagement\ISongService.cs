﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context;
using HdProject.Domain.Context.SongManagement;
using HdProject.Domain.DTOs;
using HdProject.Domain.DTOs.SongManagement;

namespace HdProject.Application.Services.Interfaces.SaasPos.SongSyncCenter
{
    public  interface ISongService
    {
        /// <summary>
        /// 单曲录入
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SingleSongEntryDto> SingleSongEntry(SingleSongEntryContext context);
        /// <summary>
        /// 批量导入歌曲
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SongDto> BatchImportSong(SongContext context);
        /// <summary>
        /// 歌曲查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SongDto> SongQuery(SongContext context);
        /// <summary>
        /// 歌曲编辑
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SongDto> SongEditing(SongContext context);
        /// <summary>
        /// 歌曲删除(软删除）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SongDto> SongDeletion(SongContext context);
    }
}
