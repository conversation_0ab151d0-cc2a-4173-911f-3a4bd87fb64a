﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;
using HdProject.Domain.Entities.MainFood.Room;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.MainFood
{
    /// <summary>
    /// 房间配送接口实现类
    /// </summary>
    public class RtAutoService : IRtAutoService
    {
        private readonly IRepositoryMainFood<RtAuto> _repositoryMainFood;
        private readonly IMapper _mapper;
        public RtAutoService(IRepositoryMainFood<RtAuto> repositoryMainFood, IMapper mapper)
        {
            _repositoryMainFood = repositoryMainFood;
            _mapper = mapper;
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RtAutoAddResponseDto> AddAsync(RtAutoAddRequestDto requestDto)
        {
            try
            {
                RtAutoAddResponseDto responseDto = new RtAutoAddResponseDto();
                //var resultModel = _mapper.Map<RtAuto>(requestDto.Model);//使用AutoMapper进行对象属性映射
                var resultModel = new RtAuto
                {
                    IKey = requestDto.Model.IKey,
                    RtNo = requestDto.Model.RtNo,
                    FdQty = requestDto.Model.FdQty,
                    FdNo = requestDto.Model.FdNo,
                    CashType = requestDto.Model.CashType,
                    CashUserId = requestDto.Model.CashUserId
                };
                //resultModel.Rowguid = Guid.NewGuid(); //插入时自动生成新的Guid
                resultModel.MsreplTranVersion = Guid.NewGuid();
                //调用新增方法
                var result = await _repositoryMainFood.InsertAsync(resultModel);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("新增操作失败！");
            }
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RtAutoDeleteResponseDto> DeleteAsync(RtAutoDeleteRequestDto requestDto)
        {
            try
            {
                RtAutoDeleteResponseDto responseDto = new RtAutoDeleteResponseDto();
                var result = await _repositoryMainFood.DeleteAsync(it => it.IKey.Equals(requestDto.RtAutoID));
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("删除操作失败！");
            }
        }
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>

        public async Task<RtAutoGetAllAsyncResponseDto> GetAllAsync(RtAutoGetAllAsyncRequestDto requestDto)
        {
            RtAutoGetAllAsyncResponseDto responseDto = new RtAutoGetAllAsyncResponseDto();
            //调用查询方法
            var result = await _repositoryMainFood.GetPageListAsync(requestDto, it => it.IKey.Equals(requestDto.QueryCriteria));
            var model = result.Select(r => new RtAutoDto
            {
                IKey = r.IKey,
                RtNo = r.RtNo,
                FdQty = r.FdQty,
                FdNo = r.FdNo,
                CashType = r.CashType,
                CashUserId = r.CashUserId,
                MsreplTranVersion = r.MsreplTranVersion
            }).ToList();
            //var model = _mapper.Map<List<RtAutoDto>>(result);//使用AutoMapper进行对象属性映射
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RtAutoGetByIdAsyncResponseDto> GetByIdAsync(RtAutoGetByIdAsyncRequestDto requestDto)
        {
            RtAutoGetByIdAsyncResponseDto responseDto = new RtAutoGetByIdAsyncResponseDto();
            //调用查询方法
            var result = await _repositoryMainFood.GetByIdAsync(requestDto.IKey);
            if (result == null) throw new Exception("查询信息失败！");
            //var model = _mapper.Map<RtAutoDto>(result);//使用AutoMapper进行对象属性映射
            var model = new RtAutoDto
            {
                IKey = result.IKey,
                RtNo = result.RtNo,
                FdQty = result.FdQty,
                FdNo = result.FdNo,
                CashType = result.CashType,
                CashUserId = result.CashUserId,
                MsreplTranVersion = result.MsreplTranVersion
            };
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>

        public async Task<RtAutoUpdateResponseDto> UpdateAsync(RtAutoUpdateRequestDto requestDto)
        {
            try
            {
                RtAutoUpdateResponseDto responseDto = new RtAutoUpdateResponseDto();
                //var resultModel = _mapper.Map<RtAuto>(requestDto.Model);//使用AutoMapper进行对象属性映射
                var resultModel = new RtAuto
                {
                    IKey = requestDto.Model.IKey,
                    RtNo = requestDto.Model.RtNo,
                    FdQty = requestDto.Model.FdQty,
                    FdNo = requestDto.Model.FdNo,
                    CashType = requestDto.Model.CashType,
                    CashUserId = requestDto.Model.CashUserId
                };
                var existing = await _repositoryMainFood.GetFirstAsync(r => r.IKey == requestDto.Model.IKey);

                // 保留原有rowguid
                //resultModel.Rowguid = existing.Rowguid;
                resultModel.MsreplTranVersion = existing.MsreplTranVersion;
                var result = await _repositoryMainFood.UpdateAsync(resultModel);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("修改操作失败！");
            }
        }
    }
}
