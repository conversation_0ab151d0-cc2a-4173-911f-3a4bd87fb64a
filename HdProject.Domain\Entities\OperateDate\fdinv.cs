﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Entities.OperateDate
{
    public class fdinv
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Ikey { get; set; }
        public int ShopId { get; set; }
        public string InvNo { get; set; }
        //public string Ikey { get; set; }
        public string RmNo { get; set; }
        public string BookDate { get; set; }
    
        public string BookTime { get; set; }
        public string InDate { get; set; }
        public string InTime { get; set; }
        public short? InNumbers { get; set; }
        public string MemberNo { get; set; }
        public string MemberName { get; set; }
        public string OpenUserId { get; set; }
        public string OpenUserName { get; set; }
        public string AccUserId { get; set; }
        public string AccUserName { get; set; }
        public string AccDate { get; set; }
        public string AccTime { get; set; }
        public string CustName { get; set; }
        public string OrderUserId { get; set; }
        public string OrderUserName { get; set; }
        public int? DiscRate { get; set; }
        public string OutDate { get; set; }
        public string OutTime { get; set; }
        public string CloseUserId { get; set; }
        public string CloseUserName { get; set; }
        public string Rem { get; set; }
        public int? FdCost { get; set; }
        public int? RmCost { get; set; }
        public int ZD { get; set; }
        public int BeerZD { get; set; }
        public int BeerCash { get; set; }
        public int? Serv { get; set; }
        public int? Disc { get; set; }
        public int Tax { get; set; }
        public int? Tot { get; set; }
        public int? Cash { get; set; }
        public int Cash_Targ { get; set; }
        public int? Vesa { get; set; }
        public string VesaName { get; set; }
        public int Vesa_Targ { get; set; }
        public string VesaName_Targ { get; set; }
        public int? GZ { get; set; }
        public int? AccOkZD { get; set; }
        public int? MembCard { get; set; }
        public int? NoPayed { get; set; }
        public string WorkDate { get; set; }
        public bool? Void { get; set; }
        public string VoidUserId { get; set; }
        public string VoidUserName { get; set; }
        public bool? GZOk { get; set; }
        public string GZOkDate { get; set; }
        public string GZOkTime { get; set; }
        public int? MorePayed { get; set; }
        public bool? Booked { get; set; }
        public string CardConsumeMNo { get; set; }
        public string CardConsumeMName { get; set; }
        public string VesaNo { get; set; }
        public string VesaNo_Targ { get; set; }
        public string GZName { get; set; }
        public int? FixedDisc { get; set; }
        public Guid? msrepl_tran_version { get; set; }
        public int? th_RmCost { get; set; }
        public string CustomerServiceManagerName { get; set; }
        public string CustomerServiceManagerID { get; set; }
        public string AccountManagerCName { get; set; }
        public string AccountManagerID { get; set; }
        public int SubServ { get; set; }
        public int IntegralRule { get; set; }
        public int ReturnScale { get; set; }
        public int IntegralValue { get; set; }
        public int ReturnValue { get; set; }
        public bool NonMember { get; set; }
        public int ReturnStatus { get; set; }
        public int PointRule { get; set; }
        public int PointValue { get; set; }
        public int MemberCardTypeNo { get; set; }
    }
}
