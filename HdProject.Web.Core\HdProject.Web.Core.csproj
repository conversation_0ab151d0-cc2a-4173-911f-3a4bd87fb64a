﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
	  <ManagePackageVersionsCentrally>false</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
	</ItemGroup>

	<ItemGroup>
    <ProjectReference Include="..\HdProject.Application\HdProject.Application.csproj" />
  </ItemGroup>

</Project>
