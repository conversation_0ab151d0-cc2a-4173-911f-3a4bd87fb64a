﻿using HdProject.Domain.WebApi;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace HdProject.Web.Api.Core.Utility.Filters
{
    public class GlobalExceptionFilter : Attribute, IExceptionFilter
    {
        public void OnException(ExceptionContext context)
        {
            context.Result = new JsonResult(new
            {
                code = StatusCodes.Status200OK,
                message = context.Exception.Message,
                state = ResultType.error.ToString(),
                detail = "服务器内部错误"
            })
            {
                StatusCode = StatusCodes.Status200OK
            };

            context.ExceptionHandled = true; // 标记异常已处理
        }
    }
}
