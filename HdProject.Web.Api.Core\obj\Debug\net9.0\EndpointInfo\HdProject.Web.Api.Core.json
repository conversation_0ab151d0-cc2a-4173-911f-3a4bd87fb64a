{"openapi": "3.0.4", "info": {"title": "HD Project API", "description": "HD Project Web API", "version": "v1"}, "paths": {"/api/AssignEmployee/GetAll": {"get": {"tags": ["AssignEmployee"], "parameters": [{"name": "request", "in": "query", "schema": {"$ref": "#/components/schemas/AssignEmployeeGetAllRequestDto"}}], "responses": {"200": {"description": "OK"}}}}, "/api/AssignShowings/GetByRoom": {"get": {"tags": ["AssignShowings"], "parameters": [{"name": "ShopID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "RmNo", "in": "query", "schema": {"type": "string"}}, {"name": "Bill<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/AssignShowings/GetById": {"get": {"tags": ["AssignShowings"], "parameters": [{"name": "AssignID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/AssignShowings/Add": {"post": {"tags": ["AssignShowings"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignShowingsAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignShowingsAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignShowingsAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/AssignShowings/Update": {"put": {"tags": ["AssignShowings"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignShowingsUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignShowingsUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignShowingsUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/AssignShowings/Delete": {"delete": {"tags": ["AssignShowings"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignShowingsDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignShowingsDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignShowingsDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/AssignShowings/GetRoomStatus": {"get": {"tags": ["AssignShowings"], "parameters": [{"name": "ShopId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "RmNo", "in": "query", "schema": {"type": "string"}}, {"name": "InvNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/user-id": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/CallNumber/Get": {"get": {"tags": ["CallNumber"], "parameters": [{"name": "Key", "in": "query", "schema": {"type": "string"}}, {"name": "Date", "in": "query", "schema": {"type": "string"}}, {"name": "TypeId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/CommodityAdjustment/GetCommDraftRecord": {"get": {"tags": ["CommodityAdjustment"], "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Keyword", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/CommodityAdjustment/GetCommDraftListRecord": {"get": {"tags": ["CommodityAdjustment"], "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "AdjustmentID", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Keyword", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/CommodityAdjustment/DeleteCommDraftRecord": {"post": {"tags": ["CommodityAdjustment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DelectDraftRecordContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DelectDraftRecordContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DelectDraftRecordContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/CommodityAdjustment/AddOrUpdateCommDraftRecords": {"post": {"tags": ["CommodityAdjustment"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdjustmentRequestContext"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdjustmentRequestContext"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdjustmentRequestContext"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/CommodityAdjustment/GetCommExistingGoods": {"get": {"tags": ["CommodityAdjustment"], "parameters": [{"name": "Keyword", "in": "query", "schema": {"type": "string"}}, {"name": "FtNo", "in": "query", "schema": {"type": "string"}}, {"name": "IsPackage", "in": "query", "schema": {"type": "boolean"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/CommodityAdjustment/GetCommHistoricalPrice": {"get": {"tags": ["CommodityAdjustment"], "parameters": [{"name": "FdNo", "in": "query", "schema": {"type": "string"}}, {"name": "ComboNo", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Keyword", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/CommodityAdjustment/GetCommPublished": {"get": {"tags": ["CommodityAdjustment"], "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "AdjustmentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Keyword", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/CommodityAdjustment/CommSynchronousStore": {"post": {"tags": ["CommodityAdjustment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommSynchronousStoreContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CommSynchronousStoreContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CommSynchronousStoreContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/DbFoodRoom/GetAll": {"get": {"tags": ["DbFoodRoom"], "parameters": [{"name": "AreaID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ShopID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/DbFoodRoom/GetRegionAll": {"get": {"tags": ["DbFoodRoom"], "parameters": [{"name": "ShopId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/MarketingActivity/GetSalesAndCardsInfoRecord": {"get": {"tags": ["MarketingActivity"], "parameters": [{"name": "ActivityName", "in": "query", "schema": {"type": "string"}}, {"name": "CardSheetName", "in": "query", "schema": {"type": "string"}}, {"name": "TypeName", "in": "query", "schema": {"type": "string"}}, {"name": "SalesId", "in": "query", "schema": {"type": "string"}}, {"name": "CardSheetId", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/MarketingActivity/EditorSalesAndCardsInfoRecord": {"post": {"tags": ["MarketingActivity"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Data": {"type": "string"}, "File": {"type": "string", "format": "binary"}}}, "encoding": {"Data": {"style": "form"}, "File": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/MarketingActivity/DeleteSalesAndCardsInfoRecord": {"post": {"tags": ["MarketingActivity"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteSalesAndCardsInfoContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteSalesAndCardsInfoContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteSalesAndCardsInfoContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/MarketingActivity/GetCardSheetListRecord": {"get": {"tags": ["MarketingActivity"], "parameters": [{"name": "CardSheetListName", "in": "query", "schema": {"type": "string"}}, {"name": "CardSheetListId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/MarketingActivity/GetWXQrCodeRecord": {"get": {"tags": ["MarketingActivity"], "parameters": [{"name": "Path", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMAdCampaign/GetAll": {"get": {"tags": ["MMAdCampaign"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMAdCampaign/GetById": {"get": {"tags": ["MMAdCampaign"], "parameters": [{"name": "CampaignID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMAdCampaign/Add": {"post": {"tags": ["MMAdCampaign"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMAdCampaign/Update": {"put": {"tags": ["MMAdCampaign"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMAdCampaign/UpdateLaunchDevice": {"put": {"tags": ["MMAdCampaign"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignUpdateLaunchDeviceRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignUpdateLaunchDeviceRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignUpdateLaunchDeviceRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMAdCampaign/Delete": {"delete": {"tags": ["MMAdCampaign"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMAdCampaignDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMAdCampaign/GetDetails": {"get": {"tags": ["MMAdCampaign"], "parameters": [{"name": "CampaignID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMDevice/GetAll": {"get": {"tags": ["MMDevice"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMDevice/GetById": {"get": {"tags": ["MMDevice"], "parameters": [{"name": "DeviceID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMDevice/Add": {"post": {"tags": ["MMDevice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMDeviceAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMDeviceAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMDeviceAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMDevice/SaveWh": {"post": {"tags": ["MMDevice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMDeviceSaveWhRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMDeviceSaveWhRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMDeviceSaveWhRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMDevice/Update": {"put": {"tags": ["MMDevice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMDeviceUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMDeviceUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMDeviceUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMDevice/Delete": {"delete": {"tags": ["MMDevice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMDeviceDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMDeviceDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMDeviceDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMDevice/GetDetails": {"get": {"tags": ["MMDevice"], "parameters": [{"name": "DeviceID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMDevice/UpdateLaunchCampaign": {"put": {"tags": ["MMDevice"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMDeviceUpdateLaunchCampaignRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMDeviceUpdateLaunchCampaignRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMDeviceUpdateLaunchCampaignRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMFile/GetAll": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMFile/GetById": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "FileID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMFile/Upload": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMFile/Update": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMFileUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMFileUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMFileUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMFile/Delete": {"delete": {"tags": ["<PERSON><PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMFileDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMFileDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMFileDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMFolder/Add": {"post": {"tags": ["MMFolder"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMFolderAddFolderRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMFolderAddFolderRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMFolderAddFolderRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMFolder/GetAll": {"get": {"tags": ["MMFolder"], "parameters": [{"name": "request", "in": "query", "schema": {"$ref": "#/components/schemas/MMFolderGetAllFolderRequestDto"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMFolder/GetById": {"get": {"tags": ["MMFolder"], "parameters": [{"name": "FolderID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMLayoutTemplate/GetAll": {"get": {"tags": ["MMLayoutTemplate"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMLayoutTemplate/GetById": {"get": {"tags": ["MMLayoutTemplate"], "parameters": [{"name": "LayoutID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMLayoutTemplate/Add": {"post": {"tags": ["MMLayoutTemplate"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMLayoutTemplate/Update": {"put": {"tags": ["MMLayoutTemplate"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMLayoutTemplate/Delete": {"delete": {"tags": ["MMLayoutTemplate"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMLayoutTemplateDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMPlaylist/GetAll": {"get": {"tags": ["MMPlaylist"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMPlaylist/GetById": {"get": {"tags": ["MMPlaylist"], "parameters": [{"name": "PlaylistID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MMPlaylist/Add": {"post": {"tags": ["MMPlaylist"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMPlaylistAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMPlaylistAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMPlaylistAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMPlaylist/Update": {"put": {"tags": ["MMPlaylist"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMPlaylistUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMPlaylistUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMPlaylistUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MMPlaylist/Delete": {"delete": {"tags": ["MMPlaylist"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MMPlaylistDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MMPlaylistDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MMPlaylistDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MTopen/authcode": {"get": {"tags": ["MTopen"], "parameters": [{"name": "businessId", "in": "query", "schema": {"type": "string"}}, {"name": "developerId", "in": "query", "schema": {"type": "string"}}, {"name": "code", "in": "query", "schema": {"type": "string"}}, {"name": "state", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/Refund/GetRefundRecord": {"get": {"tags": ["Refund"], "parameters": [{"name": "PhoneNumber", "in": "query", "schema": {"type": "string"}}, {"name": "NCodeNo", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/Refund/ApplyRefundRecord": {"post": {"tags": ["Refund"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplyRefundContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApplyRefundContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApplyRefundContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Refund/ApplyRefundListRecord": {"get": {"tags": ["Refund"], "parameters": [{"name": "ApplyNo", "in": "query", "schema": {"type": "string"}}, {"name": "ConfirmNo", "in": "query", "schema": {"type": "string"}}, {"name": "ApplyUserID", "in": "query", "schema": {"type": "string"}}, {"name": "IsEnd", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "BeginDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ShopID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/Refund/RefundRecord": {"post": {"tags": ["Refund"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmRefundContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConfirmRefundContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConfirmRefundContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RmAreaManage/GetAll": {"get": {"tags": ["RmAreaManage"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RmAreaManage/GetById": {"get": {"tags": ["RmAreaManage"], "parameters": [{"name": "AreaNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RmAreaManage/Add": {"post": {"tags": ["RmAreaManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RmAreaAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RmAreaAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RmAreaAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RmAreaManage/Update": {"put": {"tags": ["RmAreaManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RmAreaUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RmAreaUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RmAreaUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RmAreaManage/Delete": {"delete": {"tags": ["RmAreaManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RmAreaDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RmAreaDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RmAreaDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RmInfo/GetList": {"get": {"tags": ["RmInfo"], "responses": {"200": {"description": "OK"}}}}, "/api/RoomManage/GetAll": {"get": {"tags": ["RoomManage"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RoomManage/GetById": {"get": {"tags": ["RoomManage"], "parameters": [{"name": "RmNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RoomManage/Add": {"post": {"tags": ["RoomManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoomAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoomAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RoomManage/Update": {"put": {"tags": ["RoomManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoomUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoomUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RoomManage/Delete": {"delete": {"tags": ["RoomManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoomDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoomDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoomDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RoomTypeManage/GetAll": {"get": {"tags": ["RoomTypeManage"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RoomTypeManage/GetById": {"get": {"tags": ["RoomTypeManage"], "parameters": [{"name": "RtNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RoomTypeManage/Add": {"post": {"tags": ["RoomTypeManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RmTypeAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RmTypeAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RmTypeAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RoomTypeManage/Update": {"put": {"tags": ["RoomTypeManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RmTypeUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RmTypeUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RmTypeUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RoomTypeManage/Delete": {"delete": {"tags": ["RoomTypeManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RmTypeDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RmTypeDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RmTypeDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RtAutoManage/GetAll": {"get": {"tags": ["RtAutoManage"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RtAutoManage/GetById": {"get": {"tags": ["RtAutoManage"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RtAutoManage/Add": {"post": {"tags": ["RtAutoManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RtAutoAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RtAutoAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RtAutoAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RtAutoManage/Update": {"put": {"tags": ["RtAutoManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RtAutoUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RtAutoUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RtAutoUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RtAutoManage/Delete": {"delete": {"tags": ["RtAutoManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RtAutoDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RtAutoDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RtAutoDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RtTimePriceManage/GetAll": {"get": {"tags": ["RtTimePriceManage"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RtTimePriceManage/GetById": {"get": {"tags": ["RtTimePriceManage"], "parameters": [{"name": "RtNo", "in": "query", "schema": {"type": "string"}}, {"name": "DayOfWeek", "in": "query", "schema": {"type": "string"}}, {"name": "FromTime", "in": "query", "schema": {"type": "string"}}, {"name": "ToTime", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/RtTimePriceManage/Add": {"post": {"tags": ["RtTimePriceManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RtTimePriceAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RtTimePriceAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RtTimePriceAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RtTimePriceManage/Update": {"put": {"tags": ["RtTimePriceManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RtTimePriceUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RtTimePriceUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RtTimePriceUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/RtTimePriceManage/Delete": {"delete": {"tags": ["RtTimePriceManage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RtTimePriceDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RtTimePriceDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RtTimePriceDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/RegisterAsLeader": {"post": {"tags": ["ShopBookLeader"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterLeaderContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterLeaderContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterLeaderContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/ApplyForLeaderWithdrawal": {"post": {"tags": ["ShopBookLeader"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplyForLeaderWithdrawalContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApplyForLeaderWithdrawalContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApplyForLeaderWithdrawalContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/GetLeaderAppointmentRecords": {"post": {"tags": ["ShopBookLeader"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLeaderAppointmentRecordsContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetLeaderAppointmentRecordsContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetLeaderAppointmentRecordsContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/GetLeaderPersonalInfo": {"post": {"tags": ["ShopBookLeader"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLeaderPersonalInfoContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetLeaderPersonalInfoContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetLeaderPersonalInfoContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/GetLeaderWithdrawalRecords": {"post": {"tags": ["ShopBookLeader"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLeaderWithdrawalRecordsContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetLeaderWithdrawalRecordsContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetLeaderWithdrawalRecordsContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/GetLeaderSummaryReport": {"post": {"tags": ["ShopBookLeader"], "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/GetLeadersInfo": {"post": {"tags": ["ShopBookLeader"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLeaderInfoListContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetLeaderInfoListContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetLeaderInfoListContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/GetLeadersOrderInfo": {"post": {"tags": ["ShopBookLeader"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetOrderInfoListContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetOrderInfoListContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetOrderInfoListContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/ShopBookLeader/GetLeadersAccrueCommission": {"post": {"tags": ["ShopBookLeader"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCommissionRecordsContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetCommissionRecordsContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetCommissionRecordsContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Singer/NewSingerAdded": {"post": {"tags": ["<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Singer/SingerlistInfo": {"get": {"tags": ["<PERSON>"], "parameters": [{"name": "context", "in": "query", "schema": {"$ref": "#/components/schemas/SingerContext"}}], "responses": {"200": {"description": "OK"}}}}, "/Singer/SingerInfoUpdate": {"put": {"tags": ["<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Singer/DeleteSinger": {"put": {"tags": ["<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SingerContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Singer/GetSingerSong": {"get": {"tags": ["<PERSON>"], "parameters": [{"name": "context", "in": "query", "schema": {"$ref": "#/components/schemas/SingerContext"}}], "responses": {"200": {"description": "OK"}}}}, "/Song/SingleSongEntry": {"post": {"tags": ["Song"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SingleSongEntryContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SingleSongEntryContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SingleSongEntryContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Song/BatchImportSong": {"post": {"tags": ["Song"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SongContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SongContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SongContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Song/SongQuery": {"get": {"tags": ["Song"], "parameters": [{"name": "context", "in": "query", "schema": {"$ref": "#/components/schemas/SongContext"}}], "responses": {"200": {"description": "OK"}}}}, "/Song/SongEditing": {"put": {"tags": ["Song"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SongContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SongContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SongContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Song/SongDeletion": {"put": {"tags": ["Song"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SongContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SongContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SongContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/SongFeedback/Add": {"post": {"tags": ["SongFeedback"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SongFeedbackAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SongFeedbackAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SongFeedbackAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/SummaryStoreTimeSlotDaily/GetSummaryStoreTimeSlotDailyRecord": {"get": {"tags": ["SummaryStoreTimeSlotDaily"], "parameters": [{"name": "StoreID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/SummaryStoreTimeSlotDaily/GetSummaryStoreTimeSlotDailyListRecord": {"get": {"tags": ["SummaryStoreTimeSlotDaily"], "parameters": [{"name": "Comedate", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/Transfer/GetSharedCardList": {"get": {"tags": ["Transfer"], "parameters": [{"name": "openid", "in": "query", "schema": {"type": "string"}}, {"name": "IsGive", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CodeStatus", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/Transfer/TransferCardRecord": {"post": {"tags": ["Transfer"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferCardContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferCardContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TransferCardContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Transfer/ClaimRecord": {"post": {"tags": ["Transfer"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimContext"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClaimContext"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClaimContext"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Transfer/GetClaimKeyList": {"get": {"tags": ["Transfer"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/Transfer/GetNoSharedCardList": {"get": {"tags": ["Transfer"], "parameters": [{"name": "openid", "in": "query", "schema": {"type": "string"}}, {"name": "IsGive", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Paging.Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Paging.Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/profile": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/User/admin-only": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/User/test": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "integer", "format": "int32"}}, "application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/WayFoodMap/GetAll": {"get": {"tags": ["WayFoodMap"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AllDates", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "QueryCriteria", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WayFoodMap/Add": {"post": {"tags": ["WayFoodMap"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WayFoodMapAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WayFoodMapAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WayFoodMapAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WayFoodMap/Update": {"put": {"tags": ["WayFoodMap"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WayFoodMapUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WayFoodMapUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WayFoodMapUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WayFoodMap/Import": {"post": {"tags": ["WayFoodMap"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WayFoodMap/Delete": {"delete": {"tags": ["WayFoodMap"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WayFoodMapSingleItemDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WayFoodMapSingleItemDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WayFoodMapSingleItemDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WayFoodMap/GetStoreAll": {"get": {"tags": ["WayFoodMap"], "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicI/GetByUserOpenId": {"get": {"tags": ["WPTopicI"], "parameters": [{"name": "UserOpenID", "in": "query", "schema": {"type": "string"}}, {"name": "Rows", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Sidx", "in": "query", "schema": {"type": "string"}}, {"name": "Sord", "in": "query", "schema": {"type": "string"}}, {"name": "Records", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Total", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicI/GetById": {"get": {"tags": ["WPTopicI"], "parameters": [{"name": "TopicID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicI/Add": {"post": {"tags": ["WPTopicI"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WPTopicIAddRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WPTopicIAddRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WPTopicIAddRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicI/Update": {"put": {"tags": ["WPTopicI"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WPTopicIUpdateRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WPTopicIUpdateRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WPTopicIUpdateRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicI/Delete": {"delete": {"tags": ["WPTopicI"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WPTopicIDeleteRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WPTopicIDeleteRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WPTopicIDeleteRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicI/GetStoreAndPath": {"get": {"tags": ["WPTopicI"], "parameters": [{"name": "Store", "in": "query", "schema": {"type": "string"}}, {"name": "GUID", "in": "query", "schema": {"type": "string"}}, {"name": "Path", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicIFile/Upload": {"post": {"tags": ["WPTopicIFile"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicITemplate/GetAll": {"get": {"tags": ["WPTopicITemplate"], "responses": {"200": {"description": "OK"}}}}, "/api/WPTopicITemplate/GetById": {"get": {"tags": ["WPTopicITemplate"], "parameters": [{"name": "TemplateID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AddDraftRecordContext": {"type": "object", "properties": {"FdCName": {"type": "string", "nullable": true}, "ComboName": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}, "FdQty": {"type": "integer", "format": "int32"}, "FtNo": {"type": "string", "nullable": true}, "FdNo": {"type": "string", "nullable": true}, "ComboNo": {"type": "string", "nullable": true}, "DetailID": {"type": "string", "format": "uuid", "nullable": true}, "MarketPrice": {"type": "number", "format": "double"}, "SalePrice": {"type": "number", "format": "double"}, "PriceMode": {"type": "string", "nullable": true}, "ApplicableStores": {"type": "string", "nullable": true}, "PackageItems": {"type": "array", "items": {"$ref": "#/components/schemas/ComboPackageItemContext"}, "nullable": true}}, "additionalProperties": false}, "AdjustmentRequestContext": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "AdjustmentID": {"type": "string", "format": "uuid", "nullable": true}, "AddDraftRecordList": {"type": "array", "items": {"$ref": "#/components/schemas/AddDraftRecordContext"}, "nullable": true}}, "additionalProperties": false}, "ApplyForLeaderWithdrawalContext": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "Amount": {"type": "number", "format": "double"}, "BankCard": {"type": "string", "nullable": true}, "Title": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApplyRefundContext": {"type": "object", "properties": {"InCodeKey": {"type": "string", "nullable": true}, "Torecharge": {"type": "integer", "format": "int32"}, "ApplyMoney": {"type": "number", "format": "double"}, "ApplyNum": {"type": "integer", "format": "int32"}, "ApplyShopId": {"type": "string", "nullable": true}, "ApplyUserID": {"type": "string", "nullable": true}, "ApplyName": {"type": "string", "nullable": true}, "ApplyReason": {"type": "string", "nullable": true}, "NCodeNo": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AssignEmployeeCreateDto": {"type": "object", "properties": {"AEID": {"type": "integer", "format": "int32"}, "AssignID": {"type": "integer", "format": "int32"}, "EmployeeID": {"type": "string", "nullable": true}, "EmployeeName": {"type": "string", "nullable": true}, "IsCom": {"type": "boolean"}, "IsUnviewedProperty": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "AssignEmployeeGetAllRequestDto": {"type": "object", "additionalProperties": false}, "AssignRecommendCreateDto": {"type": "object", "properties": {"ARID": {"type": "integer", "format": "int32"}, "AssignID": {"type": "integer", "format": "int32"}, "ReferenceID": {"type": "string", "nullable": true}, "ReferenceName": {"type": "string", "nullable": true}, "RecommendedID": {"type": "string", "nullable": true}, "RecommendedName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AssignShowingsAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/AssignShowingsCreateDto"}, "PlaceAnOrdeUser": {"$ref": "#/components/schemas/PlaceAnOrdeUserDto"}}, "additionalProperties": false}, "AssignShowingsCreateDto": {"type": "object", "properties": {"AssignID": {"type": "integer", "format": "int32"}, "RmNo": {"type": "string", "nullable": true}, "BillNumber": {"type": "string", "nullable": true}, "OperatorID": {"type": "string", "nullable": true}, "OperatorName": {"type": "string", "nullable": true}, "ShopID": {"type": "integer", "format": "int32"}, "BookingAgentID": {"type": "string", "nullable": true}, "BookingAgentName": {"type": "string", "nullable": true}, "EmployeeCreateDtos": {"type": "array", "items": {"$ref": "#/components/schemas/AssignEmployeeCreateDto"}, "nullable": true}, "recommendCreateDtos": {"type": "array", "items": {"$ref": "#/components/schemas/AssignRecommendCreateDto"}, "nullable": true}}, "additionalProperties": false}, "AssignShowingsDeleteRequestDto": {"type": "object", "properties": {"AssignID": {"type": "integer", "format": "int32"}, "PlaceAnOrdeUser": {"$ref": "#/components/schemas/PlaceAnOrdeUserDto"}}, "additionalProperties": false}, "AssignShowingsUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/AssignShowingsCreateDto"}, "PlaceAnOrdeUser": {"$ref": "#/components/schemas/PlaceAnOrdeUserDto"}}, "additionalProperties": false}, "ChangePasswordDto": {"type": "object", "properties": {"OldPassword": {"type": "string", "nullable": true}, "NewPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClaimContext": {"type": "object", "properties": {"ClaimKey": {"type": "string", "nullable": true}, "openid": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ComboPackageItemContext": {"type": "object", "properties": {"PGFdNo": {"type": "string", "nullable": true}, "PGFdQty": {"type": "integer", "format": "int32"}, "CommodityPrice": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CommSynchronousStoreContext": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "AdjustmentIDs": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ConfirmRefundContext": {"type": "object", "properties": {"ApplyNo": {"type": "string", "nullable": true}, "ConfirmNo": {"type": "string", "nullable": true}, "ConfirmName": {"type": "string", "nullable": true}, "CheckName": {"type": "string", "nullable": true}, "OpenId": {"type": "string", "nullable": true}, "Transaction_id": {"type": "string", "nullable": true}, "TotalAmount": {"type": "number", "format": "double"}, "RefundType": {"type": "integer", "format": "int32"}, "RefundRemark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DelectDraftRecordContext": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "AdjustmentID": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "DeleteSalesAndCardsInfoContext": {"type": "object", "properties": {"SalesId": {"type": "string", "nullable": true}, "CardSheetId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetCommissionRecordsContext": {"type": "object", "properties": {"SettlementStatus": {"type": "integer", "format": "int32", "nullable": true}, "ConsumeStartTime": {"type": "string", "format": "date-time", "nullable": true}, "ConsumeEndTime": {"type": "string", "format": "date-time", "nullable": true}, "Paging": {"$ref": "#/components/schemas/Pagination"}}, "additionalProperties": false}, "GetLeaderAppointmentRecordsContext": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "Status": {"type": "integer", "format": "int32", "nullable": true}, "AllDates": {"type": "string", "format": "date-time", "nullable": true}, "Paging": {"$ref": "#/components/schemas/Pagination"}}, "additionalProperties": false}, "GetLeaderInfoListContext": {"type": "object", "properties": {"RegisterStartTime": {"type": "string", "format": "date-time", "nullable": true}, "RegisterEndTime": {"type": "string", "format": "date-time", "nullable": true}, "Paging": {"$ref": "#/components/schemas/Pagination"}}, "additionalProperties": false}, "GetLeaderPersonalInfoContext": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetLeaderWithdrawalRecordsContext": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "Status": {"type": "integer", "format": "int32", "nullable": true}, "AllDates": {"type": "string", "format": "date-time", "nullable": true}, "Paging": {"$ref": "#/components/schemas/Pagination"}}, "additionalProperties": false}, "GetOrderInfoListContext": {"type": "object", "properties": {"OrderStatus": {"type": "integer", "format": "int32", "nullable": true}, "SettlementStatus": {"type": "integer", "format": "int32", "nullable": true}, "OrderStartTime": {"type": "string", "format": "date-time", "nullable": true}, "OrderEndTime": {"type": "string", "format": "date-time", "nullable": true}, "Paging": {"$ref": "#/components/schemas/Pagination"}}, "additionalProperties": false}, "MMAdCampaignAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMAdCampaignCreateDto"}}, "additionalProperties": false}, "MMAdCampaignCreateDto": {"type": "object", "properties": {"CampaignID": {"type": "integer", "format": "int32"}, "CampaignName": {"type": "string", "nullable": true}, "PlaylistID": {"type": "integer", "format": "int32"}, "DeviceID": {"type": "integer", "format": "int32", "nullable": true}, "MMDeviceList": {"type": "array", "items": {"$ref": "#/components/schemas/MMDeviceCreateDto"}, "nullable": true}, "MMLayoutTemplateEntity": {"$ref": "#/components/schemas/MMLayoutTemplateCreateDto"}, "LayoutID": {"type": "integer", "format": "int32"}, "StartTime": {"type": "string", "format": "date-time", "nullable": true}, "EndTime": {"type": "string", "format": "date-time", "nullable": true}, "IsItDaily": {"type": "boolean"}}, "additionalProperties": false}, "MMAdCampaignDeleteRequestDto": {"type": "object", "properties": {"CampaignID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MMAdCampaignUpdateLaunchDeviceRequestDto": {"type": "object", "properties": {"CampaignID": {"type": "integer", "format": "int32"}, "CampaignName": {"type": "string", "nullable": true}, "mMPlaybackDevice": {"type": "array", "items": {"$ref": "#/components/schemas/MMPlaybackDeviceCreateDto"}, "nullable": true}}, "additionalProperties": false}, "MMAdCampaignUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMAdCampaignCreateDto"}}, "additionalProperties": false}, "MMDeviceAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMDeviceCreateDto"}}, "additionalProperties": false}, "MMDeviceCreateDto": {"type": "object", "properties": {"DeviceID": {"type": "integer", "format": "int32"}, "DeviceName": {"type": "string", "nullable": true}, "DeviceModel": {"type": "string", "nullable": true}, "DeviceVersion": {"type": "string", "nullable": true}, "DeviceOrientation": {"type": "string", "nullable": true}, "DeviceHeight": {"type": "integer", "format": "int32", "nullable": true}, "DeviceWidth": {"type": "integer", "format": "int32", "nullable": true}, "DeviceResolution": {"type": "string", "nullable": true}, "StoreId": {"type": "integer", "format": "int32", "nullable": true}, "OnlineStatusCode": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "MMDeviceDeleteRequestDto": {"type": "object", "properties": {"DeviceID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MMDeviceSaveWhRequestDto": {"type": "object", "properties": {"DeviceID": {"type": "integer", "format": "int32"}, "Height": {"type": "integer", "format": "int32"}, "Width": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MMDeviceUpdateLaunchCampaignRequestDto": {"type": "object", "properties": {"DeviceID": {"type": "integer", "format": "int32"}, "DeviceName": {"type": "string", "nullable": true}, "mMPlaybackDevice": {"type": "array", "items": {"$ref": "#/components/schemas/MMPlaybackDeviceCreateDto"}, "nullable": true}}, "additionalProperties": false}, "MMDeviceUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMDeviceCreateDto"}}, "additionalProperties": false}, "MMFileCreateDto": {"type": "object", "properties": {"FileID": {"type": "integer", "format": "int32"}, "FileName": {"type": "string", "nullable": true}, "FilePath": {"type": "string", "nullable": true}, "FormatType": {"type": "string", "nullable": true}, "FileSize": {"type": "integer", "format": "int64", "nullable": true}, "VideoDuration": {"type": "number", "format": "float", "nullable": true}}, "additionalProperties": false}, "MMFileDeleteRequestDto": {"type": "object", "properties": {"FileID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MMFileDto": {"type": "object", "properties": {"FileID": {"type": "integer", "format": "int32"}, "FileName": {"type": "string", "nullable": true}, "FilePath": {"type": "string", "nullable": true}, "FormatType": {"type": "string", "nullable": true}, "FileSize": {"type": "integer", "format": "int64", "nullable": true}, "ThumbnailPath": {"type": "string", "nullable": true}, "Width": {"type": "integer", "format": "int32", "nullable": true}, "Height": {"type": "integer", "format": "int32", "nullable": true}, "VideoDuration": {"type": "number", "format": "float", "nullable": true}, "IsActive": {"type": "boolean"}}, "additionalProperties": false}, "MMFileUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMFileCreateDto"}}, "additionalProperties": false}, "MMFolderAddFolderRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMFolderDto"}}, "additionalProperties": false}, "MMFolderDto": {"type": "object", "properties": {"FolderID": {"type": "integer", "format": "int32"}, "FolderName": {"type": "string", "nullable": true}, "mFileDtos": {"type": "array", "items": {"$ref": "#/components/schemas/MMFileDto"}, "nullable": true}}, "additionalProperties": false}, "MMFolderGetAllFolderRequestDto": {"type": "object", "additionalProperties": false}, "MMLayoutRegionCreateDto": {"type": "object", "properties": {"RegionID": {"type": "integer", "format": "int32"}, "LayoutID": {"type": "integer", "format": "int32"}, "RegionName": {"type": "string", "nullable": true}, "StartX": {"type": "integer", "format": "int32", "nullable": true}, "StartY": {"type": "integer", "format": "int32", "nullable": true}, "RegionWidth": {"type": "integer", "format": "int32", "nullable": true}, "RegionHeight": {"type": "integer", "format": "int32", "nullable": true}, "CreatedBy": {"type": "string", "nullable": true}, "HtmlTemplate": {"type": "string", "nullable": true}, "PlaylistDetails": {"$ref": "#/components/schemas/MMPlaylistDetailCreateDto"}}, "additionalProperties": false}, "MMLayoutTemplateAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMLayoutTemplateCreateDto"}}, "additionalProperties": false}, "MMLayoutTemplateCreateDto": {"type": "object", "properties": {"LayoutID": {"type": "integer", "format": "int32"}, "LayoutName": {"type": "string", "nullable": true}, "LayoutDescription": {"type": "string", "nullable": true}, "LayoutRows": {"type": "integer", "format": "int32", "nullable": true}, "LayoutCols": {"type": "integer", "format": "int32", "nullable": true}, "TemplateGridCount": {"type": "integer", "format": "int32", "nullable": true}, "RegionList": {"type": "array", "items": {"$ref": "#/components/schemas/MMLayoutRegionCreateDto"}, "nullable": true}}, "additionalProperties": false}, "MMLayoutTemplateDeleteRequestDto": {"type": "object", "properties": {"LayoutID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MMLayoutTemplateUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMLayoutTemplateCreateDto"}}, "additionalProperties": false}, "MMPlaybackDeviceCreateDto": {"type": "object", "properties": {"PlaybackDeviceID": {"type": "integer", "format": "int32"}, "CampaignID": {"type": "integer", "format": "int32"}, "DeviceID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MMPlaylistAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMPlaylistCreateDto"}}, "additionalProperties": false}, "MMPlaylistCreateDto": {"type": "object", "properties": {"PlaylistID": {"type": "integer", "format": "int32"}, "PlaylistName": {"type": "string", "nullable": true}, "LayoutID": {"type": "integer", "format": "int32"}, "PlaylistDetailDtoList": {"type": "array", "items": {"$ref": "#/components/schemas/MMPlaylistDetailCreateDto"}, "nullable": true}}, "additionalProperties": false}, "MMPlaylistDeleteRequestDto": {"type": "object", "properties": {"PlaylistID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "MMPlaylistDetailCreateDto": {"type": "object", "properties": {"DetailID": {"type": "integer", "format": "int32"}, "PlaylistID": {"type": "integer", "format": "int32"}, "ProgramName": {"type": "string", "nullable": true}, "RegionID": {"type": "integer", "format": "int32"}, "PlaylistDetailXqList": {"type": "array", "items": {"$ref": "#/components/schemas/MMPlaylistDetailXqCreateDto"}, "nullable": true}}, "additionalProperties": false}, "MMPlaylistDetailXqCreateDto": {"type": "object", "properties": {"DetailXqID": {"type": "integer", "format": "int32"}, "DetailID": {"type": "integer", "format": "int32"}, "FileID": {"type": "integer", "format": "int32"}, "Sequence": {"type": "integer", "format": "int32"}, "AdjustedDuration": {"type": "integer", "format": "int32", "nullable": true}, "MMFile": {"$ref": "#/components/schemas/MMFileCreateDto"}}, "additionalProperties": false}, "MMPlaylistUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/MMPlaylistCreateDto"}}, "additionalProperties": false}, "Pagination": {"type": "object", "properties": {"Rows": {"type": "integer", "format": "int32"}, "Page": {"type": "integer", "format": "int32"}, "Sidx": {"type": "string", "nullable": true}, "Sord": {"type": "string", "nullable": true}, "Records": {"type": "integer", "format": "int32"}, "Total": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "PlaceAnOrdeUserDto": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "FdUid": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "ShopId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RefreshTokenDto": {"type": "object", "properties": {"RefreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterLeaderContext": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "Name": {"type": "string", "nullable": true}, "Phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RmAreaAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RmAreaDto"}}, "additionalProperties": false}, "RmAreaDeleteRequestDto": {"type": "object", "properties": {"RmAreaID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RmAreaDto": {"type": "object", "properties": {"AreaNo": {"type": "string", "nullable": true}, "AreaName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RmAreaUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RmAreaDto"}}, "additionalProperties": false}, "RmTypeAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RmTypeDto"}}, "additionalProperties": false}, "RmTypeDeleteRequestDto": {"type": "object", "properties": {"RmTypeID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RmTypeDto": {"type": "object", "properties": {"RtNo": {"type": "string", "nullable": true}, "RtName": {"type": "string", "nullable": true}, "MaxP": {"type": "integer", "format": "int32"}, "NoServ": {"type": "boolean"}, "AccType": {"type": "string", "nullable": true}, "RmPrice": {"type": "integer", "format": "int32"}, "SRmPrice": {"type": "integer", "format": "int32", "nullable": true}, "WeekEndPrice": {"type": "integer", "format": "int32", "nullable": true}, "RealRoom": {"type": "boolean"}, "CanAutoZD": {"type": "boolean"}, "MaxZDRate": {"type": "integer", "format": "int32"}, "RmCostType": {"type": "string", "nullable": true}, "ServRate": {"type": "integer", "format": "int32"}, "RmPrice_Person": {"type": "integer", "format": "int32"}, "SRmPrice_Person": {"type": "integer", "format": "int32"}, "WeekEndPrice_Person": {"type": "integer", "format": "int32"}, "RmPrice_PerUnit": {"type": "integer", "format": "int32"}, "SRmPrice_PerUnit": {"type": "integer", "format": "int32"}, "WeekEndPrice_PerUnit": {"type": "integer", "format": "int32"}, "UnitMinutes": {"type": "integer", "format": "int32"}, "MinMinutesOfTimeZone": {"type": "integer", "format": "int32"}, "MinMinutesOfTimeUnit": {"type": "integer", "format": "int32"}, "SetClearing": {"type": "boolean"}, "Rowguid": {"type": "string", "format": "uuid"}, "MsreplTranVersion": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "RmTypeUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RmTypeDto"}}, "additionalProperties": false}, "RoomAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RoomDto"}}, "additionalProperties": false}, "RoomDeleteRequestDto": {"type": "object", "properties": {"RoomID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RoomDto": {"type": "object", "properties": {"RmNo": {"type": "string", "nullable": true}, "RmName": {"type": "string", "nullable": true}, "RtNo": {"type": "string", "nullable": true}, "AreaNo": {"type": "string", "nullable": true}, "InvNo": {"type": "string", "nullable": true}, "PriceNo": {"type": "string", "nullable": true}, "WorkDate": {"type": "string", "nullable": true}, "RsPos": {"type": "string", "nullable": true}, "RmStatus": {"type": "string", "nullable": true}, "IsSDate": {"type": "boolean"}, "Rem": {"type": "string", "nullable": true}, "BookDate": {"type": "string", "nullable": true}, "BookTime": {"type": "string", "nullable": true}, "InDate": {"type": "string", "nullable": true}, "InTime": {"type": "string", "nullable": true}, "InNumbers": {"type": "integer", "format": "int32", "nullable": true}, "OpenUserId": {"type": "string", "nullable": true}, "AccUserId": {"type": "string", "nullable": true}, "AccDate": {"type": "string", "nullable": true}, "AccTime": {"type": "string", "nullable": true}, "ContinueUserId": {"type": "string", "nullable": true}, "ContinueTime": {"type": "string", "nullable": true}, "MemberNo": {"type": "string", "nullable": true}, "CustName": {"type": "string", "nullable": true}, "OrderUserId": {"type": "string", "nullable": true}, "DiscRate": {"type": "integer", "format": "int32"}, "Serv": {"type": "integer", "format": "int32", "nullable": true}, "FdCost": {"type": "integer", "format": "int32", "nullable": true}, "RmCost": {"type": "integer", "format": "int32", "nullable": true}, "Disc": {"type": "integer", "format": "int32", "nullable": true}, "ZD": {"type": "integer", "format": "int32", "nullable": true}, "BeerZD": {"type": "integer", "format": "int32", "nullable": true}, "BeerCash": {"type": "integer", "format": "int32", "nullable": true}, "Tax": {"type": "integer", "format": "int32", "nullable": true}, "MorePayed": {"type": "integer", "format": "int32"}, "Tot": {"type": "integer", "format": "int32", "nullable": true}, "WC": {"type": "boolean"}, "Dance": {"type": "boolean"}, "PrnFIndex": {"type": "string", "nullable": true}, "PrnDIndex": {"type": "string", "nullable": true}, "PInvCount": {"type": "integer", "format": "int32", "nullable": true}, "FromRmNo": {"type": "string", "nullable": true}, "OpenCount": {"type": "integer", "format": "int32", "nullable": true}, "ForceNoServ": {"type": "boolean"}, "Tag": {"type": "integer", "format": "int32", "nullable": true}, "FixedDisc": {"type": "integer", "format": "int32"}, "CarId": {"type": "string", "nullable": true}, "FdCost_InRmCost": {"type": "integer", "format": "int32", "nullable": true}, "FdCost_NotInRmCost": {"type": "integer", "format": "int32", "nullable": true}, "MembDisc": {"type": "boolean"}, "MembCard": {"type": "integer", "format": "int32", "nullable": true}, "CardMNo": {"type": "string", "nullable": true}, "CardAmount": {"type": "integer", "format": "int32", "nullable": true}, "CloseTime": {"type": "string", "nullable": true}, "CallAccount": {"type": "boolean"}, "BadReason": {"type": "string", "nullable": true}, "BadUserId": {"type": "string", "nullable": true}, "AutoZD": {"type": "integer", "format": "int32"}, "Rowguid": {"type": "string", "format": "uuid"}, "MsreplTranVersion": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "RoomUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RoomDto"}}, "additionalProperties": false}, "RtAutoAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RtAutoDto"}}, "additionalProperties": false}, "RtAutoDeleteRequestDto": {"type": "object", "properties": {"RtAutoID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RtAutoDto": {"type": "object", "properties": {"IKey": {"type": "integer", "format": "int32"}, "RtNo": {"type": "string", "nullable": true}, "FdNo": {"type": "string", "nullable": true}, "FdQty": {"type": "integer", "format": "int32"}, "CashType": {"type": "string", "nullable": true}, "CashUserId": {"type": "string", "nullable": true}, "MsreplTranVersion": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "RtAutoUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RtAutoDto"}}, "additionalProperties": false}, "RtTimePriceAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RtTimePriceDto"}}, "additionalProperties": false}, "RtTimePriceDeleteRequestDto": {"type": "object", "properties": {"RtTimePriceID": {"type": "string", "nullable": true}, "DayOfWeek": {"type": "string", "nullable": true}, "FromTime": {"type": "string", "nullable": true}, "ToTime": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RtTimePriceDto": {"type": "object", "properties": {"RtNo": {"type": "string", "nullable": true}, "DayOfWeek": {"type": "string", "nullable": true}, "FromTime": {"type": "string", "nullable": true}, "ToTime": {"type": "string", "nullable": true}, "RmPrice": {"type": "integer", "format": "int32"}, "SRmPrice": {"type": "integer", "format": "int32"}, "WeekEndPrice": {"type": "integer", "format": "int32"}, "DiscRate": {"type": "integer", "format": "int32"}, "MsreplTranVersion": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "RtTimePriceUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/RtTimePriceDto"}}, "additionalProperties": false}, "SingerContext": {"type": "object", "additionalProperties": false}, "SingleSongEntryContext": {"type": "object", "properties": {"SongName": {"type": "string", "nullable": true}, "ArtistId": {"type": "string", "nullable": true}, "AlbumName": {"type": "string", "nullable": true}, "Duration": {"type": "integer", "format": "int32"}, "ReleaseYear": {"type": "integer", "format": "int32"}, "Genre": {"type": "string", "nullable": true}, "Language": {"type": "string", "nullable": true}, "Version": {"type": "string", "nullable": true}, "FilePath": {"type": "string", "nullable": true}, "PlayUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SongContext": {"type": "object", "additionalProperties": false}, "SongFeedbackAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/SongFeedbackCreateDto"}}, "additionalProperties": false}, "SongFeedbackCreateDto": {"type": "object", "properties": {"FbId": {"type": "integer", "format": "int32"}, "SongName": {"type": "string", "nullable": true}, "SingerName": {"type": "string", "nullable": true}, "SongVersion": {"type": "string", "nullable": true}, "CreateTime": {"type": "string", "format": "date-time", "nullable": true}, "Remark": {"type": "string", "nullable": true}, "ContactWay": {"type": "string", "nullable": true}, "FeedbackByID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TransferCardContext": {"type": "object", "properties": {"CodeKey": {"type": "string", "nullable": true}, "openid": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserLoginDto": {"required": ["Password", "UserName"], "type": "object", "properties": {"UserName": {"minLength": 1, "type": "string"}, "Password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "WPTopicIAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/WPTopicICreateDto"}}, "additionalProperties": false}, "WPTopicIBdFileCreateDto": {"type": "object", "properties": {"TbfID": {"type": "integer", "format": "int32"}, "TopicID": {"type": "integer", "format": "int32"}, "DetailsID": {"type": "integer", "format": "int32"}, "FileID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WPTopicICreateDto": {"type": "object", "properties": {"TopicID": {"type": "integer", "format": "int32"}, "TopicName": {"type": "string", "nullable": true}, "TemplateID": {"type": "integer", "format": "int32"}, "CreatedBy": {"type": "string", "nullable": true}, "topicIBdFiles": {"type": "array", "items": {"$ref": "#/components/schemas/WPTopicIBdFileCreateDto"}, "nullable": true}}, "additionalProperties": false}, "WPTopicIDeleteRequestDto": {"type": "object", "properties": {"TopicID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WPTopicIUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/WPTopicICreateDto"}}, "additionalProperties": false}, "WayFoodMapAddRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/WayFoodMapCreateDto"}}, "additionalProperties": false}, "WayFoodMapCreateDto": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "FdNo": {"type": "string", "nullable": true}, "ThirdFdNo": {"type": "string", "nullable": true}, "Platform": {"type": "integer", "format": "int32"}, "StoreId": {"type": "integer", "format": "int32"}, "Qty": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WayFoodMapSingleItemDeleteRequestDto": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WayFoodMapUpdateRequestDto": {"type": "object", "properties": {"Model": {"$ref": "#/components/schemas/WayFoodMapCreateDto"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}