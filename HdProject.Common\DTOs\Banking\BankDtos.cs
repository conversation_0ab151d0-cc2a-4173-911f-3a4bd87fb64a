using System.ComponentModel.DataAnnotations;
using HdProject.Domain.Result.Page;

namespace HdProject.Common.DTOs.Banking
{
    public class BankCreateDto
    {
        [Required]
        public int BankSK { get; set; }

        [Required, StringLength(200)]
        public string BankName { get; set; } = string.Empty;
    }

    public class BankUpdateDto
    {
        [Required, StringLength(200)]
        public string BankName { get; set; } = string.Empty;
    }

    public class BankQueryDto : Pagination
    {
        public string? Keyword { get; set; }
    }

    public class BankDealCreateDto
    {
        [Required]
        public int DealSK { get; set; }
        [Required]
        public int BankSK { get; set; }
        [Required, StringLength(50)]
        public string FdNo { get; set; } = string.Empty;
        [Required, StringLength(500)]
        public string DealName { get; set; } = string.Empty;
        [Range(0,double.MaxValue)]
        public decimal DealAmount { get; set; }
        [Range(0,double.MaxValue)]
        public decimal SubsidyAmount { get; set; }
        [Range(0,double.MaxValue)]
        public decimal TotalAmount { get; set; }
        [Range(0,double.MaxValue)]
        public decimal ServiceFee { get; set; }
        [Range(0,double.MaxValue)]
        public decimal NetAmount { get; set; }
    }

    public class BankDealUpdateDto
    {
        [Required]
        public int BankSK { get; set; }
        [Required, StringLength(50)]
        public string FdNo { get; set; } = string.Empty;
        [Required, StringLength(500)]
        public string DealName { get; set; } = string.Empty;
        [Range(0,double.MaxValue)]
        public decimal DealAmount { get; set; }
        [Range(0,double.MaxValue)]
        public decimal SubsidyAmount { get; set; }
        [Range(0,double.MaxValue)]
        public decimal TotalAmount { get; set; }
        [Range(0,double.MaxValue)]
        public decimal ServiceFee { get; set; }
        [Range(0,double.MaxValue)]
        public decimal NetAmount { get; set; }
    }

    public class BankDealQueryDto : Pagination
    {
        public int? BankSK { get; set; }
        public string? FdNo { get; set; }
        public string? DealName { get; set; }
    }
}
