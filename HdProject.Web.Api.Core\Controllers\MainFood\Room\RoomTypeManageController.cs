﻿using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.MainFood.Room
{
    /// <summary>
    /// 房型接口控制器
    /// </summary>
    public class RoomTypeManageController : PublicControllerBase
    {
        private readonly IRoomTypeService _roomTypeService;
        private readonly ILogger<RoomTypeManageController> _logger;
        public RoomTypeManageController(IRoomTypeService roomTypeService, ILogger<RoomTypeManageController> logger)
        {
            _roomTypeService = roomTypeService;
            _logger = logger;
        }
        /// <summary>
        /// 查询房型全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllRoomType([FromQuery] RmTypeGetAllAsyncRequestDto request)
        {
            var result = await _roomTypeService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询房型信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdRoomType([FromQuery] RmTypeGetByIdAsyncRequestDto request)
        {
            var result = await _roomTypeService.GetByIdAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 新增房型信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> RoomTypeAddValue([FromBody] RmTypeAddRequestDto request)
        {
            var result = await _roomTypeService.AddAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 修改房型信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> RoomTypeUpdateValue([FromBody] RmTypeUpdateRequestDto request)
        {
            var result = await _roomTypeService.UpdateAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 删除房型信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> RoomTypeDeleteValue([FromBody] RmTypeDeleteRequestDto request)
        {
            var result = await _roomTypeService.DeleteAsync(request);
            return ApiData(result);
        }
    }
}
