﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.Rms
{
    public class timeinfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Ikey { get; set; }

        /// <summary>
        /// 时间编号
        /// </summary>

        public string TimeNo { get; set; }

        /// <summary>
        /// 时间名称
        /// </summary>

        public string TimeName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>

        public int BegTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>

        public int EndTime { get; set; }

        /// <summary>
        /// 是否打卡
        /// </summary>

        public bool IsClocks { get; set; }

        /// <summary>
        /// 是否特殊
        /// </summary>

        public bool IsSpecial { get; set; }
    }
}
