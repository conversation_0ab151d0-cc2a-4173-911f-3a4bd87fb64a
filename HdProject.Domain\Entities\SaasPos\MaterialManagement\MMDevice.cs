﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MaterialManagement
{
    /// <summary>
    /// 设备表
    /// </summary>
    [SugarTable("MM_Device")]
    public class MMDevice
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int DeviceID { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }
        /// <summary>
        /// 设备型号
        /// </summary>
        public string? DeviceModel { get; set; }
        /// <summary>
        /// 设备版本
        /// </summary>
        public string? DeviceVersion { get; set; }
        /// <summary>
        /// 设备方向
        /// </summary>
        public string? DeviceOrientation { get; set; }
        /// <summary>
        /// 高
        /// </summary>
        public int? DeviceHeight { get; set; }
        /// <summary>
        /// 宽
        /// </summary>
        public int? DeviceWidth { get; set; }
        /// <summary>
        /// 分辨率
        /// </summary>
        public string? DeviceResolution { get; set; }
        /// <summary>
        /// 门店
        /// </summary>
        public int? StoreId { get; set; }
        /// <summary>
        /// 设备状态
        /// </summary>
        /// 
        //[Navigate(NavigateType.OneToOne, nameof(OnlineStatusCode))]//一对一
        //public MMDeviceStatus OnlineStatus { get; set; }
        public int? OnlineStatusCode { get; set; }
        /// <summary>
        /// 设备布局
        /// </summary>
        /// 
        [Navigate(NavigateType.OneToOne, nameof(DeviceID), nameof(MMLayoutTemplate.LayoutID))]
        public MMLayoutTemplate LayoutTemplate { get; set; }
        [Navigate(NavigateType.OneToMany, nameof(DeviceID), nameof(MMLayoutRegion.LayoutID))]

        public List<MMLayoutRegion> LayoutRegions { get; set; }
        /// <summary>
        /// 设备最后访问时间
        /// </summary>
        /// 
        [Navigate(NavigateType.OneToOne, nameof(DeviceID))]//一对一
        public MMDeviceFinalVisit? finalVisit { get; set; }

        /// <summary>
        /// 投放设备
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(DeviceID), nameof(MMAdCampaign.CampaignID))]
        public List<MMAdCampaign> mMAdCampaigns { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public bool IsActive { get; set; }
        /// <summary>
        /// 禁用人
        /// </summary>
        public string? DisabledBy { get; set; }
        /// <summary>
        /// 禁用时间
        /// </summary>
        public DateTime? DisabledTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreatedTime { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string? ModifiedBy { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifiedTime { get; set; }
    }
    ///// <summary>
    ///// 设备状态表
    ///// </summary>
    //[SugarTable("MM_DeviceStatus")]
    //public class MMDeviceStatus
    //{
    //    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    //    public int StatusID { get; set; }
    //    public string StatusName { get; set; }
    //    public string? Remarks { get; set; }
    //}

    /// <summary>
    /// 设备最后访问时间表
    /// </summary>
    [SugarTable("MM_DeviceFinalVisit")]
    public class MMDeviceFinalVisit
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int FinalVisitID { get; set; }
        public int DeviceID { get; set; }
        public DateTime? LastVisitTime { get; set; }
    }
}
