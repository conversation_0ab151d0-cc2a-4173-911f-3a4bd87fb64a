﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Context.SaasPos.Commission.AssignRecommend
{
    /// <summary>
    /// 推荐关系创建关系类
    /// </summary>
    public class AssignRecommendCreateDto
    {
        /// <summary>
        /// 推荐关系ID
        /// </summary>
        public int ARID { get; set; }

        /// <summary>
        /// 关联指派看房ID，外键
        /// </summary>
        public int AssignID { get; set; }

        /// <summary>
        /// 推荐人编码
        /// </summary>
        public string? ReferenceID { get; set; }

        /// <summary>
        /// 推荐人名称
        /// </summary>
        public string? ReferenceName { get; set; }

        /// <summary>
        /// 被推荐编码
        /// </summary>
        public string? RecommendedID { get; set; }

        /// <summary>
        /// 被推荐员工名称
        /// </summary>
        public string? RecommendedName { get; set; }
    }
}
