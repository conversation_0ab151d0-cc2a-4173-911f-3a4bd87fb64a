﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context;
using HdProject.Domain.Context.SongManagement;
using HdProject.Domain.DTOs;
using HdProject.Domain.DTOs.SongManagement;

namespace HdProject.Application.Services.Interfaces.SaasPos.SongSyncCenter
{
    public  interface ISingerService
    {
        /// <summary>
        /// 新增歌星
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SingerDto> NewSingerAdded(SingerContext context);
        /// <summary>
        ///歌星列表详细信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SingerDto> SingerlistInfo(SingerContext context);
        /// <summary>
        ///歌星信息更新
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SingerDto> SingerInfoUpdate(SingerContext context);

        /// <summary>
        /// 歌星删除
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SingerDto> DeleteSinger(SingerContext context);
        /// <summary>
        /// 获取歌星相关歌曲
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<SingerDto> GetSingerSong(SingerContext context);
    }
}
