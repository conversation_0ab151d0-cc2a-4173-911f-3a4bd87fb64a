﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.Commission
{
    /// <summary>
    /// 指派看房模型类
    /// </summary>
    [SugarTable("Commission_AssignShowings")]
    public class CommissionAssignShowing
    {
        /// <summary>
        /// 指派看房ID，主键自增
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int AssignID { get; set; }

        /// <summary>
        /// 房间号
        /// </summary>
        public string? RmNo { get; set; }

        /// <summary>
        /// 账单号
        /// </summary>
        public string? BillNumber { get; set; }

        /// <summary>
        /// 操作员ID
        /// </summary>
        public string? OperatorID { get; set; }

        /// <summary>
        /// 操作员名称
        /// </summary>
        public string? OperatorName { get; set; }

        public int ShopID { get; set; }

        /// <summary>
        /// 代订人ID
        /// </summary>
        public string? BookingAgentID { get; set; }

        /// <summary>
        /// 代订人名称
        /// </summary>
        public string? BookingAgentName { get; set; }

        /// <summary>
        /// 可用状态，不可为空
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 禁用人
        /// </summary>
        public string? DisabledBy { get; set; }

        /// <summary>
        /// 禁用时间
        /// </summary>
        public DateTime? DisabledTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreatedTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string? ModifiedBy { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifiedTime { get; set; }

        /// <summary>
        /// 指派员工
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(AssignID), nameof(CommissionAssignEmployee.AssignID))]
        public List<CommissionAssignEmployee> assignEmployeeList { get; set; }
        /// <summary>
        /// 指派推荐关系
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(AssignID), nameof(CommissionAssignRecommend.AssignID))]
        public List<CommissionAssignRecommend> assignRecommends { get; set; }
    }
}
