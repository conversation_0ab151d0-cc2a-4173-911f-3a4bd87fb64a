[{"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Commission.AssignEmployeeController", "Method": "GetByRoomAssignEmployee", "RelativePath": "api/AssignEmployee/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.Commission.AssignEmployee.AssignEmployeeGetAllRequestDto", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Commission.AssignShowingsController", "Method": "AssignShowingsAddValue", "RelativePath": "api/AssignShowings/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.Commission.AssignShowings.AssignShowingsAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Commission.AssignShowingsController", "Method": "AssignShowingsDeletedValue", "RelativePath": "api/AssignShowings/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.Commission.AssignShowings.AssignShowingsDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Commission.AssignShowingsController", "Method": "GetByIdAssignShowings", "RelativePath": "api/AssignShowings/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "AssignID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Commission.AssignShowingsController", "Method": "GetByRoomAssignShowings", "RelativePath": "api/AssignShowings/GetByRoom", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ShopID", "Type": "System.Int32", "IsRequired": false}, {"Name": "RmNo", "Type": "System.String", "IsRequired": false}, {"Name": "Bill<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Commission.AssignShowingsController", "Method": "GetRoomStatus", "RelativePath": "api/AssignShowings/GetRoomStatus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ShopId", "Type": "System.Int32", "IsRequired": false}, {"Name": "RmNo", "Type": "System.String", "IsRequired": false}, {"Name": "InvNo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Commission.AssignShowingsController", "Method": "AssignShowingsUpdateValue", "RelativePath": "api/AssignShowings/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.Commission.AssignShowings.AssignShowingsUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "HdProject.Common.DTOs.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "HdProject.Common.DTOs.UserLoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "HdProject.Common.DTOs.RefreshTokenDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.AuthController", "Method": "GetUserIdString", "RelativePath": "api/Auth/user-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SysService.CallNumberController", "Method": "GetCallNumber", "RelativePath": "api/CallNumber/Get", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Key", "Type": "System.String", "IsRequired": false}, {"Name": "Date", "Type": "System.String", "IsRequired": false}, {"Name": "TypeId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.DbFood.dbFoodRoom.DbFoodRoomController", "Method": "GetAllDbFoodRoom", "RelativePath": "api/DbFoodRoom/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "AreaID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ShopID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.DbFood.dbFoodRoom.DbFoodRoomController", "Method": "GetAllRegionAsync", "RelativePath": "api/DbFoodRoom/GetRegionAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ShopId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMAdCampaignController", "Method": "MMAdCampaignAddValue", "RelativePath": "api/MMAdCampaign/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign.MMAdCampaignAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMAdCampaignController", "Method": "MMAdCampaignDeletedValue", "RelativePath": "api/MMAdCampaign/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign.MMAdCampaignDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMAdCampaignController", "Method": "GetAllMMAdCampaign", "RelativePath": "api/MMAdCampaign/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMAdCampaignController", "Method": "GetByIdMMAdCampaign", "RelativePath": "api/MMAdCampaign/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "CampaignID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMAdCampaignController", "Method": "GetCampaignDetails", "RelativePath": "api/MMAdCampaign/GetDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "CampaignID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMAdCampaignController", "Method": "MMAdCampaignUpdateValue", "RelativePath": "api/MMAdCampaign/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign.MMAdCampaignUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMAdCampaignController", "Method": "MMUpdateLaunchDevice", "RelativePath": "api/MMAdCampaign/UpdateLaunchDevice", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign.MMAdCampaignUpdateLaunchDeviceRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMDeviceController", "Method": "MMDeviceAddValue", "RelativePath": "api/MMDevice/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice.MMDeviceAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMDeviceController", "Method": "MMDeviceDeletedValue", "RelativePath": "api/MMDevice/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice.MMDeviceDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMDeviceController", "Method": "GetAllMMDevice", "RelativePath": "api/MMDevice/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMDeviceController", "Method": "GetByIdMMDevice", "RelativePath": "api/MMDevice/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMDeviceController", "Method": "GetDeviceDetails", "RelativePath": "api/MMDevice/GetDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMDeviceController", "Method": "MMDeviceSaveWh", "RelativePath": "api/MMDevice/SaveWh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice.MMDeviceSaveWhRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMDeviceController", "Method": "MMDeviceUpdateValue", "RelativePath": "api/MMDevice/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice.MMDeviceUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMDeviceController", "Method": "MMUpdateLaunchDevice", "RelativePath": "api/MMDevice/UpdateLaunchCampaign", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice.MMDeviceUpdateLaunchCampaignRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMFileController", "Method": "MMFileDeletedValue", "RelativePath": "api/M<PERSON>ile/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile.MMFileDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMFileController", "Method": "GetAllMMFile", "RelativePath": "api/MMFile/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMFileController", "Method": "GetByIdMMFile", "RelativePath": "api/MMFile/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "FileID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMFileController", "Method": "MMFileUpdateValue", "RelativePath": "api/MMFile/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile.MMFileUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMFileController", "Method": "MMFileAddValue", "RelativePath": "api/MMFile/Upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMFolderController", "Method": "AddFolder", "RelativePath": "api/MMFolder/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMFolder.MMFolderAddFolderRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMFolderController", "Method": "GetAllFolder", "RelativePath": "api/MMFolder/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMFolder.MMFolderGetAllFolderRequestDto", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMFolderController", "Method": "GetByIdFolder", "RelativePath": "api/MMFolder/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "FolderID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMLayoutTemplateController", "Method": "MMLayoutTemplateAddValue", "RelativePath": "api/MMLayoutTemplate/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout.MMLayoutTemplateAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMLayoutTemplateController", "Method": "MMLayoutTemplateDeletedValue", "RelativePath": "api/MMLayoutTemplate/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout.MMLayoutTemplateDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMLayoutTemplateController", "Method": "GetAllMMLayoutTemplate", "RelativePath": "api/MMLayoutTemplate/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMLayoutTemplateController", "Method": "GetByIdMMLayoutTemplate", "RelativePath": "api/MMLayoutTemplate/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "LayoutID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMLayoutTemplateController", "Method": "MMLayoutTemplateUpdateValue", "RelativePath": "api/MMLayoutTemplate/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout.MMLayoutTemplateUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMPlaylistController", "Method": "MMPlaylistAddValue", "RelativePath": "api/MMPlaylist/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist.MMPlaylistAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMPlaylistController", "Method": "MMPlaylistDeletedValue", "RelativePath": "api/MMPlaylist/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist.MMPlaylistDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMPlaylistController", "Method": "GetAllMMPlaylist", "RelativePath": "api/MMPlaylist/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMPlaylistController", "Method": "GetByIdMMPlaylist", "RelativePath": "api/MMPlaylist/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PlaylistID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement.MMPlaylistController", "Method": "MMPlaylistUpdateValue", "RelativePath": "api/MMPlaylist/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist.MMPlaylistUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.OpenApi.MTopenController", "Method": "AuthCode", "RelativePath": "api/MTopen/authcode", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "businessId", "Type": "System.String", "IsRequired": false}, {"Name": "developerId", "Type": "System.String", "IsRequired": false}, {"Name": "code", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RmAreaManageController", "Method": "RmAreaAddValue", "RelativePath": "api/RmAreaManage/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RmAreaAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RmAreaManageController", "Method": "RmAreaDeleteValue", "RelativePath": "api/RmAreaManage/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RmAreaDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RmAreaManageController", "Method": "GetAllRmArea", "RelativePath": "api/RmAreaManage/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RmAreaManageController", "Method": "GetByIdRmArea", "RelativePath": "api/RmAreaManage/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "AreaNo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RmAreaManageController", "Method": "RmAreaUpdateValue", "RelativePath": "api/RmAreaManage/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RmAreaUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.Rms.Room.RmInfoController", "Method": "GetList", "RelativePath": "api/RmInfo/GetList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomManageController", "Method": "RoomAddValue", "RelativePath": "api/RoomManage/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RoomAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomManageController", "Method": "RoomDeletedValue", "RelativePath": "api/RoomManage/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RoomDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomManageController", "Method": "GetAllRoom", "RelativePath": "api/RoomManage/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomManageController", "Method": "GetByIdRoom", "RelativePath": "api/RoomManage/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RmNo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomManageController", "Method": "RoomUpdateValue", "RelativePath": "api/RoomManage/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RoomUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomTypeManageController", "Method": "RoomTypeAddValue", "RelativePath": "api/RoomTypeManage/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RmTypeAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomTypeManageController", "Method": "RoomTypeDeleteValue", "RelativePath": "api/RoomTypeManage/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RmTypeDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomTypeManageController", "Method": "GetAllRoomType", "RelativePath": "api/RoomTypeManage/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomTypeManageController", "Method": "GetByIdRoomType", "RelativePath": "api/RoomTypeManage/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RtNo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RoomTypeManageController", "Method": "RoomTypeUpdateValue", "RelativePath": "api/RoomTypeManage/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RmTypeUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtAutoManageController", "Method": "RtAutoAddValue", "RelativePath": "api/RtAutoManage/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RtAutoAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtAutoManageController", "Method": "RtAutoDeleteValue", "RelativePath": "api/RtAutoManage/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RtAutoDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtAutoManageController", "Method": "GetAllRtAuto", "RelativePath": "api/RtAutoManage/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtAutoManageController", "Method": "GetByIdRtAuto", "RelativePath": "api/RtAutoManage/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtAutoManageController", "Method": "RtAutoUpdateValue", "RelativePath": "api/RtAutoManage/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RtAutoUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtTimePriceManageController", "Method": "RtTimePriceAddValue", "RelativePath": "api/RtTimePriceManage/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RtTimePriceAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtTimePriceManageController", "Method": "RtTimePriceDeleteValue", "RelativePath": "api/RtTimePriceManage/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RtTimePriceDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtTimePriceManageController", "Method": "GetAllRtTimePrice", "RelativePath": "api/RtTimePriceManage/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtTimePriceManageController", "Method": "GetByIdRtTimePrice", "RelativePath": "api/RtTimePriceManage/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RtNo", "Type": "System.String", "IsRequired": false}, {"Name": "DayOfWeek", "Type": "System.String", "IsRequired": false}, {"Name": "FromTime", "Type": "System.String", "IsRequired": false}, {"Name": "ToTime", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MainFood.Room.RtTimePriceManageController", "Method": "RtTimePriceUpdateValue", "RelativePath": "api/RtTimePriceManage/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.MainFood.Room.RtTimePriceUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.SongManagement.SongFeedbackController", "Method": "SongFeedbackAdd", "RelativePath": "api/SongFeedback/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.SongFeedback.SongFeedbackAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.UserController", "Method": "AdminOnlyEndpoint", "RelativePath": "api/User/admin-only", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.UserController", "Method": "GetUserProfile", "RelativePath": "api/User/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.UserController", "Method": "Get", "RelativePath": "api/User/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ExternalGroupBuying.WayFoodMapController", "Method": "FoodMapAddValue", "RelativePath": "api/WayFoodMap/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.ExternalGroupBuying.WayFoodMapAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ExternalGroupBuying.WayFoodMapController", "Method": "FoodMapDeletedValue", "RelativePath": "api/WayFoodMap/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestDto", "Type": "HdProject.Domain.Context.SaasPos.ExternalGroupBuying.WayFoodMapSingleItemDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ExternalGroupBuying.WayFoodMapController", "Method": "GetAllWayFoodMap", "RelativePath": "api/WayFoodMap/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AllDates", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "QueryCriteria", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ExternalGroupBuying.WayFoodMapController", "Method": "GetAllWayStoreMap", "RelativePath": "api/WayFoodMap/GetStoreAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ExternalGroupBuying.WayFoodMapController", "Method": "ImportWayFoodMap", "RelativePath": "api/WayFoodMap/Import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ExternalGroupBuying.WayFoodMapController", "Method": "FoodMapUpdateValue", "RelativePath": "api/WayFoodMap/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SaasPos.ExternalGroupBuying.WayFoodMapUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicIController", "Method": "TopicIAddValue", "RelativePath": "api/WPTopicI/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SongBase.WallPainting.WPTopicI.WPTopicIAddRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicIController", "Method": "TopicIDeletedValue", "RelativePath": "api/WPTopicI/Delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SongBase.WallPainting.WPTopicI.WPTopicIDeleteRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicIController", "Method": "GetByIdTopicI", "RelativePath": "api/WPTopicI/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "TopicID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicIController", "Method": "GetByUserOpenIdTopicI", "RelativePath": "api/WPTopicI/GetByUserOpenId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserOpenID", "Type": "System.String", "IsRequired": false}, {"Name": "Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Sidx", "Type": "System.String", "IsRequired": false}, {"Name": "Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicIController", "Method": "GetStoreAndPath", "RelativePath": "api/WPTopicI/GetStoreAndPath", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Store", "Type": "System.String", "IsRequired": false}, {"Name": "GUID", "Type": "System.String", "IsRequired": false}, {"Name": "Path", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicIController", "Method": "TopicIUpdateValue", "RelativePath": "api/WPTopicI/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "HdProject.Domain.Context.SongBase.WallPainting.WPTopicI.WPTopicIUpdateRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicIFileController", "Method": "WPTopicIFileAddValue", "RelativePath": "api/WPTopicIFile/Upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicITemplateController", "Method": "GetAllTemplate", "RelativePath": "api/WPTopicITemplate/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SongBase.WallPainting.WPTopicITemplateController", "Method": "GetByIdTemplate", "RelativePath": "api/WPTopicITemplate/GetById", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "TemplateID", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition.CommodityAdjustmentController", "Method": "AddOrUpdateCommDraftRecords", "RelativePath": "CommodityAdjustment/AddOrUpdateCommDraftRecords", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "System.Collections.Generic.List`1[[HdProject.Domain.Context.CommodityManagement.AdjustmentRequestContext, HdProject.Domain, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition.CommodityAdjustmentController", "Method": "CommSynchronousStore", "RelativePath": "CommodityAdjustment/CommSynchronousStore", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.CommodityManagement.CommSynchronousStoreContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition.CommodityAdjustmentController", "Method": "DeleteCommDraftRecord", "RelativePath": "CommodityAdjustment/DeleteCommDraftRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.CommodityManagement.DelectDraftRecordContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition.CommodityAdjustmentController", "Method": "GetCommDraftListRecord", "RelativePath": "CommodityAdjustment/GetCommDraftListRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "AdjustmentID", "Type": "System.Guid", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}, {"Name": "Keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition.CommodityAdjustmentController", "Method": "GetCommDraftRecord", "RelativePath": "CommodityAdjustment/GetCommDraftRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}, {"Name": "Keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition.CommodityAdjustmentController", "Method": "GetCommExistingGoods", "RelativePath": "CommodityAdjustment/GetCommExistingGoods", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Keyword", "Type": "System.String", "IsRequired": false}, {"Name": "FtNo", "Type": "System.String", "IsRequired": false}, {"Name": "IsPackage", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition.CommodityAdjustmentController", "Method": "GetCommHistoricalPrice", "RelativePath": "CommodityAdjustment/GetCommHistoricalPrice", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "FdNo", "Type": "System.String", "IsRequired": false}, {"Name": "ComboNo", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}, {"Name": "Keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition.CommodityAdjustmentController", "Method": "GetCommPublished", "RelativePath": "CommodityAdjustment/GetCommPublished", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "AdjustmentId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}, {"Name": "Keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MarketingActivityManagement.MarketingActivityController", "Method": "DeleteSalesAndCardsInfoRecord", "RelativePath": "MarketingActivity/DeleteSalesAndCardsInfoRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.MarketingActivityManagement.MarketingActivityContext+DeleteSalesAndCardsInfoContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MarketingActivityManagement.MarketingActivityController", "Method": "EditorSalesAndCardsInfoRecord", "RelativePath": "MarketingActivity/EditorSalesAndCardsInfoRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Data", "Type": "System.String", "IsRequired": false}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MarketingActivityManagement.MarketingActivityController", "Method": "GetCardSheetListRecord", "RelativePath": "MarketingActivity/GetCardSheetListRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "CardSheetListName", "Type": "System.String", "IsRequired": false}, {"Name": "CardSheetListId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MarketingActivityManagement.MarketingActivityController", "Method": "GetSalesAndCardsInfoRecord", "RelativePath": "MarketingActivity/GetSalesAndCardsInfoRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ActivityName", "Type": "System.String", "IsRequired": false}, {"Name": "CardSheetName", "Type": "System.String", "IsRequired": false}, {"Name": "TypeName", "Type": "System.String", "IsRequired": false}, {"Name": "SalesId", "Type": "System.String", "IsRequired": false}, {"Name": "CardSheetId", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.MarketingActivityManagement.MarketingActivityController", "Method": "GetWXQrCodeRecord", "RelativePath": "MarketingActivity/GetWXQrCodeRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Path", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.Rms.RefundManagement.RefundController", "Method": "ApplyRefundListRecord", "RelativePath": "Refund/ApplyRefundListRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ApplyNo", "Type": "System.String", "IsRequired": false}, {"Name": "ConfirmNo", "Type": "System.String", "IsRequired": false}, {"Name": "ApplyUserID", "Type": "System.String", "IsRequired": false}, {"Name": "IsEnd", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "BeginDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ShopID", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.Rms.RefundManagement.RefundController", "Method": "ApplyRefundRecord", "RelativePath": "Refund/ApplyRefundRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.RMS.RefundContext+ApplyRefundContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.Rms.RefundManagement.RefundController", "Method": "GetRefundRecord", "RelativePath": "Refund/GetRefundRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "NCodeNo", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.Rms.RefundManagement.RefundController", "Method": "RefundRecord", "RelativePath": "Refund/RefundRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.RMS.RefundContext+ConfirmRefundContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "ApplyForLeaderWithdrawal", "RelativePath": "ShopBookLeader/ApplyForLeaderWithdrawal", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.ApplyForLeaderWithdrawalContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "GetLeaderAppointmentRecords", "RelativePath": "ShopBookLeader/GetLeaderAppointmentRecords", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.GetLeaderAppointmentRecordsContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "GetLeaderPersonalInfo", "RelativePath": "ShopBookLeader/GetLeaderPersonalInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.GetLeaderPersonalInfoContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "GetLeadersAccrueCommission", "RelativePath": "ShopBookLeader/GetLeadersAccrueCommission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.GetCommissionRecordsContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "GetLeadersInfo", "RelativePath": "ShopBookLeader/GetLeadersInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.GetLeaderInfoListContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "GetLeadersOrderInfo", "RelativePath": "ShopBookLeader/GetLeadersOrderInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.GetOrderInfoListContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "GetLeaderSummaryReport", "RelativePath": "ShopBookLeader/GetLeaderSummaryReport", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "GetLeaderWithdrawalRecords", "RelativePath": "ShopBookLeader/GetLeaderWithdrawalRecords", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.GetLeaderWithdrawalRecordsContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.ShopBookLeaderController", "Method": "RegisterAsLeader", "RelativePath": "ShopBookLeader/RegisterAsLeader", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.RegisterLeaderContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SingerController", "Method": "Delete<PERSON>inger", "RelativePath": "<PERSON>/DeleteSinger", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SingerContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SingerController", "Method": "GetSingerSong", "RelativePath": "<PERSON>/GetSingerSong", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SingerContext", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SingerController", "Method": "NewSingerAdded", "RelativePath": "<PERSON>/NewSingerAdded", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SingerContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SingerController", "Method": "SingerInfoUpdate", "RelativePath": "Singer/SingerInfoUpdate", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SingerContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SingerController", "Method": "SingerlistInfo", "RelativePath": "Singer/SingerlistInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SingerContext", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SongController", "Method": "BatchImportSong", "RelativePath": "Song/BatchImportSong", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SongContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SongController", "Method": "SingleSongEntry", "RelativePath": "Song/SingleSongEntry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SingleSongEntryContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SongController", "Method": "SongDeletion", "RelativePath": "Song/SongDeletion", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SongContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SongController", "Method": "SongEditing", "RelativePath": "Song/SongEditing", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SongContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.SaasPos.Song.SongController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "Song/SongQuery", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.SongManagement.SongContext", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.Rms.SummaryStoreTimeSlotDailyList.SummaryStoreTimeSlotDailyController", "Method": "GetSummaryStoreTimeSlotDailyListRecord", "RelativePath": "SummaryStoreTimeSlotDaily/GetSummaryStoreTimeSlotDailyListRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Comedate", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.Rms.SummaryStoreTimeSlotDailyList.SummaryStoreTimeSlotDailyController", "Method": "GetSummaryStoreTimeSlotDailyRecord", "RelativePath": "SummaryStoreTimeSlotDaily/GetSummaryStoreTimeSlotDailyRecord", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StoreID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.GrouponBase.TransferController", "Method": "<PERSON><PERSON><PERSON>Record", "RelativePath": "Transfer/ClaimRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.GrouponBase.ClaimContext", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.GrouponBase.TransferController", "Method": "GetClaimKeyList", "RelativePath": "Transfer/GetClaimKeyList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.GrouponBase.TransferController", "Method": "GetNoSharedCardList", "RelativePath": "Transfer/GetNoSharedCardList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "openid", "Type": "System.String", "IsRequired": false}, {"Name": "IsGive", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.GrouponBase.TransferController", "Method": "GetSharedCardList", "RelativePath": "Transfer/GetSharedCardList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "openid", "Type": "System.String", "IsRequired": false}, {"Name": "IsGive", "Type": "System.Int32", "IsRequired": false}, {"Name": "CodeStatus", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Rows", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Sord", "Type": "System.String", "IsRequired": false}, {"Name": "Paging.Records", "Type": "System.Int32", "IsRequired": false}, {"Name": "Paging.Total", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "HdProject.Web.Api.Core.Controllers.GrouponBase.TransferController", "Method": "TransferCardRecord", "RelativePath": "Transfer/TransferCardRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "context", "Type": "HdProject.Domain.Context.GrouponBase.TransferCardContext", "IsRequired": true}], "ReturnTypes": []}]