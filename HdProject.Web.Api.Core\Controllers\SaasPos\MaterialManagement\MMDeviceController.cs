﻿
using System.Collections.Generic;
using Azure.Core;
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice;
using HdProject.Domain.WebApi;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement
{
    /// <summary>
    /// 设备管理接口控制器
    /// </summary>
    public class MMDeviceController : PublicControllerBase
    {
        private readonly IMMDeviceService _mMDeviceService;
        private readonly ILogger<MMDeviceController> _logger;
        public MMDeviceController(IMMDeviceService mMDeviceService, ILogger<MMDeviceController> logger)
        {
            _mMDeviceService = mMDeviceService;
            _logger = logger;
        }

        /// <summary>
        /// 查询设备全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllMMDevice([FromQuery] MMDeviceGetAllAsyncRequestDto request)
        {
            var result = await _mMDeviceService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询设备信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdMMDevice([FromQuery] MMDeviceGetByIdAsyncRequestDto request)
        {
            var result = await _mMDeviceService.GetByIdAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 新增设备信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> MMDeviceAddValue([FromBody] MMDeviceAddRequestDto request)
        {
            var result = await _mMDeviceService.AddAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 保存设备信息的宽高
        /// </summary>
        /// <returns></returns>
        [HttpPost("SaveWh")]
        public async Task<IActionResult> MMDeviceSaveWh([FromBody] MMDeviceSaveWhRequestDto request)
        {
            var result = await _mMDeviceService.SaveDeviceWhAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 修改设备信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> MMDeviceUpdateValue([FromBody] MMDeviceUpdateRequestDto request)
        {
            var result = await _mMDeviceService.UpdateAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 删除设备信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> MMDeviceDeletedValue([FromBody] MMDeviceDeleteRequestDto request)
        {
            var result = await _mMDeviceService.DeleteAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 根据ID和版本查询设备详情信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetDetails")]
        public async Task<IActionResult> GetDeviceDetails([FromQuery] MMDeviceGetDeviceDetailsAsyncRequestDto request)
        {
            var result = await _mMDeviceService.GetDeviceDetails(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 修改设备的绑定投放任务接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("UpdateLaunchCampaign")]
        public async Task<IActionResult> MMUpdateLaunchDevice([FromBody] MMDeviceUpdateLaunchCampaignRequestDto request)
        {
            var result = await _mMDeviceService.UpdateLaunchAdCampaignAsyns(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }
    }
}
