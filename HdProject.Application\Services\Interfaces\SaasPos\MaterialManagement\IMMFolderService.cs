﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFolder;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMFolder;

namespace HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement
{
    /// <summary>
    /// 文件夹
    /// </summary>
    public interface IMMFolderService
    {

        #region 文件夹相关
        /// <summary>
        /// 新增文件夹
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMFolderAddFolderResponseDto> AddFolderAsync(MMFolderAddFolderRequestDto requestDto);
        /// <summary>
        /// 查询所有文件夹
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMFolderGetAllFolderResponseDto> GetAllFolderAsync(MMFolderGetAllFolderRequestDto requestDto);
        /// <summary>
        /// 根据ID查询文件夹
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMFolderGetByIdFolderResponseDto> GetByIdFolderAsync(MMFolderGeByIdFolderRequestDto requestDto);
        #endregion
    }
}
