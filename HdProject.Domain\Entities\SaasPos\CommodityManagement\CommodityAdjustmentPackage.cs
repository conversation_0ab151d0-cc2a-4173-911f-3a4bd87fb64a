﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.CommodityManagement
{
    public partial class CommodityAdjustmentPackage
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        /// <summary>
        /// 套餐编号
        /// </summary>

        public string? ComboNo { get; set; }

        /// <summary>
        /// 调整详情ID
        /// </summary>
        public Guid DetailID { get; set; }

        /// <summary>
        /// 套餐类别号
        /// </summary>
        public string? FtNo { get; set; }

        /// <summary>
        /// 套餐名称
        /// </summary>
        public string? ComboName { get; set; }

        /// <summary>
        /// 商品编号
        /// </summary>
        public string? FdNo { get; set; }
        

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; }
        
    }
}
