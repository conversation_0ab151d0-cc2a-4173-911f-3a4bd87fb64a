﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.PlaceAnOrder;

namespace HdProject.Application.Services.Interfaces.SaasPos.PlaceAnOrder
{
    /// <summary>
    /// 下单公用接口类
    /// </summary>
    public interface IPlaceAnOrderService
    {
        /// <summary>
        /// 传入下单参数进行下单
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<bool> GetOrderByOperation(PlaceAnOrderRequestDto requestDto);
    }
}
