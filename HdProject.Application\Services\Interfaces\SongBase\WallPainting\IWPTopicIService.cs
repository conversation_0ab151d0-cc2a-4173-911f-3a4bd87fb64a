﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicI;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicI;

namespace HdProject.Application.Services.Interfaces.SongBase.WallPainting
{
    /// <summary>
    /// 主题接口
    /// </summary>
    public interface IWPTopicIService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicIGetByIdResponseDto> GetByIdAsync(WPTopicIGetByIdRequestDto requestDto);
        /// <summary>
        /// 根据用户OpenID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicIGetByUserOpenIdResponseDto> GetByUserOpenIdAsync(WPTopicIGetByUserOpenIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<WPTopicIGetAllResponseDto> GetAllAsync(WPTopicIGetAllRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicIAddResponseDto> AddAsync(WPTopicIAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicIUpdateResponseDto> UpdateAsync(WPTopicIUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicIDeleteResponseDto> DeleteAsync(WPTopicIDeleteRequestDto requestDto);
    }
}
