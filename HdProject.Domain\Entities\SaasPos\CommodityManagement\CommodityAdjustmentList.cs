﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.CommodityManagement
{
    // 商品调整表（CommodityAdjustmentList）

    public partial class CommodityAdjustmentList
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public Guid AdjustmentID { get; set; }
        public string Name { get; set; }
        public string UserID { get; set; }
        public DateTime CreationTime { get; set; }
        public byte Status { get; set; }
        [SugarColumn(DefaultValue = "0")]
        public bool IsDeleted { get; set; }
        public DateTime? DeleteTime { get; set; }
        public DateTime? PublishTime { get; set; }
        public DateTime? SyncTime { get; set; }
        public int? ActionType { get; set; }
       

    }
}
