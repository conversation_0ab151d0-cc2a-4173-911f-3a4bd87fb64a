﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;

namespace HdProject.Application.Services.Interfaces.MainFood
{
    /// <summary>
    /// 房间类型
    /// </summary>
    public interface IRoomTypeService
    {
        /// <summary>
        /// 根据ID查询房间类型信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RmTypeGetByIdAsyncResponseDto> GetByIdAsync(RmTypeGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 查询房间类型全部信息
        /// </summary>
        /// <returns></returns>
        Task<RmTypeGetAllAsyncResponseDto> GetAllAsync(RmTypeGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 新增房间类型信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RmTypeAddResponseDto> AddAsync(RmTypeAddRequestDto requestDto);
        /// <summary>
        /// 修改房间类型信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RmTypeUpdateResponseDto> UpdateAsync(RmTypeUpdateRequestDto requestDto);
        /// <summary>
        /// 删除房间类型信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RmTypeDeleteResponseDto> DeleteAsync(RmTypeDeleteRequestDto requestDto);
    }
}
