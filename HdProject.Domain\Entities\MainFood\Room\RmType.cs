﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.MainFood.Room
{
    /// <summary>
    /// 房型
    /// </summary>
    [SugarTable("RmType")]
    public class RmType
    {
        [SugarColumn(IsPrimaryKey = true, Length = 2)]
        public string RtNo { get; set; }

        [SugarColumn(Length = 50)]
        public string? RtName { get; set; }

        public short MaxP { get; set; }
        public bool NoServ { get; set; }

        [SugarColumn(Length = 1)]
        public string AccType { get; set; }

        public int RmPrice { get; set; }
        public int? SRmPrice { get; set; }
        public int? WeekEndPrice { get; set; }
        public bool RealRoom { get; set; }
        public bool CanAutoZD { get; set; }
        public int MaxZDRate { get; set; }

        [SugarColumn(Length = 1)]
        public string RmCostType { get; set; }

        public int ServRate { get; set; }
        public int RmPrice_Person { get; set; }
        public int SRmPrice_Person { get; set; }
        public int WeekEndPrice_Person { get; set; }
        public int RmPrice_PerUnit { get; set; }
        public int SRmPrice_PerUnit { get; set; }
        public int WeekEndPrice_PerUnit { get; set; }
        public int UnitMinutes { get; set; }
        public int MinMinutesOfTimeZone { get; set; }
        public int MinMinutesOfTimeUnit { get; set; }
        public bool SetClearing { get; set; }

        [SugarColumn(ColumnName = "rowguid", IndexGroupNameList = new[] { "index_324196205" })]
        public Guid Rowguid { get; set; }

        [SugarColumn(ColumnName = "msrepl_tran_version")]
        public Guid MsreplTranVersion { get; set; }
    }
}
