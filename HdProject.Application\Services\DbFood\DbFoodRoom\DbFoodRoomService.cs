﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Azure.Core;
using Furion.DatabaseAccessor;
using Furion.JsonSerialization;
using HdProject.Application.Services.Interfaces.DbFood.DbFoodRoom;
using HdProject.Domain.Context.DbFood.Room;
using HdProject.Domain.DTOs.DbFood;
using HdProject.Domain.Entities.DbFood;
using HdProject.Domain.Interfaces;
using LinqKit;
using Microsoft.AspNetCore.SignalR.Protocol;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using SqlSugar;
using StackExchange.Redis;

namespace HdProject.Application.Services.DbFood.DbFoodRoom
{
    /// <summary>
    /// 房间消费人数统计
    /// </summary>
    public class DbFoodRoomService : IDbFoodRoomService
    {
        private readonly IRepositoryDbFood<Room> _repositoryDbFood;//
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IHttpClientFactory _httpClientFactory;
        public DbFoodRoomService(IRepositoryDbFood<Room> repositoryDbFood, ISqlSugarClient sqlSugarClient, IHttpClientFactory httpClientFactory)
        {
            _repositoryDbFood = repositoryDbFood;
            _sqlSugarClient = sqlSugarClient;
            _httpClientFactory = httpClientFactory;
        }

        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Rms2009");
            }
        }

        /// <summary>
        /// 查询（分页）
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<DbFoodRoomGetAllResponseDto> GetAllAsync(DbFoodRoomGetAllRequestDto requestDto)
        {
            DbFoodRoomGetAllResponseDto responseDto = new DbFoodRoomGetAllResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<Room>(true);
                //过滤数据
                predicate = predicate.And(it => !string.IsNullOrEmpty(it.InvNo));
                // 动态添加条件
                //if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
                //{
                //    predicate = predicate.And(x => x.FdNo.Contains(requestDto.QueryCriteria));
                //}
                //存储过程名称：Pr_Room_ConsumerStatistics

                var url = $"http://ktv{requestDto.ShopID}.tang-hui.com.cn:90/execuse.ashx?Ex=getfixnumbers&shopid={requestDto.ShopID}&AreaId={requestDto.AreaID}&packconent=1";

                //本地测试接口地址
                // var url = $"http://localhost:7100/SongJson/WallPainting/GetStoreAndPathDownload.ashx?" +
                //  $"StoreKey={s[0]}&RoomKey={s[1]}&StoreToken={s[2]}&GuId={request.GUID}&PathUrl={request.Path}";

                var client = _httpClientFactory.CreateClient();
                client.BaseAddress = new Uri(url);
                var response = await client.GetAsync(url);

               // var result = await _repositoryDbFood.GetProcedureAsync(requestDto,"Pr_Room_ConsumerStatistics", new { ShopId = requestDto.ShopID, AreaId = requestDto.AreaID });
                
                var model = new DbFoodRoomDto();
                var modellist = new List<DbFoodRoomDto>();
                var list = new List<DbFoodRoomListDto>();
                if (response.IsSuccessStatusCode)
                {
                    var result = response.Content.ReadAsStringAsync();
                    modellist = JsonConvert.DeserializeObject<List<DbFoodRoomDto>>(result.Result);
                    model = modellist.FirstOrDefault();
                }
                else
                {
                    throw new Exception("包房点播失败！");
                }
                //foreach (var item in result)
                //{
                //    var m = new DbFoodRoomDto()
                //    {
                //        RmNo = item.RmNo,
                //        InNumbers = item.InNumbers,
                //    };
                //    model.Add(m);
                //}
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 查询区域
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<DbFoodRoomRegionGetAllResponseDto> GetAllRegionAsync(DbFoodRoomRegionGetAllRequestDto requestDto)
        {
            DbFoodRoomRegionGetAllResponseDto responseDto = new DbFoodRoomRegionGetAllResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<Room>(true);
                //过滤数据
                predicate = predicate.And(it => !string.IsNullOrEmpty(it.InvNo));
                // 动态添加条件
                //if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
                //{
                //    predicate = predicate.And(x => x.FdNo.Contains(requestDto.QueryCriteria));
                //}
                string sql = "select AreaID,AreaName from cloudRms2019.rms2019.dbo.areainfo where ShopID=@shopId";//SQL
                object pm = new { shopId = requestDto.ShopId };//条件参数
                var result = await _db.Ado.SqlQueryAsync<DbFoodRoomRegionDto>(sql, pm);
                var model = new List<DbFoodRoomRegionDto>();
                foreach (var item in result)
                {
                    var m = new DbFoodRoomRegionDto()
                    {
                        AreaID = item.AreaID,
                        AreaName = item.AreaName,
                    };
                    model.Add(m);
                }
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
    }
}
