﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Entities.OperateDate
{
    public class fdcashbak
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid Bikey { get; set; }
        public int  ShopId { get; set; }
        public int IKey { get; set; }
        //public string Ikey { get; set; }
        public string InvNo { get; set; }
        public string FdNo { get; set; }
        public string FdCName { get; set; }
        public int? DiscRate { get; set; }
        public int? FdPriceBeforeDisc { get; set; }
        public int? FdPrice { get; set; }
        public short FdQty { get; set; }
        public string CashType { get; set; }
        public string CashTime { get; set; }
        public string CashUserId { get; set; }
        public string CashUserName { get; set; }
        public string RefNo { get; set; }
        public string DeleUserId { get; set; }
        public string DeleUserName { get; set; }
        public string DeleTime { get; set; }
        public string ToZDTime { get; set; }
        public string UserId { get; set; }
        public string UserName { get; set; }
        public string MemberNo { get; set; }
        public string MemberName { get; set; }
        public string PrnIndex { get; set; }
        public bool? InRmCost { get; set; }
        public string Ai { get; set; }
        public int? AiCost { get; set; }
        public bool? Checked { get; set; }
        public int? Tag { get; set; }
        public bool? CanServ { get; set; }
        public string CheckUserId { get; set; }
        public string GiveMembNo { get; set; }
        public Guid? msrepl_tran_version { get; set; }
        public int OrderId { get; set; }
        public string PackageFdNo { get; set; }
    }
}
