﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;

namespace HdProject.Infrastructure.Repositorys.Imp
{
    public class RepositoryDbFood <T>: Repository<T>, IRepositoryDbFood<T>
        where T : class, new()
    {
        public RepositoryDbFood(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "dbfood";
        }
    }
}
