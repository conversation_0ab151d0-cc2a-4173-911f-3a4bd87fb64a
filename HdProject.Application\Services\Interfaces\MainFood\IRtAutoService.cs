﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;

namespace HdProject.Application.Services.Interfaces.MainFood
{
    /// <summary>
    /// 房间配送业务接口类
    /// </summary>
    public interface IRtAutoService
    {
        /// <summary>
        /// 根据ID查询房间配送信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RtAutoGetByIdAsyncResponseDto> GetByIdAsync(RtAutoGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 查询房间配送全部信息
        /// </summary>
        /// <returns></returns>
        Task<RtAutoGetAllAsyncResponseDto> GetAllAsync(RtAutoGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 新增房间配送信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RtAutoAddResponseDto> AddAsync(RtAutoAddRequestDto requestDto);
        /// <summary>
        /// 修改房间配送信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RtAutoUpdateResponseDto> UpdateAsync(RtAutoUpdateRequestDto requestDto);
        /// <summary>
        /// 删除房间配送信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RtAutoDeleteResponseDto> DeleteAsync(RtAutoDeleteRequestDto requestDto);
    }
}
