﻿using HdProject.Common.DTOs;
using HdProject.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Application.Services.Interfaces
{
    public interface IUserService
    {
        Task<LoginResult> LoginAsync(UserLoginDto loginDto);
        Task<User> GetUserByUsernameAsync(string username);
        Task<bool> ValidateUserCredentialsAsync(string username, string password);
        Task<UserDto> GetUserByIdAsync(int id);
        Task<LoginResult> RefreshTokenAsync(string refreshToken);
        Task<bool> ChangePasswordAsync(int userId, string oldPassword, string newPassword);
        // 其他用户管理方法可以根据需要添加
    }
}
