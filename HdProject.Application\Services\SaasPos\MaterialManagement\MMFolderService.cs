﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFolder;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMFolder;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Interfaces;
using SqlSugar;

namespace HdProject.Application.Services.SaasPos.MaterialManagement
{
    /// <summary>
    /// 文件夹业务逻辑类
    /// </summary>
    public class MMFolderService : IMMFolderService
    {
        private readonly IRepositorySaas<MMFolder> _repositorySaas;
        private readonly IMapper _mapper;
        private readonly ISqlSugarClient _sqlSugarClient;
        public MMFolderService(IRepositorySaas<MMFolder> repositorySaas,
            IMapper mapper, ISqlSugarClient sqlSugarClient)
        {
            _repositorySaas = repositorySaas;
            _mapper = mapper;
            _sqlSugarClient = sqlSugarClient;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }

        /// <summary>
        /// 新增文件夹
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        /// 
        public async Task<MMFolderAddFolderResponseDto> AddFolderAsync(MMFolderAddFolderRequestDto requestDto)
        {
            MMFolderAddFolderResponseDto responseDto = new MMFolderAddFolderResponseDto();
            responseDto.Index = await _repositorySaas.InsertAsync(new MMFolder { FolderName = requestDto.Model.FolderName });
            return responseDto;
        }
        /// <summary>
        /// 查询文件夹列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMFolderGetAllFolderResponseDto> GetAllFolderAsync(MMFolderGetAllFolderRequestDto requestDto)
        {
            MMFolderGetAllFolderResponseDto responseDto = new MMFolderGetAllFolderResponseDto();
            var resultlist = await GetAllFolderAndFileAsync();
            responseDto.Model = resultlist;
            return responseDto;
        }

        /// <summary>
        /// 查询文件夹列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<MMFolderDto>> GetAllFolderAndFileAsync()
        {
            //根据ID查询文件夹信息
            var Mfs = await _db.Queryable<MMFolder>().Where(a => !a.IsActive).Select(a =>
            new MMFolderDto
            {
                FolderID = a.FolderID,
                FolderName = a.FolderName,

            }).ToListAsync();
            if (Mfs.Count == 0)
            {
                throw new Exception("未查询到文件夹信息！");
            }
            foreach (var m in Mfs)
            {
                List<MMFileDto> MFD = new List<MMFileDto>();
                //查询文件夹素材关系表
                var Mfbf = await _db.Queryable<MMFolderBdFile>().Where(a => a.FolderID == m.FolderID).ToListAsync();
                foreach (var mb in Mfbf)
                {
                    var mf = await _db.Queryable<MMFile>().Where(a => a.FileID == mb.FileID).FirstAsync();
                    MFD.Add(new MMFileDto()
                    {
                        FileID = mf.FileID,
                        FileName = mf.FileName,
                        FilePath = mf.FilePath,
                        FormatType = mf.FormatType,
                        FileSize = mf.FileSize,
                        Width = mf.Width,
                        Height = mf.Height,
                        VideoDuration = mf.VideoDuration
                    });
                }
                m.mFileDtos = MFD;
            }
            return Mfs;
        }

        /// <summary>
        /// 根据ID查询文件夹详情
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMFolderGetByIdFolderResponseDto> GetByIdFolderAsync(MMFolderGeByIdFolderRequestDto requestDto)
        {
            MMFolderGetByIdFolderResponseDto responseDto = new MMFolderGetByIdFolderResponseDto();
            try
            {
                var result = await GetByIdAssociationBdAsync(requestDto.FolderID);
                responseDto.Model = result;
            }
            catch (Exception ex)
            {

                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        public async Task<MMFolderDto> GetByIdAssociationBdAsync(int id)
        {
            //根据ID查询文件夹信息
            var Mf = await _db.Queryable<MMFolder>().Where(a => a.FolderID == id && !a.IsActive).FirstAsync();
            if (Mf == null)
            {
                throw new Exception("未查询到该文件夹信息！");
            }
            var mfblist = await _db.Queryable<MMFolderBdFile>().Where(a => a.FolderID == id).ToListAsync();
            List<MMFileDto> mfModel = new List<MMFileDto>();
            foreach (var item in mfblist)
            {
                var mfresult = await _db.Queryable<MMFile>().Where(a => a.FileID == item.FileID).FirstAsync();
                mfModel.Add(new MMFileDto
                {
                    FileID = mfresult.FileID,
                    FileName = mfresult.FileName,
                    FilePath = mfresult.FilePath,
                    FileSize = mfresult.FileSize,
                    FormatType = mfresult.FormatType,
                    Height = mfresult.Height,
                    Width = mfresult.Width,
                    VideoDuration = mfresult.VideoDuration,
                });
            }
            return new MMFolderDto()
            {
                mFileDtos = mfModel
            };
        }
    }
}
