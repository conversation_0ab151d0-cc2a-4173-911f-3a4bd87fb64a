﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;

namespace HdProject.Infrastructure.Repositorys.Imp
{
    internal class RepositoryRms2019
    {
    }
    public class RepositoryRms2019<T> : Repository<T>, IRepositoryRms2019<T>
       where T : class, new()
    {
        public RepositoryRms2019(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "Rms2019";
        }
    }
}
