﻿using HdProject.Domain.Entities.SaasPos.CommodityManagement;
using HdProject.Domain.Interfaces;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Infrastructure.Repositories.Imp
{
    public class RepositorySaas<T> : Repository<T>, IRepositorySaas<T>
        where T : class, new()
    {
        public RepositorySaas(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "Saas";
        }

       
    }
}
