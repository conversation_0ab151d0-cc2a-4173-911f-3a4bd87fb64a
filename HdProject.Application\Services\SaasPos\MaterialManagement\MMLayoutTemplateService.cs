﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMLayout;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using SqlSugar;

namespace HdProject.Application.Services.SaasPos.MaterialManagement
{
    /// <summary>
    /// 布局模板服务接口实现类
    /// </summary>
    public class MMLayoutTemplateService : IMMLayoutTemplateService
    {
        private readonly IRepositorySaas<MMLayoutTemplate> _repositoryMMLayoutTemplate;
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IMapper _mapper;
        public MMLayoutTemplateService(IRepositorySaas<MMLayoutTemplate> repositoryMMLayoutTemplate, ISqlSugarClient sqlSugarClient, IMapper mapper)
        {
            _repositoryMMLayoutTemplate = repositoryMMLayoutTemplate;
            _sqlSugarClient = sqlSugarClient;
            _mapper = mapper;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMLayoutTemplateAddResponseDto> AddAsync(MMLayoutTemplateAddRequestDto requestDto)
        {
            MMLayoutTemplateAddResponseDto responseDto = new MMLayoutTemplateAddResponseDto();
            try
            {
                var resultModel = new MMLayoutTemplate()
                {
                    LayoutName = requestDto.Model.LayoutName,
                    LayoutCols = requestDto.Model.LayoutCols,
                    LayoutRows = requestDto.Model.LayoutRows,
                    LayoutDescription = requestDto.Model.LayoutDescription,
                    TemplateGridCount = requestDto.Model.TemplateGridCount,
                    CreatedBy = "张三",
                    CreatedTime = DateTime.Now
                };
                var result = await _repositoryMMLayoutTemplate.InsertAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMLayoutTemplateDeleteResponseDto> DeleteAsync(MMLayoutTemplateDeleteRequestDto requestDto)
        {
            MMLayoutTemplateDeleteResponseDto responseDto = new MMLayoutTemplateDeleteResponseDto();
            try
            {
                var resultModel = await _repositoryMMLayoutTemplate.GetFirstAsync(a => a.LayoutID == requestDto.LayoutID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法修改，请检查该模板是否存在！");
                }
                resultModel.IsActive = true;
                resultModel.DisabledBy = "";
                resultModel.DisabledTime = DateTime.Now;
                var result = await _repositoryMMLayoutTemplate.UpdateAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMLayoutTemplateGetAllResponseDto> GetAllAsync(MMLayoutTemplateGetAllRequestDto requestDto)
        {
            MMLayoutTemplateGetAllResponseDto responseDto = new MMLayoutTemplateGetAllResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<MMLayoutTemplate>(true);
                predicate = predicate.And(it => it.IsActive == false);//过滤非禁用的数据
                                                                      // 动态添加条件
                if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
                {
                    predicate = predicate.And(it => it.LayoutName.Contains(requestDto.QueryCriteria));
                }
                var result = await GetPageAllAssociationBdAsync(requestDto, predicate);
                //var model = _mapper.Map<List<MMDeviceDto>>(result);//使用AutoMapper进行对象属性映射
                //foreach (var item in result)
                //{
                //    var DeviceStatusModel = await _repositoryMMLayoutTemplateStatus.GetByIdAsync(item.OnlineStatusCode);//根据ID查询设备状态
                //    item.OnlineStatus = new MMDeviceStatusDto()
                //    {
                //        StatusID = DeviceStatusModel.StatusID,
                //        StatusName = DeviceStatusModel.StatusName,
                //        Remarks = DeviceStatusModel.Remarks,
                //    };
                //}
                var model = _mapper.Map<List<MMLayoutTemplateDto>>(result);
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 查询全部信息(分页)，需要绑定关联表信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<MMLayoutTemplate>> GetPageAllAssociationBdAsync(Pagination page, Expression<Func<MMLayoutTemplate, bool>> whereExpression = null)
        {
            RefAsync<int> totalCount = 0;
            var query = _db.Queryable<MMLayoutTemplate>().Includes(c => c.RegionList);

            if (whereExpression != null)
            {
                query = query.Where(whereExpression);
            }

            var list = await query.ToPageListAsync(page.Page, page.Rows, totalCount);
            page.Records = totalCount;
            return list;
        }

        /// <summary>
        /// 根据ID查询，需要绑定关联表信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MMLayoutTemplate> GetByIdAssociationBdAsync(int id)
        {
            return await _db.Queryable<MMLayoutTemplate>()
                .Includes(c => c.RegionList)
                .Where(c => c.LayoutID == id && c.IsActive == false)
                .FirstAsync();
        }
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMLayoutTemplateGetByIdResponseDto> GetByIdAsync(MMLayoutTemplateGetByIdRequestDto requestDto)
        {
            MMLayoutTemplateGetByIdResponseDto responseDto = new MMLayoutTemplateGetByIdResponseDto();
            try
            {
                var result = await GetByIdAssociationBdAsync(requestDto.LayoutID);
                if (result != null)
                {
                    var model = _mapper.Map<MMLayoutTemplateDto>(result);//使用AutoMapper进行对象属性映射
                    responseDto.Model = model;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMLayoutTemplateUpdateResponseDto> UpdateAsync(MMLayoutTemplateUpdateRequestDto requestDto)
        {
            MMLayoutTemplateUpdateResponseDto responseDto = new MMLayoutTemplateUpdateResponseDto();
            try
            {
                var resultModel = await _repositoryMMLayoutTemplate.GetFirstAsync(a => a.LayoutID == requestDto.Model.LayoutID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法修改，请检查该模板是否存在！");
                }
                var model = _mapper.Map<MMLayoutTemplate>(requestDto.Model);
                model.CreatedBy = resultModel.CreatedBy;
                model.CreatedTime = resultModel.CreatedTime;
                model.DisabledBy = resultModel.DisabledBy;
                model.DisabledTime = resultModel.DisabledTime;
                model.ModifiedBy = "张三";
                model.ModifiedTime = DateTime.Now;
                var result = await _repositoryMMLayoutTemplate.UpdateAsync(model);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
    }
}
