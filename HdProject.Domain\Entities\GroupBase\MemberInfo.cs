﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Entities.GroupBase
{
    public class MemberInfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid MemberKey { get; set; }
        public int RegShopId { get; set; }
        public DateTime MemberRegDate { get; set; }
        public string MemberName { get; set; }
        public string MemberSex { get; set; }
        public string MemberBirthday { get; set; }
        public string MemberIDNumber { get; set; }
        public string MemberPhoneNumber { get; set; }
        public string MemberAddress { get; set; }
        public string MemberCardTypeNo { get; set; }
        public int PointTotal { get; set; }
        public int IntegralTotal { get; set; }
        public int RechargeTotal { get; set; }
        public int ReturnTotal { get; set; }
        public string RegUserName { get; set; }
        public string UpdateUserName { get; set; }
        public string UpdateDate { get; set; }
        public int Val1 { get; set; }
        public int Val2 { get; set; }
        public string Val3 { get; set; }
        public string Val4 { get; set; }
        public string Val6 { get; set; }
        public string MemberPwd { get; set; }
        public string MemberCardRelationName { get; set; }
        public int RechargeTotalFrozen { get; set; }
    }
}
