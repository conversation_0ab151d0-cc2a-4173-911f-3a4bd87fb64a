﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.SongSyncCenter
{
    //路径信息表
    public partial class PathInfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public int PathId { get; set; }
        public int PathGroupNo { get; set; }
        public string PathAddress { get; set; }
        public string ServerName { get; set; }
        public int AcceCount { get; set; }
        public string SyncStatus { get; set; }
        public DateTime? SyncTime { get; set; }
        public string SyncVersion { get; set; }
    }
}
