﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Result.Page;
using SqlSugar;

namespace HdProject.Domain.Context.MainFood.Room
{
    /// <summary>
    /// 房型业务请求类
    /// </summary>
    public class RmTypeDto
    {
        [SugarColumn(IsPrimaryKey = true, Length = 2)]
        public string RtNo { get; set; }

        [SugarColumn(Length = 50)]
        public string? RtName { get; set; }

        public short MaxP { get; set; }
        public bool NoServ { get; set; }

        [SugarColumn(Length = 1)]
        public string AccType { get; set; }

        public int RmPrice { get; set; }
        public int? SRmPrice { get; set; }
        public int? WeekEndPrice { get; set; }
        public bool RealRoom { get; set; }
        public bool CanAutoZD { get; set; }
        public int MaxZDRate { get; set; }

        [SugarColumn(Length = 1)]
        public string RmCostType { get; set; }

        public int ServRate { get; set; }
        public int RmPrice_Person { get; set; }
        public int SRmPrice_Person { get; set; }
        public int WeekEndPrice_Person { get; set; }
        public int RmPrice_PerUnit { get; set; }
        public int SRmPrice_PerUnit { get; set; }
        public int WeekEndPrice_PerUnit { get; set; }
        public int UnitMinutes { get; set; }
        public int MinMinutesOfTimeZone { get; set; }
        public int MinMinutesOfTimeUnit { get; set; }
        public bool SetClearing { get; set; }

        [SugarColumn(ColumnName = "rowguid", IndexGroupNameList = new[] { "index_324196205" })]
        public Guid Rowguid { get; set; }

        [SugarColumn(ColumnName = "msrepl_tran_version")]
        public Guid MsreplTranVersion { get; set; }
    }
    /// <summary>
    /// 查询
    /// </summary>
    public class RmTypeGetAllAsyncRequestDto: Pagination
    {
        /// <summary>
        /// 房型分页查询请求参数
        /// </summary>
        //public string UserId { get; set; }
        public int? Status { get; set; }
        public DateTime? AllDates { get; set; }
        /// <summary>
        /// 查询条件字符串
        /// </summary>
        public string? QueryCriteria { get; set; }
        //public Pagination Paging { get; set; }
    }

    /// <summary>
    /// 根据房型编号查询房型信息
    /// </summary>
    public class RmTypeGetByIdAsyncRequestDto
    {
        public string RtNo { get; set; }
    }
    /// <summary>
    /// 新增房型信息请求类
    /// </summary>
    public class RmTypeAddRequestDto
    {
        public RmTypeDto Model { get; set; }
    }
    /// <summary>
    /// 删除房型信息请求类
    /// </summary>
    public class RmTypeDeleteRequestDto
    {
        public string? RmTypeID { get; set; }
    }
    /// <summary>
    /// 修改房型信息请求类
    /// </summary>
    public class RmTypeUpdateRequestDto
    {
        public RmTypeDto Model { get; set; }
    }
}
