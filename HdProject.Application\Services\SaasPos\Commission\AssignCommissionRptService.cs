﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SaasPos.Commission;
using HdProject.Domain.Context.SaasPos.Commission.CommissionRpt;
using HdProject.Domain.DTOs.SaasPos.Commission.CommissionRpt;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.SaasPos.Commission
{
    /// <summary>
    /// 指派提成统计报表
    /// </summary>
    public class AssignCommissionRptService : IAssignCommissionRptService
    {
        private readonly IRepositorySaas<CommissionRptDto> _repositorySaas;
        public AssignCommissionRptService(IRepositorySaas<CommissionRptDto>  repositorySaas)
        {
            _repositorySaas = repositorySaas;
        }
        /// <summary>
        /// 根据日期查询看房提成统计数据
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<CommissionRptResponseDto> GetRptAllAsync(CommissionRptRequestDto requestDto)
        {
            CommissionRptResponseDto responseDto = new CommissionRptResponseDto();
              //_repositorySaas.GetProcedureAsync();
            return responseDto;
        }
    }
}
