﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;
using HdProject.Domain.Entities.MainFood.Room;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.MainFood
{
    /// <summary>
    /// 房间类型接口实现类
    /// </summary>
    public class RoomTypeService : IRoomTypeService
    {
        private readonly IRepositoryMainFood<RmType> _repositoryMainFood;
        private readonly IMapper _mapper;
        public RoomTypeService(IRepositoryMainFood<RmType> repositoryMainFood, IMapper mapper)
        {
            _repositoryMainFood = repositoryMainFood;
            _mapper = mapper;
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RmTypeAddResponseDto> AddAsync(RmTypeAddRequestDto requestDto)
        {
            try
            {
                RmTypeAddResponseDto responseDto = new RmTypeAddResponseDto();
                var resultModel = _mapper.Map<RmType>(requestDto.Model);//使用AutoMapper进行对象属性映射
                resultModel.Rowguid = Guid.NewGuid(); //插入时自动生成新的Guid
                resultModel.MsreplTranVersion = Guid.NewGuid();
                //调用新增方法
                var result = await _repositoryMainFood.InsertAsync(resultModel);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("新增操作失败！");
            }
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RmTypeDeleteResponseDto> DeleteAsync(RmTypeDeleteRequestDto requestDto)
        {
            try
            {
                RmTypeDeleteResponseDto responseDto = new RmTypeDeleteResponseDto();
                var result = await _repositoryMainFood.DeleteAsync(it => it.RtNo.Equals(requestDto.RmTypeID));
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("删除操作失败！");
            }
        }
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<RmTypeGetAllAsyncResponseDto> GetAllAsync(RmTypeGetAllAsyncRequestDto requestDto)
        {
            RmTypeGetAllAsyncResponseDto responseDto = new RmTypeGetAllAsyncResponseDto();
            //var resultModel = await _repositoryMainFood.GetListAsync(it => it.InvNo.Contains(requestDto.QueryCriteria));
            //调用查询方法
            var result = await _repositoryMainFood.GetPageListAsync(requestDto, it => it.RtName.Contains(requestDto.QueryCriteria));
            var model = _mapper.Map<List<RmTypeDto>>(result);//使用AutoMapper进行对象属性映射
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RmTypeGetByIdAsyncResponseDto> GetByIdAsync(RmTypeGetByIdAsyncRequestDto requestDto)
        {
            RmTypeGetByIdAsyncResponseDto responseDto = new RmTypeGetByIdAsyncResponseDto();
            //调用查询方法
            var result = await _repositoryMainFood.GetByIdAsync(requestDto.RtNo);
            if (result == null) throw new Exception("查询信息失败！");
            var model = _mapper.Map<RmTypeDto>(result);//使用AutoMapper进行对象属性映射
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RmTypeUpdateResponseDto> UpdateAsync(RmTypeUpdateRequestDto requestDto)
        {
            try
            {
                RmTypeUpdateResponseDto responseDto = new RmTypeUpdateResponseDto();
                var resultModel = _mapper.Map<RmType>(requestDto.Model);//使用AutoMapper进行对象属性映射
                var existing = await _repositoryMainFood.GetFirstAsync(r => r.RtNo == resultModel.RtNo);
                // 保留原有rowguid
                resultModel.Rowguid = existing.Rowguid;
                resultModel.MsreplTranVersion = existing.MsreplTranVersion;
                var result = await _repositoryMainFood.UpdateAsync(resultModel);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("修改操作失败！");
            }
        }
    }
}
