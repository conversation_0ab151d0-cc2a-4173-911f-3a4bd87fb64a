﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MaterialManagement
{
    /// <summary>
    /// 文件表
    /// </summary>
    [SugarTable("MM_File")]
    public class MMFile
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int FileID { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string FormatType { get; set; }
        public long? FileSize { get; set; }
        public string? UploadedBy { get; set; }
        public DateTime UploadedTime { get; set; }
        public string? ThumbnailPath { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public float? VideoDuration { get; set; }
        public bool IsActive { get; set; }
        public string? DisabledBy { get; set; }
        public DateTime? DisabledTime { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedTime { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedTime { get; set; }
    }
    /// <summary>
    /// 文件夹表
    /// </summary>
    [SugarTable("MM_Folder")]
    public class MMFolder
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int FolderID { get; set; }
        public string FolderName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// 文件绑定素材表
    /// </summary>
    [SugarTable("MM_FolderBdFile")]
    public class MMFolderBdFile
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int FbfID { get; set; }
        /// <summary>
        /// 文件夹表ID
        /// </summary>
        public int FolderID { get; set; }
        /// <summary>
        /// 素材表ID
        /// </summary>
        public int FileID { get; set; }
    }

}
