﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Result.Page;

namespace HdProject.Domain.Context.SaasPos.Commission.AssignShowings
{
    public class AssignShowingsGetAllRequestDto : Pagination
    {
        /// <summary>
        /// 分页查询请求参数
        /// </summary>
        //public string UserId { get; set; }
        public int? Status { get; set; }
        public DateTime? AllDates { get; set; }
        /// <summary>
        /// 查询条件字符串
        /// </summary>
        public string? QueryCriteria { get; set; }
    }
}
