﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;
using HdProject.Domain.Entities.MainFood.Room;
using HdProject.Domain.Interfaces;
using LinqKit;
using SqlSugar;

namespace HdProject.Application.Services.MainFood
{
    /// <summary>
    /// 房价服务接口实现类
    /// </summary>
    public class RtTimePriceService : IRtTimePriceService
    {
        private readonly IRepositoryMainFood<RtTimePrice> _repositoryMainFood;
        private readonly IMapper _mapper;
        public RtTimePriceService(IRepositoryMainFood<RtTimePrice> repositoryMainFood, IMapper mapper)
        {
            _repositoryMainFood = repositoryMainFood;
            _mapper = mapper;
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RtTimePriceAddResponseDto> AddAsync(RtTimePriceAddRequestDto requestDto)
        {
            try
            {
                RtTimePriceAddResponseDto responseDto = new RtTimePriceAddResponseDto();
                var resultModel = new RtTimePrice()
                {
                    RtNo = requestDto.Model.RtNo,
                    DayOfWeek = requestDto.Model.DayOfWeek,
                    FromTime = requestDto.Model.FromTime,
                    ToTime = requestDto.Model.ToTime,
                    RmPrice = requestDto.Model.RmPrice,
                    SRmPrice = requestDto.Model.SRmPrice,
                    WeekEndPrice = requestDto.Model.WeekEndPrice,
                    DiscRate = requestDto.Model.DiscRate
                };
                //var resultModel = _mapper.Map<RtTimePrice>(requestDto.Model);//使用AutoMapper进行对象属性映射
                //resultModel.Rowguid = Guid.NewGuid(); //插入时自动生成新的Guid
                resultModel.MsreplTranVersion = Guid.NewGuid();
                //调用新增方法
                var result = await _repositoryMainFood.InsertAsync(resultModel);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("新增操作失败！");
            }
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>

        public async Task<RtTimePriceDeleteResponseDto> DeleteAsync(RtTimePriceDeleteRequestDto requestDto)
        {
            try
            {
                RtTimePriceDeleteResponseDto responseDto = new RtTimePriceDeleteResponseDto();

                var predicate = PredicateBuilder.New<RtTimePrice>(true);
                predicate = predicate.And(it => it.RtNo.Equals(requestDto.RtTimePriceID));

                // 动态添加条件
                if (requestDto.DayOfWeek.Trim() != "")
                {
                    predicate = predicate.And(it => it.DayOfWeek.Equals(requestDto.DayOfWeek));
                }
                if (requestDto.FromTime.Trim() != "")
                {
                    predicate = predicate.And(it => it.FromTime.Equals(requestDto.FromTime));
                }
                if (requestDto.ToTime.Trim() != "")
                {
                    predicate = predicate.And(it => it.ToTime.Equals(requestDto.ToTime));
                }
                var result = await _repositoryMainFood.DeleteAsync(predicate);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("删除操作失败！");
            }
        }
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RtTimePriceGetAllAsyncResponseDto> GetAllAsync(RtTimePriceGetAllAsyncRequestDto requestDto)
        {
            RtTimePriceGetAllAsyncResponseDto responseDto = new RtTimePriceGetAllAsyncResponseDto();
            //调用查询方法
            var result = await _repositoryMainFood.GetPageListAsync(requestDto, it => it.RtNo.Equals(requestDto.QueryCriteria));
            var model = result.Select(r => new RtTimePriceDto
            {
                RtNo = r.RtNo,
                DayOfWeek = r.DayOfWeek,
                FromTime = r.FromTime,
                ToTime = r.ToTime,
                RmPrice = r.RmPrice,
                SRmPrice = r.SRmPrice,
                WeekEndPrice = r.WeekEndPrice,
                DiscRate = r.DiscRate,
                MsreplTranVersion = r.MsreplTranVersion
            }).ToList();
            // var model = _mapper.Map<List<RtTimePriceDto>>(result);//使用AutoMapper进行对象属性映射
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RtTimePriceGetByIdAsyncResponseDto> GetByIdAsync(RtTimePriceGetByIdAsyncRequestDto requestDto)
        {
            RtTimePriceGetByIdAsyncResponseDto responseDto = new RtTimePriceGetByIdAsyncResponseDto();
            var predicate = PredicateBuilder.New<RtTimePrice>(true);
            predicate = predicate.And(it => it.RtNo.Equals(requestDto.RtNo));
            // 动态添加条件
            if (requestDto.DayOfWeek.Trim() != "")
            {
                predicate = predicate.And(it => it.DayOfWeek.Equals(requestDto.DayOfWeek));
            }
            if (requestDto.FromTime.Trim() != "")
            {
                predicate = predicate.And(it => it.FromTime.Equals(requestDto.FromTime));
            }
            if (requestDto.ToTime.Trim() != "")
            {
                predicate = predicate.And(it => it.ToTime.Equals(requestDto.ToTime));
            }
            //调用查询方法
            var result = await _repositoryMainFood.GetFirstAsync(predicate);
            if (result == null) throw new Exception("查询信息失败！");
            var model = new RtTimePriceDto
            {
                RtNo = result.RtNo,
                DayOfWeek = result.DayOfWeek,
                FromTime = result.FromTime,
                ToTime = result.ToTime,
                RmPrice = result.RmPrice,
                SRmPrice = result.SRmPrice,
                WeekEndPrice = result.WeekEndPrice,
                DiscRate = result.DiscRate,
                MsreplTranVersion = result.MsreplTranVersion
            };
            //var model = _mapper.Map<RtTimePriceDto>(result);//使用AutoMapper进行对象属性映射
            responseDto.Model = model;
            return responseDto;
        }
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<RtTimePriceUpdateResponseDto> UpdateAsync(RtTimePriceUpdateRequestDto requestDto)
        {
            try
            {
                RtTimePriceUpdateResponseDto responseDto = new RtTimePriceUpdateResponseDto();
                //var resultModel = _mapper.Map<RtTimePrice>(requestDto.Model);//使用AutoMapper进行对象属性映射
                var resultModel = new RtTimePrice
                {
                    RtNo = requestDto.Model.RtNo,
                    DayOfWeek = requestDto.Model.DayOfWeek,
                    FromTime = requestDto.Model.FromTime,
                    ToTime = requestDto.Model.ToTime,
                    RmPrice = requestDto.Model.RmPrice,
                    SRmPrice = requestDto.Model.SRmPrice,
                    WeekEndPrice = requestDto.Model.WeekEndPrice,
                    DiscRate = requestDto.Model.DiscRate
                };
                var existing = await _repositoryMainFood.GetFirstAsync(r => r.RtNo == resultModel.RtNo);
                // 保留原有rowguid
                //resultModel.Rowguid = existing.Rowguid;
                resultModel.MsreplTranVersion = existing.MsreplTranVersion;
                var result = await _repositoryMainFood.UpdateAsync(resultModel);
                if (result > 0)
                {
                    responseDto.IsResult = true;
                }
                else
                {
                    responseDto.IsResult = false;
                }
                return responseDto;
            }
            catch (Exception)
            {
                throw new Exception("修改操作失败！");
            }
        }
    }
}
