using HdProject.Application.Services.Interfaces.GrouponBase.Banking;
using HdProject.Common.DTOs.Banking;
using HdProject.Domain.Entities.GroupBase;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;

namespace HdProject.Application.Services.GrouponBase.Banking
{
    public class BankService : IBankService
    {
        private readonly IRepositoryGroupBase<Dim_Bank> _repo;

        public BankService(IRepositoryGroupBase<Dim_Bank> repo)
        {
            _repo = repo;
        }

        public async Task<int> CreateAsync(BankCreateDto dto)
        {
            var entity = new Dim_Bank { BankSK = dto.BankSK, BankName = dto.BankName };
            return await _repo.InsertAsync(entity);
        }

        public async Task<int> DeleteAsync(int id)
        {
            return await _repo.DeleteAsync(id);
        }

        public async Task<Dim_Bank?> GetByIdAsync(int id)
        {
            return await _repo.GetByIdAsync(id);
        }

        public async Task<List<Dim_Bank>> GetListAsync(BankQueryDto query)
        {
            var page = query as Pagination;
            return await _repo.GetPageListAsync(page,
                string.IsNullOrWhiteSpace(query.Keyword)
                    ? null
                    : b => b.BankName!.Contains(query.Keyword));
        }

        public async Task<int> UpdateAsync(int id, BankUpdateDto dto)
        {
            var entity = await _repo.GetByIdAsync(id);
            if (entity == null) return 0;
            entity.BankName = dto.BankName;
            return await _repo.UpdateAsync(entity);
        }

        public async Task<int> CountAsync(string? keyword)
        {
            return await _repo.CountAsync(string.IsNullOrWhiteSpace(keyword) ? null : b => b.BankName!.Contains(keyword));
        }
    }

    public class BankDealService : IBankDealService
    {
        private readonly IRepositoryGroupBase<Dim_Bank_Deal> _repo;

        public BankDealService(IRepositoryGroupBase<Dim_Bank_Deal> repo)
        {
            _repo = repo;
        }

        public async Task<int> CreateAsync(BankDealCreateDto dto)
        {
            var entity = new Dim_Bank_Deal
            {
                DealSK = dto.DealSK,
                BankSK = dto.BankSK,
                FdNo = dto.FdNo,
                DealName = dto.DealName,
                DealAmount = dto.DealAmount,
                SubsidyAmount = dto.SubsidyAmount,
                TotalAmount = dto.TotalAmount,
                ServiceFee = dto.ServiceFee,
                NetAmount = dto.NetAmount
            };
            return await _repo.InsertAsync(entity);
        }

        public async Task<int> DeleteAsync(int id)
        {
            return await _repo.DeleteAsync(id);
        }

        public async Task<Dim_Bank_Deal?> GetByIdAsync(int id)
        {
            return await _repo.GetByIdAsync(id);
        }

        public async Task<List<Dim_Bank_Deal>> GetListAsync(BankDealQueryDto query)
        {
            var page = query as Pagination;
            return await _repo.GetPageListAsync(page, d =>
                (query.BankSK == null || d.BankSK == query.BankSK) &&
                (string.IsNullOrEmpty(query.FdNo) || d.FdNo.Contains(query.FdNo)) &&
                (string.IsNullOrEmpty(query.DealName) || d.DealName.Contains(query.DealName))
            );
        }

        public async Task<int> UpdateAsync(int id, BankDealUpdateDto dto)
        {
            var entity = await _repo.GetByIdAsync(id);
            if (entity == null) return 0;
            entity.BankSK = dto.BankSK;
            entity.FdNo = dto.FdNo;
            entity.DealName = dto.DealName;
            entity.DealAmount = dto.DealAmount;
            entity.SubsidyAmount = dto.SubsidyAmount;
            entity.TotalAmount = dto.TotalAmount;
            entity.ServiceFee = dto.ServiceFee;
            entity.NetAmount = dto.NetAmount;
            return await _repo.UpdateAsync(entity);
        }

        public async Task<int> CountAsync(int? bankSk, string? fdNo, string? dealName)
        {
            return await _repo.CountAsync(d =>
                (bankSk == null || d.BankSK == bankSk) &&
                (string.IsNullOrEmpty(fdNo) || d.FdNo.Contains(fdNo)) &&
                (string.IsNullOrEmpty(dealName) || d.DealName.Contains(dealName))
            );
        }
    }
}
