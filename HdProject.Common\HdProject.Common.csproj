﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.8" />
	</ItemGroup>

	<ItemGroup>
    <ProjectReference Include="..\HdProject.Domain\HdProject.Domain.csproj" />
  </ItemGroup>

</Project>
