﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using HdProject.Common.DTOs;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Common.Global.Configs;
using Microsoft.IdentityModel.Logging;
using HdProject.Domain.Entities;
using HdProject.Domain.Interfaces;
using HdProject.Application.Services.Interfaces.SaasPos;
using System.Text.RegularExpressions;
using System.Net.Http;
using HdProject.Domain.Result.Page;
using Azure;
using SqlSugar;
using HdProject.Domain.DTOs;
using HdProject.Domain.Context;
using System.Linq.Expressions;
using Furion.LinqBuilder;
namespace HdProject.Application.Services.SaasPos
{


    public class ShopBookLeaderService : IShopBookLeaderService
    {


        //私有字段（依赖的仓储接口），每个仓储对应一个数据库表
        private readonly IRepositorySaas<BookLeaderInfo> _leaderInfoRepository;
        private readonly IRepositorySaas<Shop_BookCacheInfo> _shop_BookCacheInfoRepository;
        private readonly IRepositorySaas<BookLeaderWithdrawRecord> _bookLeaderWithdrawRecordRepository;
        private readonly IRepositorySaas<BookLeaderCommission> _bookLeaderCommissionRepository;
        private readonly IRepositorySaas<BookLeaderPosterStats> _bookLeaderPosterStatsRepository;
        private readonly IRepositorySaas<BookLeaderSettlement> _bookLeaderSettlementRepository;
        /// <summary>
        ///  构造函数（依赖注入）；依赖被赋值给对应的字段
        /// </summary>
        /// <param name="leaderRepository"></param>
        /// <param name="shop_BookCacheInfoRepository"></param>
        /// <param name="bookLeaderWithdrawRecordRepository"></param>
        /// <param name="bookLeaderCommissionRepository"></param>
        ///
        public ShopBookLeaderService(IRepositorySaas<BookLeaderInfo> leaderRepository,
            IRepositorySaas<Shop_BookCacheInfo> shop_BookCacheInfoRepository,
            IRepositorySaas<BookLeaderWithdrawRecord> bookLeaderWithdrawRecordRepository,
            IRepositorySaas<BookLeaderPosterStats> bookLeaderPosterStatsRepository,
            IRepositorySaas<BookLeaderSettlement> bookLeaderSettlementRepository,
            IRepositorySaas<BookLeaderCommission> bookLeaderCommissionRepository)
        {
            _leaderInfoRepository = leaderRepository;//团长个人信息表
            _shop_BookCacheInfoRepository = shop_BookCacheInfoRepository; //团长预约记录表
            _bookLeaderWithdrawRecordRepository = bookLeaderWithdrawRecordRepository;//团长提现记录表
            _bookLeaderCommissionRepository = bookLeaderCommissionRepository;//团长佣金表
            _bookLeaderPosterStatsRepository = bookLeaderPosterStatsRepository;//团长海报统计表
            _bookLeaderSettlementRepository = bookLeaderSettlementRepository;//团长结算表
        }

        /// <summary>
        ///  团长注册开团
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<LeaderInfoStatusDto> RegisterAsLeader(RegisterLeaderContext context)
        {

            // 1. 参数验证
            if (context == null)
            {
                throw new Exception("请求参数不能为空");
            }

            if (string.IsNullOrWhiteSpace(context.UserId) ||
                string.IsNullOrWhiteSpace(context.Name) ||
                string.IsNullOrWhiteSpace(context.Phone))
            {
                throw new Exception("用户ID、姓名、电话不能为空");
            }

            // 验证手机号格式
            if (!Regex.IsMatch(context.Phone, @"^1[3-9]\d{9}$"))
            {
                throw new Exception("手机号格式不正确");
            }

            // 检查手机号是否已注册
            var existingLeader = await _leaderInfoRepository.GetFirstAsync(w => w.Phone == context.Phone);

            if (existingLeader != null)
            {
                throw new Exception("该手机号已注册");
            }

            // 3. 数据准备
            var leaderInfo = new BookLeaderInfo
            {
                LeaderId = Guid.NewGuid(),
                Name = context.Name.Trim(),
                Phone = context.Phone,
                BankCard = "0",
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now,
                IsDeleted = false,
                UserId = context.UserId
            };

            // 生成唯一团长码
            // 获取当前最大ID
            var maxId = await _leaderInfoRepository.CountAsync();

            // 生成自增码，格式为LD+6位数字(不足补零)
            string leaderCode = "LD" + (maxId + 1).ToString("D8");

            // 4. 数据库操作
            try
            {
                leaderInfo.LeaderCode = leaderCode;
                _leaderInfoRepository.BeginTran();
                // 插入团长信息
                await _leaderInfoRepository.InsertAsync(leaderInfo);
                // 初始化佣金账户
                var commission = new BookLeaderCommission
                {
                    CommissionId = Guid.NewGuid(),
                    LeaderId = leaderInfo.LeaderId,
                    TotalAmount = 0,
                    AvailableAmount = 0,
                    WithdrawnAmount = 0,
                    PendingAmount = 0,
                    FrozenAmount = 0,
                    WithdrawPendingAmount = 0,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    IsDeleted = false
                };
                // 插入佣金账户信息
                await _bookLeaderCommissionRepository.InsertAsync(commission);
                // 提交事务
                _leaderInfoRepository.CommitTran();

                var leaderInfoStatusDto = new LeaderInfoStatusDto
                {
                    Status = "Success",
                    LeaderId = leaderInfo.LeaderId,
                };

                // 5. 返回成功状态和团长id
                return leaderInfoStatusDto;
            }
            catch (Exception ex)
            {
                // 出现异常，回滚事务
                _leaderInfoRepository.RollbackTran();
                throw new Exception("注册失败：" + ex.Message);
            }
        }
        /// <summary>
        /// 获取团长个人信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<LeaderInfoWithCommissionDto> GetLeaderPersonalInfo(GetLeaderPersonalInfoContext context)
        {

            // 1. 参数验证
            if (context == null) throw new Exception("请求参数不能为空");
            if (string.IsNullOrWhiteSpace(context.UserId)) throw new Exception("团长ID不能为空");
            //_leaderInfoRepository.GetPageListAsync()
            // 2. 查询团长信息
            var leaderInfo = await _leaderInfoRepository.GetFirstAsync(w => w.UserId == context.UserId && !w.IsDeleted)
                ?? throw new InvalidOperationException($"用户ID为 {context.UserId} 的团长不存在或已被删除");

            // 3. 查询佣金信息
            var commissionInfo = await _bookLeaderCommissionRepository.GetFirstAsync(w => w.LeaderId == leaderInfo.LeaderId && !w.IsDeleted)
                ?? throw new InvalidOperationException($"团长ID为 {leaderInfo.LeaderId} 的佣金账户不存在");


            // 4. 合并数据并返回
            return new LeaderInfoWithCommissionDto
            {
                // 团长信息
                LeaderCode = leaderInfo.LeaderCode,
                Name = leaderInfo.Name,
                Phone = leaderInfo.Phone,
                // 佣金信息
                TotalAmount = commissionInfo.TotalAmount,
                WithdrawnAmount = commissionInfo.WithdrawnAmount,
                AvailableAmount = commissionInfo.AvailableAmount
            };




        }
        /// <summary>
        /// 获取团长码生成海报
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        //public async Task<GetPersonCommissionPosterModel> GetLeaderInvitationCode(GetLeaderInvitationCodeContext context)
        //{
        //    if (context == null || string.IsNullOrEmpty(context.UserId))
        //        throw new ArgumentException("未获取个人信息，无法生成海报！");

        //    try
        //    {
        //        // 1. 验证用户是否为有效的 leaderid
        //        var leaderInfo = await _leaderInfoRepository.GetFirstAsync(
        //            w => w.UserId == context.UserId && !w.IsDeleted
        //        ) ?? throw new ArgumentException($"用户ID {context.UserId} 不是有效团长，无法生成邀请码海报！");

        //        //2. 2.获取该团长的LeaderCode
        //        var leaderCode = leaderInfo.LeaderCode;
        //        if (string.IsNullOrWhiteSpace(leaderCode))
        //        {
        //            throw new InvalidOperationException("团长邀请码不存在");
        //        }

        //        // 2. 加载背景图像
        //        var imagePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory,
        //                                   GlobalConfig.Global.OrderExConfig.CommissionPosterPath);

        //        if (!File.Exists(imagePath))
        //            throw new FileNotFoundException("图片不存在该地址！", imagePath);

        //        // 3.下载QR码图像并与背景合并
        //        using (var httpClient = new HttpClient())
        //        using (var backgroundStream = new MemoryStream(await File.ReadAllBytesAsync(imagePath)))
        //        using (var qrCodeStream = await DownloadQrCodeAsync(httpClient, leaderCode))
        //        using (var mergedStream = MergeFileStreams(backgroundStream, qrCodeStream))
        //        {
        //            var bytes = new byte[mergedStream.Length];
        //            await mergedStream.ReadAsync(bytes, 0, (int)mergedStream.Length);

        //            return new GetPersonCommissionPosterModel { Bytes = bytes };
        //        }
        //    }
        //    catch (Exception ex) when (ex is ArgumentException || ex is FileNotFoundException)
        //    {
        //        throw; //重新抛出已知异常
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new Exception("获取个人团长海报失败!", ex); // Include inner exception
        //    }
        //}

        //private async Task<MemoryStream> DownloadQrCodeAsync(HttpClient httpClient, string leaderCode)
        //{
        //    var qrCodeUrl = $"{GlobalConfig.Global.OrderExConfig.HttpPath}" +
        //                   $"{GlobalConfig.Global.OrderExConfig.CommissionPoster}" +
        //                   $"{leaderCode}";

        //    var imageData = await httpClient.GetByteArrayAsync(qrCodeUrl);
        //    return new MemoryStream(imageData);
        //}
        /// <summary>
        /// 团长提现
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<bool> ApplyForLeaderWithdrawal(ApplyForLeaderWithdrawalContext context) // 修改返回类型为int
        {
            // 1. 参数验证
            if (context == null || string.IsNullOrWhiteSpace(context.UserId) ||
                string.IsNullOrWhiteSpace(context.Title) ||
                string.IsNullOrWhiteSpace(context.BankCard) ||
                context.Amount <= 0)
            {
                throw new Exception("请求参数不合法，用户ID、标题、提现金额(>0)、银行卡号不能为空");
            }
            // 规范化 BankCard（去除空格）
            context.BankCard = context.BankCard.Trim();
            if (!Regex.IsMatch(context.BankCard, @"^\d{16,19}$"))
            {
                throw new Exception("银行卡号必须是16-19位数字");
            }

            // 2. 获取团长信息
            var existingInfo = await _leaderInfoRepository.GetFirstAsync(w => w.UserId == context.UserId);
            if (existingInfo == null) throw new Exception("团长信息不存在");

            // 开始事务
            _leaderInfoRepository.BeginTran();
            try
            {
                // 更新银行卡信息
                existingInfo.BankCard = context.BankCard;
                await _leaderInfoRepository.UpdateAsync(existingInfo);

                // 3. 获取佣金信息
                var commissionInfo = await _bookLeaderCommissionRepository
                    .GetFirstAsync(w => w.LeaderId == existingInfo.LeaderId);
                if (commissionInfo == null) throw new Exception("佣金账户不存在");

                if (commissionInfo.AvailableAmount < context.Amount)
                {
                    throw new Exception("可提现金额不足");
                }

                // 4. 创建提现记录
                var withdrawRecord = new BookLeaderWithdrawRecord
                {
                    RecordId = Guid.NewGuid().ToString(),
                    LeaderId = existingInfo.LeaderId,
                    Title = context.Title,
                    WithdrawTime = DateTime.Now,
                    Status = 0, // 0表示申请中
                    Amount = context.Amount,
                    BankCard = context.BankCard,
                    OperatorId = "",
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    IsDeleted = false
                };

                // 记录资金变动
                commissionInfo.AvailableAmount -= context.Amount;//可提现金额
                commissionInfo.WithdrawPendingAmount += context.Amount;//已提现未到账金额

                // 更新佣金账户信息
                await _bookLeaderCommissionRepository.UpdateAsync(commissionInfo);

                // 将提现记录插入数据库
                await _bookLeaderWithdrawRecordRepository.InsertAsync(withdrawRecord);

                // 提交事务
                _leaderInfoRepository.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                // 回滚事务
                _leaderInfoRepository.RollbackTran();
                throw new Exception("提现申请失败: " + ex.Message);
            }
        }
        /// <summary>
        /// 获取团长预约记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<List<Shop_BookCacheInfo>> GetLeaderAppointmentRecords(GetLeaderAppointmentRecordsContext context)
        {
            try
            {
                // 1. 参数验证
                if (context == null)
                {
                    throw new ArgumentNullException(nameof(context));
                }
                if (string.IsNullOrWhiteSpace(context.UserId))
                {
                    throw new ArgumentException("团长ID不能为空", nameof(context.UserId));
                }

                // 2. 查询团长信息
                var leaderInfo = await _leaderInfoRepository.GetFirstAsync(w => w.UserId == context.UserId && !w.IsDeleted)
                    ?? throw new InvalidOperationException($"用户ID {context.UserId} 的团长不存在或已被删除");

                // 定义查询条件表达式
                Expression<Func<Shop_BookCacheInfo, bool>> whereExpression = w => w.LeaderId == leaderInfo.LeaderId;
                if (context.Status != null)
                {
                    whereExpression = whereExpression.And(w => w.BookStatus == context.Status);
                }

                if (context.AllDates != null)
                {
                    var filterDate = context.AllDates.Value.Date; // 只取日期部分

                    whereExpression = whereExpression.And(w =>
                        w.CreateTime.Date == filterDate);

                }



                // 6. 无筛选条件时返回分页数据
                var pageResult = await _shop_BookCacheInfoRepository.GetPageListAsync(context.Paging, whereExpression);

                if (!pageResult.Any())
                {
                    throw new Exception("预约记录不存在");
                }

                // 7. 映射结果
                return pageResult.Select(w => new Shop_BookCacheInfo
                {
                    Body = w.Body,
                    BookStatus = w.BookStatus,
                    CreateTime = w.CreateTime,
                    ComeDate = w.ComeDate,
                    ComeTime = w.ComeTime,
                    PayAmount = w.PayAmount
                }).ToList();
            }
            catch (ArgumentException ex)
            {
                // 参数验证异常直接抛出
                throw;
            }
            catch (InvalidOperationException ex)
            {
                // 业务逻辑异常直接抛出
                throw;
            }

        }
        /// <summary>
        /// 获取团长提现记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <summary>
        /// 获取团长提现记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<List<BookLeaderWithdrawRecord>> GetLeaderWithdrawalRecords(GetLeaderWithdrawalRecordsContext context)
        {
            try
            {
                // 1. 参数验证
                if (context == null)
                {
                    throw new ArgumentNullException(nameof(context));
                }
                if (string.IsNullOrWhiteSpace(context.UserId))
                {
                    throw new ArgumentException("团长ID不能为空", nameof(context.UserId));
                }

                // 2. 查询团长信息
                var leaderInfo = await _leaderInfoRepository.GetFirstAsync(w => w.UserId == context.UserId && !w.IsDeleted)
                    ?? throw new InvalidOperationException($"用户ID {context.UserId} 的团长不存在或已被删除");
                // 定义查询条件表达式
                Expression<Func<BookLeaderWithdrawRecord, bool>> whereExpression = w => w.LeaderId == leaderInfo.LeaderId;
                if (context.Status != null)
                {
                    whereExpression = whereExpression.And(w => w.Status == context.Status);
                }

                if (context.AllDates != null)
                {
                    var filterDate = context.AllDates.Value.Date; // 只取日期部分


                    whereExpression = whereExpression.And(w =>
                        w.CreateTime.HasValue &&
                        w.CreateTime.Value.Date == filterDate);

                }



                // 6. 无筛选条件时返回分页数据
                var pageResult = await _bookLeaderWithdrawRecordRepository.GetPageListAsync(context.Paging, whereExpression);
                if (!pageResult.Any())
                {
                    throw new Exception("提现记录不存在");
                }

                // 7. 映射结果
                return pageResult.Select(w => new BookLeaderWithdrawRecord
                {
                    Title = w.Title,
                    CreateTime = w.CreateTime,
                    Amount = w.Amount,
                    Status = w.Status,
                    WithdrawTime = w.WithdrawTime
                }).ToList();
            }
            catch (ArgumentException ex)
            {
                // 参数验证异常直接抛出
                throw;
            }
            catch (InvalidOperationException ex)
            {
                // 业务逻辑异常直接抛出
                throw;
            }
        }
        /// <summary>
        /// 合并图片
        /// </summary>
        /// <param name="fs1">背景图片</param>
        /// <param name="fs2">二维码图片</param>
        /// <returns></returns>
        //public MemoryStream MergeFileStreams(MemoryStream fs1, MemoryStream fs2)
        //{
        //    //打开图像流
        //    using (Image img1 = Image.FromStream(fs1))
        //    using (Image img2 = Image.FromStream(fs2))
        //    {//计算新图像的尺寸
        //        int width = Math.Max(img1.Width, img2.Width);
        //        int height = img1.Height;
        //        //创建新图像并绘制
        //        using (Bitmap mergedImage = new Bitmap(width, height))
        //        using (Graphics g = Graphics.FromImage(mergedImage))
        //        {//绘制第一张图像
        //            g.Clear(Color.White);
        //            g.DrawImage(img1, 0, 0);
        //            //计算第二张图像的位置和大小
        //            int w = img1.Width / 3;
        //            int h = img1.Height / 5;
        //            int x = (width - w) / 2; // 计算水平居中位置
        //            float y = img1.Height - h - (float)(h / 2.5); // 第二个图像的垂直起始位置
        //                                                          //绘制第二张图像:
        //            g.DrawImage(img2, x, y, w, h);
        //            // 创建内存流来存储合并后的图像
        //            MemoryStream ms = new MemoryStream();
        //            // 保存到内存流
        //            try
        //            {
        //                // 将合并后的图像保存到内存流
        //                mergedImage.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
        //                ms.Position = 0;

        //                return ms;
        //            }
        //            catch (Exception ex)
        //            {

        //                throw new Exception("保存到文件流异常"); // 重新抛出异常，或者根据需要处理
        //            }
        //        }
        //    }

        //}



        /// <summary>------------------------------------------
        /// 查询团长汇总报表
        /// </summary>
        public async Task<List<LeaderSummaryReportDto>> GetLeaderSummaryReport()
        {

            try
            {
                // 获取所有未删除的记录
                var leaderSettlements = await _bookLeaderSettlementRepository.GetListAsync(w => !w.IsDeleted);

                if (leaderSettlements == null || !leaderSettlements.Any())
                {
                    throw new InvalidOperationException("团长汇总表无有效信息");
                }

                // 筛选出已消费的订单
                var consumedOrders = leaderSettlements.Where(s => s.OrderStatus == 1).ToList();

                // 计算各项统计指标
                int orderTotal = leaderSettlements.Count();  // 订单总数
                decimal orderTotalAmount = consumedOrders.Sum(s => s.OrderAmount); // 订单总额（只计算已消费且未退款的订单）
                int leaderTotal = leaderSettlements.Select(s => s.LeaderId).Distinct().Count(); // 团长总数(去重）
                int sellingLeaderCount = consumedOrders.Select(s => s.LeaderId).Distinct().Count(); // 有销售行为的团长总数(去重）

                // 返回包含单一 LeaderSummaryReportDto 实例的列表
                return new List<LeaderSummaryReportDto>
        {
            new LeaderSummaryReportDto
            {
                OrderTotal = orderTotal,
                OrderTotalAmount = orderTotalAmount,
                LeaderTotal = leaderTotal,
                SellingLeaderCount = sellingLeaderCount
            }
        };
            }
            catch (ArgumentException ex)
            {
                // 参数验证异常直接抛出
                throw;
            }
            catch (InvalidOperationException ex)
            {
                // 业务逻辑异常直接抛出
                throw;
            }
        }

        /// <summary>
        /// 查询所有团长信息
        /// </summary>
        public async Task<List<LeaderInfoDto>> GetLeadersInfo(GetLeaderInfoListContext context)
        {
            try
            {
                // 1. 参数验证
                if (context == null)
                {
                    throw new ArgumentNullException("参数不能为空");
                }

                // 初始化查询条件
                Expression<Func<BookLeaderInfo, bool>> whereExpression = w => !w.IsDeleted;

                // 添加时间筛选条件
                if (context.RegisterStartTime != null && context.RegisterEndTime != null)
                {
                    var startDate = context.RegisterStartTime.Value.Date;
                    var endDate = context.RegisterEndTime.Value.Date;

                    whereExpression = whereExpression.And(w =>
                        w.CreateTime.HasValue &&
                        w.CreateTime.Value.Date >= startDate &&
                        w.CreateTime.Value.Date <= endDate);
                }
                else if (context.RegisterStartTime != null)
                {
                    var startDate = context.RegisterStartTime.Value.Date;
                    whereExpression = whereExpression.And(w =>
                        w.CreateTime.HasValue &&
                        w.CreateTime.Value.Date >= startDate);
                }
                else if (context.RegisterEndTime != null)
                {
                    var endDate = context.RegisterEndTime.Value.Date;
                    whereExpression = whereExpression.And(w =>
                        w.CreateTime.HasValue &&
                        w.CreateTime.Value.Date <= endDate);
                }

                // 使用仓储的分页方法获取分页数据
                var pagedLeaders = await _leaderInfoRepository.GetPageListAsync(context.Paging, whereExpression);
                
                if (!pagedLeaders.Any())
                {
                    throw new InvalidOperationException("团长信息不存在");
                }

                // 获取所有相关的统计信息
                var leaderIds = pagedLeaders.Select(x => x.LeaderId).ToList();
                var stats = await _bookLeaderPosterStatsRepository.GetListAsync(s => leaderIds.Contains(s.LeaderId));

               
                // 构建最终结果
                var leaderInfoDtos = pagedLeaders
                    .Join(stats,
                        leader => leader.LeaderId,
                        stat => stat.LeaderId,
                        (leader, stat) => new LeaderInfoDto
                        {
                            LeaderSerialNumber = leader.LeaderCode,
                            LeaderName = leader.Name,
                            LeaderPhone = leader.Phone,
                            RegistrationTime = leader.CreateTime,
                            PosterViewCount = stat.ViewCount,
                            PosterOrderTotal = stat.OrderCount,
                            PosterOrderTotalAmount = stat.OrderAmount,
                            ConsumptionCount = stat.ConsumeCount,
                            ConsumptionAmount = stat.ConsumeAmount,
                        })
                    .ToList();
                // 更新总记录数
                context.Paging.Records = leaderInfoDtos.Count;

                return leaderInfoDtos;
            }
            catch (ArgumentException ex)
            {
                // 参数验证异常直接抛出
                throw;
            }
            catch (InvalidOperationException ex)
            {
                // 业务逻辑异常直接抛出
                throw;
            }
        }
        /// <summary>
        /// 查询所有团长订单信息
        /// </summary>
        public async Task<List<LeaderOrderInfoDto>> GetLeadersOrderInfo(GetOrderInfoListContext context)
        {
            try
            {
                // 1. 参数验证
                if (context == null)
                {
                    throw new ArgumentNullException("参数不能为空");
                }

                // 初始化查询条件
                Expression<Func<BookLeaderSettlement, bool>> settlementWhere = w => !w.IsDeleted;
                //Expression<Func<BookLeaderInfo, bool>> infoWhere = w => !w.IsDeleted;

                // 添加订单状态筛选条件
                if (context.OrderStatus.HasValue)
                {
                    settlementWhere = settlementWhere.And(w => w.OrderStatus == context.OrderStatus.Value);
                }

                // 添加结算状态筛选条件
                if (context.SettlementStatus.HasValue)
                {
                    settlementWhere = settlementWhere.And(w => w.SettlementStatus == context.SettlementStatus.Value);
                }

                // 添加下单时间筛选条件
                if (context.OrderStartTime != null && context.OrderEndTime != null)
                {
                    var startDate = context.OrderStartTime.Value.Date;
                    var endDate = context.OrderEndTime.Value.Date;

                    settlementWhere = settlementWhere.And(w =>
                        w.OrderTime.HasValue &&
                        w.OrderTime.Value.Date >= startDate &&
                        w.OrderTime.Value.Date <= endDate);
                }
                else if (context.OrderStartTime != null)
                {
                    var startDate = context.OrderStartTime.Value.Date;
                    settlementWhere = settlementWhere.And(w =>
                        w.OrderTime.HasValue &&
                        w.OrderTime.Value.Date >= startDate);
                }
                else if (context.OrderEndTime != null)
                {
                    var endDate = context.OrderEndTime.Value.Date;
                    settlementWhere = settlementWhere.And(w =>
                        w.OrderTime.HasValue &&
                        w.OrderTime.Value.Date <= endDate);
                }

                // 使用分页方法获取结算数据
                var pagedSettlements = await _bookLeaderSettlementRepository.GetPageListAsync(context.Paging, settlementWhere);

                if (!pagedSettlements.Any())
                {
                    throw new Exception("团长订单信息不存在");
                }

                // 获取相关的团长信息
                var leaderIds = pagedSettlements.Select(x => x.LeaderId).ToList();
                var infos = await _leaderInfoRepository.GetListAsync(x => leaderIds.Contains(x.LeaderId));

                // 构建最终结果
                var orderInfoDtos = pagedSettlements
                    .Join(infos,
                        settlement => settlement.LeaderId,
                        info => info.LeaderId,
                        (settlement, info) => new LeaderOrderInfoDto
                        {
                            OrderTime = settlement.OrderTime,
                            ConsumptionTime = settlement.ConsumeTime,
                            OrderNumber = settlement.OrderNumber,
                            OrderInfo = settlement.OrderInfo,
                            OrderAmount = settlement.OrderAmount,
                            OrderStatus = settlement.OrderStatus switch
                            {
                                0 => "未消费",
                                1 => "已消费",
                                2 => "已退款",
                                _ => ""
                            },
                            SettlementCommission = settlement.CommissionAmount,
                            LeaderName = info.Name,
                            LeaderPhone = info.Phone,
                            SettlementStatus = settlement.SettlementStatus == 0 ? "未结算" : "已结算",
                            LeaderBankCardNumber = info.BankCard
                        })
                    .ToList();

                return orderInfoDtos;
            }
            catch (ArgumentException ex)
            {
                // 参数验证异常直接抛出
                throw;
            }
            catch (InvalidOperationException ex)
            {
                // 业务逻辑异常直接抛出
                throw;
            }
        }
        /// <summary>
        /// 查询所有团长佣金计提信息
        /// </summary>
        public async Task<List<LeaderCommissionAccrualDto>> GetLeadersAccrueCommission(GetCommissionRecordsContext context)
        {
            try
            {
                // 1. 参数验证
                if (context == null)
                {
                    throw new ArgumentNullException("参数不能为空");
                }

                // 初始化查询条件
                Expression<Func<BookLeaderSettlement, bool>> settlementWhere = w => !w.IsDeleted;
                //Expression<Func<BookLeaderInfo, bool>> infoWhere = w => !w.IsDeleted;

                // 添加结算状态筛选条件
                if (context.SettlementStatus.HasValue)
                {
                    settlementWhere = settlementWhere.And(w => w.SettlementStatus == context.SettlementStatus.Value);
                }

                // 添加消费时间筛选条件
                if (context.ConsumeStartTime != null && context.ConsumeEndTime != null)
                {
                    var startDate = context.ConsumeStartTime.Value.Date;
                    var endDate = context.ConsumeEndTime.Value.Date;

                    settlementWhere = settlementWhere.And(w =>
                        w.ConsumeTime.HasValue &&
                        w.ConsumeTime.Value.Date >= startDate &&
                        w.ConsumeTime.Value.Date <= endDate);
                }
                else if (context.ConsumeStartTime != null)
                {
                    var startDate = context.ConsumeStartTime.Value.Date;
                    settlementWhere = settlementWhere.And(w =>
                        w.ConsumeTime.HasValue &&
                        w.ConsumeTime.Value.Date >= startDate);
                }
                else if (context.ConsumeEndTime != null)
                {
                    var endDate = context.ConsumeEndTime.Value.Date;
                    settlementWhere = settlementWhere.And(w =>
                        w.ConsumeTime.HasValue &&
                        w.ConsumeTime.Value.Date <= endDate);
                }

                // 使用分页方法获取结算数据
                var pagedSettlements = await _bookLeaderSettlementRepository.GetPageListAsync(context.Paging, settlementWhere);

                if (!pagedSettlements.Any())
                {
                    throw new Exception("团长佣金计提信息不存在");
                }

                // 获取相关的团长信息
                var leaderIds = pagedSettlements.Select(x => x.LeaderId).ToList();
                var infos = await _leaderInfoRepository.GetListAsync(x => leaderIds.Contains(x.LeaderId) && !x.IsDeleted);

                // 构建最终结果
                var commissionCalculationDtos = pagedSettlements
                    .Join(infos,
                        settlement => settlement.LeaderId,
                        info => info.LeaderId,
                        (settlement, info) => new LeaderCommissionAccrualDto
                        {
                            LeaderName = info.Name,
                            LeaderPhone = info.Phone,
                            ConsumptionTime = settlement.ConsumeTime,
                            SettlementOrderNumber = settlement.OrderNumber,
                            SettlementOrderInfo = settlement.OrderInfo,
                            SettlementOrderAmount = settlement.OrderAmount,
                            SettlementStatus = settlement.SettlementStatus == 0 ? "未结算" : "已结算",
                            LeaderBankCardNumber = info.BankCard
                        })
                    .ToList();

                return commissionCalculationDtos;
            }
            catch (ArgumentException ex)
            {
                // 参数验证异常直接抛出
                throw;
            }
            catch (InvalidOperationException ex)
            {
                // 业务逻辑异常直接抛出
                throw;
            }

        }


       


    }
}
