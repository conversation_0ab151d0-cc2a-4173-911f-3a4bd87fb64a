﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup Label="Globals">
		<SccProjectName>SAK</SccProjectName>
		<SccProvider>SAK</SccProvider>
		<SccAuxPath>SAK</SccAuxPath>
		<SccLocalPath>SAK</SccLocalPath>
	</PropertyGroup>

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper"  Version="12.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0"/>
		<PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
		<PackageReference Include="SqlSugarCore" Version="5.1.4.188"  />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\HdProject.Domain\HdProject.Domain.csproj" />
	</ItemGroup>

</Project>
