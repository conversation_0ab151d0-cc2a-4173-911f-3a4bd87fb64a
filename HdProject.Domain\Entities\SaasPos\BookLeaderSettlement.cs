﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos
{
    public partial class BookLeaderSettlement
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid SettlementId { get; set; }
        public Guid LeaderId { get; set; }
        public string OrderNumber { get; set; }
        public string OrderInfo { get; set; }
        public decimal OrderAmount { get; set; }
        public decimal CommissionAmount { get; set; }
        public byte OrderStatus { get; set; }
        public byte SettlementStatus { get; set; }
        
        public DateTime? OrderTime { get; set; }
        public DateTime? ConsumeTime { get; set; }
        public DateTime SettlementTime { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
    }
}
