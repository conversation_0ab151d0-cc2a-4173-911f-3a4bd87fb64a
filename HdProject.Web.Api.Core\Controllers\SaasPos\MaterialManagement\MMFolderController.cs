﻿using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFolder;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.MaterialManagement
{
    public class MMFolderController : PublicControllerBase
    {
        private readonly IMMFolderService _mMFolderService;
        private readonly ILogger<MMFolderController> _logger;
        public MMFolderController(IMMFolderService mMFolderService, ILogger<MMFolderController> logger)
        {
            _mMFolderService = mMFolderService;
            _logger = logger;


        }
        /// <summary>
        /// 新增文件夹的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> AddFolder([FromBody] MMFolderAddFolderRequestDto request)
        {
            var result = await _mMFolderService.AddFolderAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 查询全部文件夹
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllFolder([FromQuery] MMFolderGetAllFolderRequestDto request)
        {
            var result = await _mMFolderService.GetAllFolderAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 根据ID查询文件夹的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdFolder([FromQuery] MMFolderGeByIdFolderRequestDto request)
        {
            var result = await _mMFolderService.GetByIdFolderAsync(request);
            return ApiData(result.Model);
        }
    }
}
