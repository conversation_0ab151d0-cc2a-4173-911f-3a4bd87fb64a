﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\21b6059e-e8aa-4de0-bccf-388d3c1cc9fb.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>HdProject.Web.Api.Core</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/HdProject.Web.Api.Core</BasePath>
      <RelativePath>uploads/21b6059e-e8aa-4de0-bccf-388d3c1cc9fb.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>f9btyktdtv</Fingerprint>
      <Integrity>QyAI6iE4Y5lKRm9FgZ5HUG5Eygi33fH5l+TPPSgIY7A=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\21b6059e-e8aa-4de0-bccf-388d3c1cc9fb.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\3ea8934a-b383-4781-912e-775ee218b2c1.mp4'))">
      <SourceType>Package</SourceType>
      <SourceId>HdProject.Web.Api.Core</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/HdProject.Web.Api.Core</BasePath>
      <RelativePath>uploads/3ea8934a-b383-4781-912e-775ee218b2c1.mp4</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>u8wkmk2gxj</Fingerprint>
      <Integrity>wllZpFKtSHvjluWVLzewsTsV2IBo5D8VBhF0XShfvFE=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\3ea8934a-b383-4781-912e-775ee218b2c1.mp4'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\775e8bbe-5470-44a2-9fb8-a135a8bc7c18.png'))">
      <SourceType>Package</SourceType>
      <SourceId>HdProject.Web.Api.Core</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/HdProject.Web.Api.Core</BasePath>
      <RelativePath>uploads/775e8bbe-5470-44a2-9fb8-a135a8bc7c18.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>pcl150yaaf</Fingerprint>
      <Integrity>pxiqwSf457f7VtSLqwR6Zxcq60kRk4uy5VQfwhWDG9I=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\775e8bbe-5470-44a2-9fb8-a135a8bc7c18.png'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>