﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMPlaylist;

namespace HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement
{
    /// <summary>
    /// 节目单服务接口类
    /// </summary>
    public interface IMMPlaylistService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMPlaylistGetByIdAsyncResponseDto> GetByIdAsync(MMPlaylistGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<MMPlaylistGetAllAsyncResponseDto> GetAllAsync(MMPlaylistGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMPlaylistAddResponseDto> AddAsync(MMPlaylistAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMPlaylistUpdateResponseDto> UpdateAsync(MMPlaylistUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMPlaylistDeleteResponseDto> DeleteAsync(MMPlaylistDeleteRequestDto requestDto);
    }
}
