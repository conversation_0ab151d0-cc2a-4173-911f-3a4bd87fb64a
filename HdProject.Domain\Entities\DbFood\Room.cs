﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.DbFood
{
    [SugarTable("Room")]
    public class Room
    {
        [SugarColumn(ColumnName = "RmNo", IsPrimaryKey = true)]
        public string RmNo { get; set; }
        public string? RmName { get; set; }
        public string RtNo { get; set; }
        public string AreaNo { get; set; }
        public string? InvNo { get; set; }
        public string PriceNo { get; set; }
        public string? WorkDate { get; set; }
        public string? RsPos { get; set; }
        public string RmStatus { get; set; }

        public bool IsSDate { get; set; }
        public string? Rem { get; set; }
        public string? BookDate { get; set; }
        public string? BookTime { get; set; }

        public string? InDate { get; set; }

        public string? InTime { get; set; }
        public short? InNumbers { get; set; }

        public string? OpenUserId { get; set; }
        public string? AccUserId { get; set; }
        public string? AccDate { get; set; }

        public string? AccTime { get; set; }

        public string? ContinueUserId { get; set; }

        public string? ContinueTime { get; set; }
        public string? MemberNo { get; set; }

        public string? CustName { get; set; }

        public string? OrderUserId { get; set; }

        public int DiscRate { get; set; }

        public int? Serv { get; set; }

        public int? FdCost { get; set; }

        public int? RmCost { get; set; }

        public int? Disc { get; set; }

        public int? ZD { get; set; }

        public int? BeerZD { get; set; }

        public int? BeerCash { get; set; }
        public int? Tax { get; set; }

        public int MorePayed { get; set; }

        public int? Tot { get; set; }
        public bool WC { get; set; }

        public bool Dance { get; set; }

        public string PrnFIndex { get; set; }

        public string PrnDIndex { get; set; }

        public short? PInvCount { get; set; }

        public string? FromRmNo { get; set; }

        public short? OpenCount { get; set; }

        public bool ForceNoServ { get; set; }

        public int? Tag { get; set; }

        public int FixedDisc { get; set; }

        public string? CarId { get; set; }

        public int? FdCost_InRmCost { get; set; }
        public int? FdCost_NotInRmCost { get; set; }

        public bool MembDisc { get; set; }

        public int? MembCard { get; set; }

        public string? Card_MNo { get; set; }

        public int? CardAmount { get; set; }
        public string? CloseTime { get; set; }
        public bool CallAccount { get; set; }

        public string? BadReason { get; set; }

        public string? BadUserId { get; set; }
        public int AutoZD { get; set; }
        public Guid rowguid { get; set; }

        public Guid msrepl_tran_version { get; set; }
        public int th_RmCost { get; set; }

        public string AccountManagerID { get; set; }
        public string AccountManagerCName { get; set; }

        public string CustomerServiceManagerID { get; set; }

        public string CustomerServiceManagerName { get; set; }

        public string? FromTime { get; set; }

        public string? ToTime { get; set; }

        public int? SubServ { get; set; }

        public string? UpRtNo { get; set; }
    }

}
