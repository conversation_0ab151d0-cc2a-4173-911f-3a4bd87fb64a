{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "AllowedHosts": "*",
  "ConnectionStrings": {
    "Default": "Data Source=************;Initial Catalog=test;User ID=sa;Password=***;Encrypt=True;TrustServerCertificate=True;",
    "Saas": "Data Source=************;Initial Catalog=saas.pos;User ID=sa;Password=***;Encrypt=True;TrustServerCertificate=True;",
    "MainFood": "Data Source=************;Initial Catalog=mainfood;User ID=sa;Password=***;Encrypt=True;TrustServerCertificate=True;",
    "Rms": "Data Source=************;Initial Catalog=RMS2019;User ID=sa;Password=***;Encrypt=True;TrustServerCertificate=True;",
    "Redis": "************:6379,password=***,abortConnect=false",
    "SongBase": "Data Source=************;Initial Catalog=SongBase;User ID=sa;Password=***;Encrypt=True;TrustServerCertificate=True;",
    "GroupBase": "Data Source=***********;Initial Catalog=GrouponBase;User ID=sa;Password=Musicbox***;Encrypt=True;TrustServerCertificate=True;",
    //"GroupBase2": "Data Source=***********;Initial Catalog=GrouponBase;User ID=sa;Password=Musicbox***;Encrypt=True;TrustServerCertificate=True;",
    "MIMS": "Data Source=***********;Initial Catalog=MIMS;User ID=sa;Password=Musicbox***;Encrypt=True;TrustServerCertificate=True;",
    "WxInfo": "Data Source=***********;Initial Catalog=WxInfo;User ID=sa;Password=Musicbox***;Encrypt=True;TrustServerCertificate=True;",
    "OperateData": "Data Source=***********;Initial Catalog=OperateData;User ID=sa;Password=Musicbox***;Encrypt=True;TrustServerCertificate=True;",
    "dbfood": "Data Source=************;Initial Catalog=dbfood;User ID=sa;Password=***;Encrypt=True;TrustServerCertificate=True;",
    "Rms2009": "Data Source=************;Initial Catalog=Rms2009;User ID=sa;Password=***;Encrypt=True;TrustServerCertificate=True;",
    "Rms2019": "Data Source=193.112.2.229;Initial Catalog=rms2019;User ID=sa;Password=************;Encrypt=True;TrustServerCertificate=True;"
  },
    "WeChat": {
        "AppId": "wx8ad48bb7e973f237",
        "AppSecret": "0ea42745ded99e20507890d1f0b99eab",
        "EnvId": "cloudutils-jfdyd"
    },
    "JwtSettings": {
        "SecretKey": "YourVeryLongSecretKeyHere***4567890",
        "Issuer": "HdProjectApi",
        "Audience": "HdProjectClient",
        "AccessTokenExpirationMinutes": 360,
        "RefreshTokenExpirationDays": 7
    },
    "FileUpload": {
        "UploadFolder": "uploads", //广告机播放上传的素材路径
        "WallPaintingUploadFolder": "wpuploads", //播放壁画上传的文件路径
        "MaxFileSize": 1073741824, // 50MB
        "ThumbnailSize": 200,
        "AllowedImageTypes": [
            "image/jpeg",
            "image/png",
            "image/gif",
            ".jpg",
            ".jpeg",
            ".png",
            ".gif"
        ],
        "AllowedVideoTypes": [
            "video/mp4",
            "video/quicktime",
            ".mp4",
            ".mov"
        ],
        "AllowedAudioTypes": [
            "audio/mpeg",
            "audio/mp3",
            ".mp3"
        ]
    }
}