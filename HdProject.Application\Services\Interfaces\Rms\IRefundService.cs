﻿using HdProject.Domain.Context.RMS;
using HdProject.Domain.Context.RMS.SummaryStoreTimeSlotDailyList;
using HdProject.Domain.DTOs.RMS;
using HdProject.Domain.DTOs.RMS.SummaryStoreTimeSlotDailyList;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static HdProject.Domain.Context.RMS.RefundContext;
using static HdProject.Domain.DTOs.RMS.RefundDto;

namespace HdProject.Application.Services.Interfaces.Rms
{
    /// <summary>
    /// 退款管理
    /// </summary>
    public interface IRefundService
    {
        /// <summary>
        /// 卡卷查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<CardDto>> GetRefundRecord(CardContext context);
        /// <summary>
        /// 申请退款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<ApplyRefundDto>> ApplyRefundRecord(ApplyRefundContext context);
        /// <summary>
        /// 申请退款列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<ApplyRefundListDto>> ApplyRefundListRecord(ApplyRefundListContext context);
        /// <summary>
        /// 退款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<bool> RefundRecord(ConfirmRefundContext context);

    }
}
