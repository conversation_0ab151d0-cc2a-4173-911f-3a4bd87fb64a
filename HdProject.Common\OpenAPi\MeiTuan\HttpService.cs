﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net.Security;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography.X509Certificates;

namespace HdProject.Common.OpenAPi.MeiTuan
{
    public class HttpService
    {
        private static readonly string BaseUrl = "https://api-open-cater.meituan.com";
        public static string Post(string url, Hashtable param)
        {

            #region 参数处理
            if (param == null) throw new Exception("无效的param");

         


            #endregion

            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidation);
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072 | SecurityProtocolType.Tls;
            HttpWebRequest client = (HttpWebRequest)WebRequest.Create(url);
            try
            {
                client.Method = "POST";

                client.ContentType = "application/x-www-form-urlencoded";

                // 构建x-www-form-urlencoded格式的参数字符串
                string formData = BuildFormUrlEncodedData(param);
                // 将数据写入请求体
                byte[] postData = Encoding.UTF8.GetBytes(formData);
                client.ContentLength = postData.Length;
                using (Stream requestStream = client.GetRequestStream())
                {
                    requestStream.Write(postData, 0, postData.Length);
                    requestStream.Flush();
                }

                WebResponse response = client.GetResponse();
                using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                {
                    string responseBody = reader.ReadToEnd();
                    if (string.IsNullOrEmpty(responseBody))
                        throw new Exception("未获取到响应信息！");

                    return responseBody;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("请求出现异常！" + ex.Message);
            }
            return "";
        }
        // 构建x-www-form-urlencoded格式的字符串
        private static string BuildFormUrlEncodedData(Hashtable param)
        {
            var sb = new StringBuilder();
            foreach (DictionaryEntry entry in param)
            {
                if (sb.Length > 0)
                    sb.Append("&");

                sb.Append(Uri.EscapeDataString(entry.Key.ToString()));
                sb.Append("=");
                sb.Append(Uri.EscapeDataString(entry.Value?.ToString() ?? ""));
            }
            return sb.ToString();
        }
        protected static bool CheckValidation(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            return true;
        }
    }
}
