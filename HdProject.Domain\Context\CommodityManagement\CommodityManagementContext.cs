﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.DTOs.CommodityManagement;
using HdProject.Domain.Result.Page;

namespace HdProject.Domain.Context.CommodityManagement
{
    public class CommodityManagementContext
    {
    }
    public class DraftContext
    {
        public string UserId { get; set; }//用户ID

        public Pagination Paging { get; set; }//分页
        public string? Keyword { get; set; } // 搜索关键词
    }
    public class DraftListContext
    {
        public string UserId { get; set; }//用户ID
        public Guid AdjustmentID { get; set; }//调整ID
        public Pagination Paging { get; set; }//分页
        public string? Keyword { get; set; } // 搜索关键词
    }
    public class DelectDraftRecordContext
    {
        public string UserId { get; set; }
        public Guid AdjustmentID { get; set; }
    }
    public class AdjustmentRequestContext
    {
        public string UserId { get; set; }
        public string Name { get; set; }
        public Guid? AdjustmentID { get; set; }
        public List<AddDraftRecordContext> AddDraftRecordList { get; set; }
    }

    public class AddDraftRecordContext
    {
        public string? FdCName { get; set; } //商品名称
        public string? ComboName { get; set; } //套餐名称
        public string Type { get; set; } //类型=CommodityAdjustmentDetail
        public int FdQty { get; set; } //商品数量
        public string FtNo { get; set; }
        public string? FdNo { get; set; } //商品编号
        public string? ComboNo { get; set; } //套餐编号
        public Guid? DetailID { get; set; } //调整详情ID
        public decimal MarketPrice { get; set; } //市场价=CommodityAdjustmentDetail
        public decimal SalePrice { get; set; } //实际售价=CommodityAdjustmentDetail
        public string PriceMode { get; set; } //价格模式=CommodityAdjustmentDetail
        public string ApplicableStores { get; set; } //适用门店=CommodityAdjustmentDetail
        public List<ComboPackageItemContext>? PackageItems { get; set; } //套餐包含的商品
    }

    public class ComboPackageItemContext
    {
        public string? PGFdNo { get; set; } //商品编号
        public int PGFdQty { get; set; } //商品数量
        public string CommodityPrice { get; set; } //商品价格
    }
    public class GetExistingGoodsContext
    {
        public string? Keyword { get; set; } // 搜索关键词
        public string? FtNo { get; set; } // 按类别筛选
        public bool? IsPackage { get; set; } // 是否只查询套餐
        public Pagination Paging { get; set; }
    }
    public class GetCommPublishedContext
    {
        public string UserId { get; set; }
        public Guid? AdjustmentId { get; set; } //调整ID
        public Pagination Paging { get; set; }
        public string? Keyword { get; set; } // 搜索关键词
    }

    // 历史调价
    public class CommodityPriceHistoryContext
    {
        public string? FdNo { get; set; }  // 商品编号
        public string? ComboNo { get; set; }  // 套餐编号
        public string? UserId { get; set; }

        public Pagination Paging { get; set; }
        public string? Keyword { get; set; } // 搜索关键词
    }
    /// <summary>
    /// 门店同步
    /// </summary>
    public class CommSynchronousStoreContext
    {

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }



        /// <summary>
        /// 调整单ID列表(逗号分隔)
        /// </summary>
        public string AdjustmentIDs { get; set; }
    }
}
