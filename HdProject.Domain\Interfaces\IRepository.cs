﻿using HdProject.Domain.Result.Page;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Interfaces
{
    public interface IRepository<T> where T : class
    {
        #region 查询操作
        Task<T> GetByIdAsync(object id);
        Task<T> GetFirstAsync(Expression<Func<T, bool>> whereExpression);
        Task<List<T>> GetListAsync();
        Task<List<T>> GetListAsync(Expression<Func<T, bool>> whereExpression);
        Task<List<T>> GetListAsync(string sql, object parameters = null);
        Task<List<T>> GetPageListAsync(Pagination page, Expression<Func<T, bool>> whereExpression = null);
        Task<bool> ExistsAsync(Expression<Func<T, bool>> whereExpression);
        Task<int> CountAsync(Expression<Func<T, bool>> whereExpression = null);
        /// <summary>
        /// 查询存储过程并分页
        /// </summary>
        /// <param name="page"></param>
        /// <param name="name"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        Task<List<T>> GetProcedureAsync(Pagination page, string name, object parameters = null);
        /// <summary>
        ///  查询存储过程
        /// </summary>
        /// <param name="name"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        Task<List<T>> GetAllByProcedureAsync( string name, object parameters = null);
        #endregion

        #region 增删改操作
        Task<int> InsertAsync(T entity);
        Task<int> InsertRangeAsync(List<T> entities);
        Task<int> UpdateAsync(T entity);
        Task<int> UpdateAsync(Expression<Func<T, bool>> whereExpression, Expression<Func<T, T>> updateExpression);
        Task<int> DeleteAsync(object id);
        Task<int> DeleteAsync(Expression<Func<T, bool>> whereExpression);
        #endregion

        #region 事务
        void BeginTran();
        void CommitTran();
        void RollbackTran();
        #endregion
    }
}
