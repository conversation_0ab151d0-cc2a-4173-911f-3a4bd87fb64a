﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.SongFeedback;
using HdProject.Domain.DTOs.SaasPos.SongFeedback;

namespace HdProject.Application.Services.Interfaces.SaasPos.SongManagement
{
    /// <summary>
    /// 缺歌登记接口
    /// </summary>
    public interface ISongFeedbackService
    {
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<SongFeedbackAddResponseDto> AddAsync(SongFeedbackAddRequestDto requestDto);
    }
}
