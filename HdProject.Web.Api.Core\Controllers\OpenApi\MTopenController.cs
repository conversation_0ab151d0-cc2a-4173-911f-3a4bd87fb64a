﻿using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Collections;
using HdProject.Common.OpenAPi.MeiTuan;
using Newtonsoft.Json.Linq;

namespace HdProject.Web.Api.Core.Controllers.OpenApi
{
    /// <summary>
    /// 美团服务接口
    /// </summary>
    public class MTopenController : PublicControllerBase
    {
        // 日志文件路径配置
        private static readonly string LogDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Logs");
        private static readonly string LogFilePath = Path.Combine(LogDirectory, "authcode_requests.log");
        private static readonly long MaxLogFileSize = 5 * 1024 * 1024; // 5MB

        [HttpGet("authcode")]
        /// <summary>
        /// 业务授权码回调
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> AuthCode(string businessId, string developerId, string code, string state)
        {
            Hashtable param = new Hashtable();
            param.Add("businessId", businessId);
            param.Add("code", code);
            param.Add("grantType", "authorization_code");
            param.Add("developerId", developerId);
            param.Add("charset", "utf-8");
            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            TimeSpan timeSpan = DateTime.UtcNow - epoch;
            long timestamp = (long)timeSpan.TotalSeconds;
            param.Add("timestamp", timestamp);
            //state=shopid|signKey;
            if (state == null) return ApiError("格式不正确");
            var stateParams = state.Split("|");
            if (stateParams.Length < 2) return ApiError("格式不正确");
            string shopid = stateParams[0], signKey = stateParams[1];
            param.Add("sign", SignUtil.getSign(signKey, param));

            var response = HttpService.Post("https://api-open-cater.meituan.com/oauth/token", param);
            object responseData = null;
            try
            {
                var respObj = JsonConvert.DeserializeObject<JObject>(response);
                responseData = new
                {
                    developerId,
                    businessId,
                    version = "2",
                    charset = "utf-8",
                    shopid,
                    signKey,
                    accessToken = respObj["data"]["accessToken"],
                    refreshToken = respObj["data"]["refreshToken"],
                    responsebody= response
                };

            }
            catch (Exception ex)
            {
                responseData = "response:" + response + "_" + ex.Message;
            }
            try
            {
                // 获取查询参数
                var queryParameters = Request.Query
                    .ToDictionary(q => q.Key, q => q.Value.ToString());

                // 创建日志条目
                var logEntry = new
                {
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    Path = Request.Path,
                    QueryParameters = queryParameters,
                    IP = HttpContext.Connection.RemoteIpAddress?.ToString(),
                    responseData
                };

                // 异步保存到文件（不阻塞当前请求）
                _ = Task.Run(() => AppendToLogFile(logEntry));

                return ApiData(responseData);
            }
            catch (Exception ex)
            {
                // 记录错误日志但不中断请求
                _ = Task.Run(() => AppendToLogFile(new
                {
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    Error = "记录日志时发生异常",
                    Exception = ex.Message
                }));

                return ApiData("ok");
            }
        }

        private async Task AppendToLogFile(object logEntry)
        {
            try
            {
                // 确保日志目录存在
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }

                // 检查文件大小，超过限制则轮转
                await RotateLogFileIfNeeded();

                // 序列化日志内容
                var logContent = JsonConvert.SerializeObject(logEntry, Formatting.Indented) + Environment.NewLine;

                // 异步写入文件
                await System.IO.File.AppendAllTextAsync(LogFilePath, logContent, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志文件失败: {ex.Message}");
            }
        }

        private async Task RotateLogFileIfNeeded()
        {
            try
            {
                var fileInfo = new FileInfo(LogFilePath);
                if (fileInfo.Exists && fileInfo.Length > MaxLogFileSize)
                {
                    var newPath = Path.Combine(
                        LogDirectory,
                        $"authcode_requests_{DateTime.Now:yyyyMMddHHmmss}.log");

                    // 异步移动文件
                    await Task.Run(() => System.IO.File.Move(LogFilePath, newPath));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"日志轮转失败: {ex.Message}");
            }
        }
    }
}
