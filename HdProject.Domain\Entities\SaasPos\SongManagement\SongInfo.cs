﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.SongSyncCenter
{
    /// <summary>
    /// 歌曲信息表
    /// </summary>
    public partial class SongInfo
    {
        /// <summary>
        /// 歌曲ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public int SongId { get; set; }

        /// <summary>
        /// 歌曲文件
        /// </summary>

        public string SongFile { get; set; }

        /// <summary>
        /// 歌曲名称
        /// </summary>

        public string SongName { get; set; }

        /// <summary>
        /// 拼音缩写
        /// </summary>

        public string PYStr { get; set; }

        /// <summary>
        /// 壁画缩写
        /// </summary>

        public string BHStr { get; set; }

        /// <summary>
        /// 歌曲时长
        /// </summary>

        public int SongLen { get; set; }

        /// <summary>
        /// 歌曲语言ID
        /// </summary>

        public int SongLanId { get; set; }

        /// <summary>
        /// 歌曲语言
        /// </summary>

        public string SongLan { get; set; }

        /// <summary>
        /// 歌星1
        /// </summary>

        public string Sing1 { get; set; }

        /// <summary>
        /// 歌星2
        /// </summary>

        public string Sing2 { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>

        public string FileType { get; set; }

        /// <summary>
        /// 视频类型
        /// </summary>

        public string VideoType { get; set; }

        /// <summary>
        /// 音量
        /// </summary>

        public int Volume { get; set; }

        /// <summary>
        /// 亮度
        /// </summary>

        public int Brightness { get; set; }

        /// <summary>
        /// 对比度
        /// </summary>

        public int Contrast { get; set; }

        /// <summary>
        /// 饱和度
        /// </summary>

        public int Saturation { get; set; }

        /// <summary>
        /// 等级
        /// </summary>

        public short Grade { get; set; }

        /// <summary>
        /// VCD音频
        /// </summary>

        public string VCDAudio { get; set; }

        /// <summary>
        /// DVD音频
        /// </summary>

        public string DVDAudio { get; set; }

        /// <summary>
        /// DVD音乐
        /// </summary>

        public string DVDMusic { get; set; }

        /// <summary>
        /// 输入日期
        /// </summary>

        public string InputDate { get; set; }

        /// <summary>
        /// 歌曲日期
        /// </summary>

        public string SongDate { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>

        public bool Valid { get; set; }

        /// <summary>
        /// 视频点播类型
        /// </summary>

        public string VodType { get; set; }

        /// <summary>
        /// 点击热度
        /// </summary>

        public int HotClick { get; set; }

        /// <summary>
        /// 介绍数
        /// </summary>

        public int IntroCount { get; set; }

        /// <summary>
        /// 是否最新
        /// </summary>

        public string IsNew { get; set; }

        /// <summary>
        /// 类别ID
        /// </summary>

        public int ClasId { get; set; }

        /// <summary>
        /// 版本
        /// </summary>

        public string Version { get; set; }

        /// <summary>
        /// 是否Disco
        /// </summary>

        public string IsDisco { get; set; }

        /// <summary>
        /// 音轨
        /// </summary>

        public int VoiceTrack { get; set; }

        /// <summary>
        /// 音乐曲目
        /// </summary>

        public int MusicTrack { get; set; }

        /// <summary>
        /// ..频道
        /// </summary>

        public int YcChannel { get; set; }

        /// <summary>
        /// ..频道
        /// </summary>

        public int BcChannel { get; set; }

        /// <summary>
        /// 光源编号
        /// </summary>

        public string LightNo { get; set; }

        /// <summary>
        /// 音量
        /// </summary>

        public int BcVolume { get; set; }

        /// <summary>
        /// 主题名称
        /// </summary>

        public string TopicName { get; set; }

        /// <summary>
        /// 新的视频类型
        /// </summary>

        public string NewVodType { get; set; }

        /// <summary>
        /// 新的收藏
        /// </summary>

        public string NewCollection { get; set; }

        /// <summary>
        /// 专辑名称
        /// </summary>

        public string AlbumName { get; set; }

        /// <summary>
        /// 发行年份
        /// </summary>

        public string ReleaseYear { get; set; }

        /// <summary>
        /// 歌曲风格
        /// </summary>

        public string SongStyle { get; set; }

        /// <summary>
        /// 版本类型
        /// </summary>

        public string VersionType { get; set; }

        /// <summary>
        /// 自定义分类ID
        /// </summary>

        public int? CustomCategoryId { get; set; }
    }
}
