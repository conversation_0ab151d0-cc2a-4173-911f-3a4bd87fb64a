﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context;
using HdProject.Domain.Context.CommodityManagement;
using HdProject.Domain.Context.SongManagement;
using HdProject.Domain.DTOs.CommodityManagement;
using HdProject.Domain.DTOs.SongManagement;
using HdProject.Domain.Entities.CommodityManagement;
using HdProject.Domain.Entities.SaasPos;

namespace HdProject.Application.Services.Interfaces.SaasPos.CommodityManagement
{
    //
    public interface ICommodityAdjustmentService
    {
        /// <summary>
        /// 获取草稿箱个人记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<GetDraftRecordDto>> GetCommDraftRecord(DraftContext context);
        /// <summary>
        /// 获取草稿详情个人记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<GetDraftRecorListdDto>> GetCommDraftListRecord(DraftListContext context);
        /// <summary>
        /// 删除草稿个人记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<bool> DeleteCommDraftRecord(DelectDraftRecordContext context);

        ///// <summary>
        ///// 添加草稿记录
        ///// </summary>
        ///// <param name="context"></param>
        ///// <returns></returns>
        Task<bool> AddOrUpdateCommDraftRecords(List<AdjustmentRequestContext> context);

        ///// <summary>
        ///// 查询已有商品
        ///// </summary>
        ///// <param name="context"></param>
        ///// <returns></returns>
        Task<List<CommodityDto>> GetCommExistingGoods(GetExistingGoodsContext context);
        ///// <summary>
        ///// 获取商品历史调价
        ///// </summary>
        ///// <param name="context"></param>
        ///// <returns></returns>
        Task<Dictionary<string, List<CommodityListDto>>> GetCommHistoricalPrice(CommodityPriceHistoryContext context);
        /// <summary>
        ///查看已发布
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<CommodityPublishedDto>> GetCommPublished(GetCommPublishedContext context);
        /// <summary>
        /// 同步门店
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<CommSyncResultDto> CommSynchronousStore(CommSynchronousStoreContext context);
    }

}
