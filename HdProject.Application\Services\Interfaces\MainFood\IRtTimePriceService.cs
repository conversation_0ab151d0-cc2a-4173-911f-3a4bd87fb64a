﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.DTOs.MainFood.Room;

namespace HdProject.Application.Services.Interfaces.MainFood
{
    /// <summary>
    /// 房价业务接口类
    /// </summary>
    public interface IRtTimePriceService
    {
        /// <summary>
        /// 根据ID查询房价信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RtTimePriceGetByIdAsyncResponseDto> GetByIdAsync(RtTimePriceGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 查询房价全部信息
        /// </summary>
        /// <returns></returns>
        Task<RtTimePriceGetAllAsyncResponseDto> GetAllAsync(RtTimePriceGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 新增房价信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RtTimePriceAddResponseDto> AddAsync(RtTimePriceAddRequestDto requestDto);
        /// <summary>
        /// 修改房价信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<RtTimePriceUpdateResponseDto> UpdateAsync(RtTimePriceUpdateRequestDto requestDto);
        /// <summary>
        /// 删除房价信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<RtTimePriceDeleteResponseDto> DeleteAsync(RtTimePriceDeleteRequestDto requestDto);
    }
}
