﻿
using System.Drawing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Logging;

using HdProject.Common.DTOs;

using HdProject.Domain.Entities.SaasPos;
using HdProject.Application.Services.SaasPos;
using HdProject.Application.Services.Interfaces;
using HdProject.Application.Services.Interfaces.SaasPos;
using HdProject.Web.Core;
using HdProject.Domain.Result.Page;
using HdProject.Domain.Context;

namespace HdProject.Web.Api.Core.Controllers.SaasPos
{
    /// <summary>
    /// 团长接口
    /// </summary>
    [Route("[controller]/[Action]")]
    public class ShopBookLeaderController : PublicControllerBase
    {
        private readonly IShopBookLeaderService _shopBookLeaderService;


        public ShopBookLeaderController(IShopBookLeaderService shopBookLeaderService)
            => _shopBookLeaderService = shopBookLeaderService;

        /// <summary>
        /// 团长注册开团
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>

        [HttpPost]
        public async Task<IActionResult> RegisterAsLeader(RegisterLeaderContext context)
        {
            var res = await _shopBookLeaderService.RegisterAsLeader(context);
            return ApiData(res);
        }


        /// <summary>
        /// 团长提现
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ApplyForLeaderWithdrawal(ApplyForLeaderWithdrawalContext context)
        {
            var res = await _shopBookLeaderService.ApplyForLeaderWithdrawal(context);
            return ApiData(res);
        }
        /// <summary>
        /// 获取团长预约记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]//Task<(List<Shop_BookCacheInfo> Data, Pagination Paging)>
        public async Task<IActionResult> GetLeaderAppointmentRecords( GetLeaderAppointmentRecordsContext context)
        {
            var res = await _shopBookLeaderService.GetLeaderAppointmentRecords(context);
            return ApiPaged(res, context.Paging);

        }
        /// <summary>
        /// 获取团长码生成海报
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        //[HttpGet]
        //public async Task<IActionResult> GetLeaderInvitationCode(GetLeaderInvitationCodeContext context)
        //{
        //    var res = await _shopBookLeaderService.GetLeaderInvitationCode(context);
        //    return ApiData(res);
        //}
        /// <summary>
        /// 获取团长个人信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetLeaderPersonalInfo(GetLeaderPersonalInfoContext context)
        {
            var res = await _shopBookLeaderService.GetLeaderPersonalInfo(context);
            return ApiData(res);
        }
        /// <summary>
        /// 获取团长提现记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetLeaderWithdrawalRecords(GetLeaderWithdrawalRecordsContext context)
        {
            var res = await _shopBookLeaderService.GetLeaderWithdrawalRecords(context);
            return ApiPaged(res, context.Paging);
        }
        /// <summary>
        /// 查询团长汇总报表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetLeaderSummaryReport()
        {
            var res = await _shopBookLeaderService.GetLeaderSummaryReport();
            return ApiData(res);
        }
        /// <summary>
        /// 查询所有团长信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetLeadersInfo(GetLeaderInfoListContext context)
        {
            var res = await _shopBookLeaderService.GetLeadersInfo(context);
            return ApiPaged(res, context.Paging);
        }
        /// <summary>
        /// 查询所有团长订单信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetLeadersOrderInfo(GetOrderInfoListContext context)
        {
            var res = await _shopBookLeaderService.GetLeadersOrderInfo(context);
            return ApiPaged(res, context.Paging);
        }
        /// <summary>
        /// 查询所有团长佣金计提信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetLeadersAccrueCommission(GetCommissionRecordsContext context)
        {
            var res = await _shopBookLeaderService.GetLeadersAccrueCommission(context);
            return ApiPaged(res, context.Paging);
        }
    }
}
