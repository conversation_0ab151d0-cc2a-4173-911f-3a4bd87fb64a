﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.Rms
{
    public class SummaryStoreTimeSlotDaily
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
       [SugarColumn(IsPrimaryKey = true)]
        public Guid Ikey { get; set; }

        /// <summary>
        /// 店别
        /// </summary>
        public int StoreID { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        public DateTime StatistDate { get; set; }

        /// <summary>
        /// 周
        /// </summary>
        public string Week { get; set; }

        /// <summary>
        /// 时段
        /// </summary>
        public string Period { get; set; }

        /// <summary>
        /// 预定数
        /// </summary>
        public int ReservatCount { get; set; }

        /// <summary>
        /// 取消数
        /// </summary>
        public int CancelCount { get; set; }

        /// <summary>
        /// 自来客
        /// </summary>
        public int StraightToCount { get; set; }

        /// <summary>
        /// 直落数
        /// </summary>
        public int StraightSetCount { get; set; }

        /// <summary>
        /// 开房数
        /// </summary>
        public int RoomCount { get; set; }

        /// <summary>
        /// 是否满房
        /// </summary>
        public int? IsFull { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public string? StaffID { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
    }
}
