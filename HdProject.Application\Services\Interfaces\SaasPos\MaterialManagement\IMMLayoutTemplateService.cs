﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMLayout;

namespace HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement
{
    /// <summary>
    /// 布局模板服务接口类
    /// </summary>
    public interface IMMLayoutTemplateService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMLayoutTemplateGetByIdResponseDto> GetByIdAsync(MMLayoutTemplateGetByIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<MMLayoutTemplateGetAllResponseDto> GetAllAsync(MMLayoutTemplateGetAllRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMLayoutTemplateAddResponseDto> AddAsync(MMLayoutTemplateAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMLayoutTemplateUpdateResponseDto> UpdateAsync(MMLayoutTemplateUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMLayoutTemplateDeleteResponseDto> DeleteAsync(MMLayoutTemplateDeleteRequestDto requestDto);
    }
}
