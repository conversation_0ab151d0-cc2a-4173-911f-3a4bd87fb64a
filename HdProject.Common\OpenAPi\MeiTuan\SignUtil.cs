﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Common.OpenAPi.MeiTuan
{
    public class SignUtil
    {

        public static string getSign(string signKey, Hashtable param)
        {
            ArrayList keys = new ArrayList(param.Keys);
            keys.Sort(); //按字母顺序进行排序
            string resultStr = "";
            foreach (string key in keys)
            {
                string value = param[key].ToString();
                if (key != "sign" && value != null && value != "")
                {
                    resultStr = resultStr + key + value;
                }
            }
            resultStr = signKey + resultStr;
            SHA1 sha1 = new SHA1CryptoServiceProvider();
            byte[] rstRes = sha1.ComputeHash(Encoding.UTF8.GetBytes(resultStr));
            string hex = BitConverter.ToString(rstRes, 0).Replace("-", string.Empty).ToLower();

            return hex;

        }

    }
}
