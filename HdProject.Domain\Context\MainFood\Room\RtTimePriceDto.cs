﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Result.Page;
using SqlSugar;

namespace HdProject.Domain.Context.MainFood.Room
{
    /// <summary>
    /// 房间价格业务请求类
    /// </summary>
    public class RtTimePriceDto
    {
        [SugarColumn(IsPrimaryKey = true, Length = 2)]
        public string RtNo { get; set; }

        [SugarColumn(IsPrimaryKey = true, Length = 1)]
        public string DayOfWeek { get; set; }

        [SugarColumn(IsPrimaryKey = true, Length = 5)]
        public string FromTime { get; set; }

        [SugarColumn(IsPrimaryKey = true, Length = 5)]
        public string ToTime { get; set; }

        public int RmPrice { get; set; }

        public int SRmPrice { get; set; }

        public int WeekEndPrice { get; set; }
        public int DiscRate { get; set; }

        [SugarColumn(ColumnName = "msrepl_tran_version")]
        public Guid MsreplTranVersion { get; set; }
    }
    /// <summary>
    /// 查询
    /// </summary>
    public class RtTimePriceGetAllAsyncRequestDto: Pagination
    {
        /// <summary>
        /// 房间价格分页查询请求参数
        /// </summary>
        //public string UserId { get; set; }
        public int? Status { get; set; }
        public DateTime? AllDates { get; set; }
        /// <summary>
        /// 查询条件字符串
        /// </summary>
        public string? QueryCriteria { get; set; }
        //public Pagination Paging { get; set; }
    }

    /// <summary>
    /// 根据条件查询房间价格信息
    /// </summary>
    public class RtTimePriceGetByIdAsyncRequestDto
    {
        public string? RtNo { get; set; }
        public string? DayOfWeek { get; set; }
        public string? FromTime { get; set; }
        public string? ToTime { get; set; }
    }
    /// <summary>
    /// 新增房间价格信息请求类
    /// </summary>
    public class RtTimePriceAddRequestDto
    {
        public RtTimePriceDto Model { get; set; }
    }
    /// <summary>
    /// 删除房间价格信息请求类
    /// </summary>
    public class RtTimePriceDeleteRequestDto
    {
        public string? RtTimePriceID { get; set; }
        public string? DayOfWeek { get; set; }
        public string? FromTime { get; set; }
        public string? ToTime { get; set; } 
    }
    /// <summary>
    /// 修改房间价格信息请求类
    /// </summary>
    public class RtTimePriceUpdateRequestDto
    {
        public RtTimePriceDto Model { get; set; }
    }
}
