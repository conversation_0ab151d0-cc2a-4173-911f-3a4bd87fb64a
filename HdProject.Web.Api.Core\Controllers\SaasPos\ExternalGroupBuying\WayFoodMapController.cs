﻿using System.Data;
using ExcelDataReader;
using HdProject.Application.Services.Interfaces.SaasPos.ExternalGroupBuying;
using HdProject.Domain.Context.SaasPos.ExternalGroupBuying;
using HdProject.Web.Api.Core.Utility;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.ExternalGroupBuying
{
    public class WayFoodMapController : PublicControllerBase
    {
        private readonly IWayFoodMapService _wayFoodMapService;
        private readonly ILogger<WayFoodMapController> _logger;
        public WayFoodMapController(IWayFoodMapService wayFoodMapService, ILogger<WayFoodMapController> logger)
        {
            _wayFoodMapService = wayFoodMapService;
            _logger = logger;
        }

        /// <summary>
        /// 查询团购全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllWayFoodMap([FromQuery] WayFoodMapGetAllRequestDto request)
        {
            var result = await _wayFoodMapService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }

        /// <summary>
        /// 新增外部团购的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> FoodMapAddValue([FromBody] WayFoodMapAddRequestDto request)
        {
            var result = await _wayFoodMapService.AddAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 修改外部团购的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> FoodMapUpdateValue([FromBody] WayFoodMapUpdateRequestDto request)
        {
            var result = await _wayFoodMapService.UpdateAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 导入
        /// </summary>
        /// <returns></returns>
        [HttpPost("Import")]
        public async Task<IActionResult> ImportWayFoodMap([FromForm] List<IFormFile> files)
        {
            var file = files.FirstOrDefault();
            WayFoodMapImportRequestDto requestDto = new WayFoodMapImportRequestDto();
            if (file == null || file.Length == 0)
            {
                throw new ArgumentNullException(nameof(file));
            }
            // 读取Excel文件
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);
            stream.Position = 0;

            using var reader = ExcelReaderFactory.CreateReader(stream);
            var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration()
            {
                ConfigureDataTable = _ => new ExcelDataTableConfiguration()
                {
                    UseHeaderRow = true // 使用第一行作为列名
                }
            });

            if (dataSet.Tables.Count == 0)
                return BadRequest("Excel文件中没有数据表");

            var dataTable = dataSet.Tables[0];

            // 转换为实体列表 - 根据你的实际实体类修改
            var importData = new List<WayFoodMapCreateDto>();
            int i = 0;
            foreach (DataRow row in dataTable.Rows)
            {
                i++;

                string platformStr = row["平台"]?.ToString();
                string SaasNoStr = row["天王食品编号"]?.ToString();
                short platformValue = 0; // 默认值
                if (!string.IsNullOrEmpty(SaasNoStr)) //当存在天王编号时才判断平台
                {
                    if (platformStr != "美团" && platformStr != "抖音" && platformStr != "内部")
                    {
                        throw new Exception("第" + i + "行，列名为平台，数据不规范！");
                    }
                    int bmid = -1;

                    if (string.IsNullOrEmpty(row["门店别名"]?.ToString()))
                    {
                        bmid = -1;
                    }
                    else
                    {
                        if (row["门店别名"].GetType() == typeof(string))
                        {
                            bmid = -1;
                        }
                        else if (row["门店别名"].GetType() == typeof(int) || row["门店别名"].GetType() == typeof(double))
                        {
                            bmid = Convert.ToInt32(row["门店别名"]);
                        }

                    }

                    if (!string.IsNullOrEmpty(platformStr))
                    {
                        // 尝试解析枚举
                        if (Enum.TryParse<PlatformEnumDto>(platformStr, out PlatformEnumDto platformEnum))
                        {
                            platformValue = (short)platformEnum;
                        }
                    }
                    var entity = new WayFoodMapCreateDto
                    {
                        // 根据Excel列映射到实体属性
                        FdNo = row["天王食品编号"]?.ToString(),
                        ThirdFdNo = row["外部食品编号"]?.ToString(),
                        Platform = platformValue,
                        StoreId = bmid,
                        Qty = Convert.ToInt32(row["数量"]),
                    };
                    importData.Add(entity);
                }
            }
            bool iscf = importData.GroupBy(w => w.ThirdFdNo).Any(g => g.Count() > 1);

            if (iscf)
            {
                throw new Exception("操作失败：文件中存在重复导入外部编码！");
            }
            requestDto.Model = importData;
            WayFoodMapDeleteRequestDto deModel = new WayFoodMapDeleteRequestDto() { Model = importData };
            var deIndex = await _wayFoodMapService.DeleteAsync(deModel);
            var result = await _wayFoodMapService.ImportAsync(requestDto);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 删除团购数据的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> FoodMapDeletedValue([FromBody] WayFoodMapSingleItemDeleteRequestDto requestDto)
        {
            var result = await _wayFoodMapService.SingleItemDeleteAsync(requestDto);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }


        /// <summary>
        /// 查询团购门店全部信息的接口(不分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetStoreAll")]
        public async Task<IActionResult> GetAllWayStoreMap()
        {
            var result = await _wayFoodMapService.GetStoreAllAsync();
            return ApiData(result.Model);
        }
    }
}
