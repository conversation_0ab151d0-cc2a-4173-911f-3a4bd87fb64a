﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.SaasPos
{
    internal class Shop_BookCacheInfoService
    {
        private readonly IRepositorySaas<Shop_BookCacheInfo> _Repository;
        public Shop_BookCacheInfoService(IRepositorySaas<Shop_BookCacheInfo> userRepository) => _Repository = userRepository;
        
        //private readonly IRepositorySaas<Shop_BookCacheInfo> _leaderCacheInfoRepository;
        //public ShopBookLeaderService(IRepositorySaas<Shop_BookCacheInfo> leaderRepository) => _leaderCacheInfoRepository = leaderRepository;
    }
}
