﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.CommodityManagement
{
    // 商品表（Commodity）

    public partial class Commodity
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public string FdNo { get; set; }
        public string FtNo { get; set; }
        public string FdCName { get; set; }
        public string? FdEName { get; set; }
        public int FdQty { get; set; }
        public string? Specification { get; set; }
        public string? BaseUnit { get; set; }
        public string ?StorageMethod { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public bool IsDeleted { get; set; }
       
    }
}
