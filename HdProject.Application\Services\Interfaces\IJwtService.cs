using HdProject.Common.DTOs;
using HdProject.Domain.Entities;
using System.Security.Claims;

namespace HdProject.Application.Services.Interfaces
{
    public interface IJwtService
    {
        TokenResponseDto GenerateToken(User user);
        ClaimsPrincipal ValidateToken(string token);
        ClaimsPrincipal ValidateRefreshToken(string refreshToken);
        Task<ClaimsPrincipal> RefreshClaims(ClaimsPrincipal principal);
    }
}