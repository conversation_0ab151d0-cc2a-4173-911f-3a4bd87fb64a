{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\Workspaces\\THDefault\\HdProject\\HdProject\\HdProject.Core\\HdProject.Core.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\Workspaces\\THDefault\\HdProject\\HdProject\\HdProject.Core\\HdProject.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\Workspaces\\THDefault\\HdProject\\HdProject\\HdProject.Core\\HdProject.Core.csproj", "projectName": "HdProject.Core", "projectPath": "C:\\Users\\<USER>\\source\\Workspaces\\THDefault\\HdProject\\HdProject\\HdProject.Core\\HdProject.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\Workspaces\\THDefault\\HdProject\\HdProject\\HdProject.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\Workspaces\\THDefault\\HdProject\\HdProject\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Furion": {"target": "Package", "version": "[4.9.7.42, )"}, "SqlSugarCore": {"target": "Package", "version": "[5.1.4.188, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}