<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <targets>
    <target name="file" xsi:type="File"    
      fileName="${basedir}/logs/log_${shortdate}.log"
      layout="${longdate}|${level:uppercase=true}|${logger}|${message} [${callsite:fileName=true:includeSourcePath=false:methodName=true:cleanNamesOfAsyncContinuations=true}:${callsite-linenumber}]${onexception:${newline}${exception:format=tostring}} ${event-properties:itemSeparator=|:pairSeparator==}"
      archiveFileName="${basedir}/logs/archive/archivelog_{#}.log"
      archiveEvery="Day"
      archiveNumbering="Rolling"    
      maxArchiveFiles="30"      
      concurrentWrites="true"   
      keepFileOpen="false"     
      encoding="utf-8" />
      
    <target name="errorfile" xsi:type="File"
      fileName="${basedir}/logs/error_${shortdate}.log"
      layout="${longdate}|${level:uppercase=true}|${logger}|${message} [${callsite:fileName=true:includeSourcePath=false:methodName=true:cleanNamesOfAsyncContinuations=true}:${callsite-linenumber}]${onexception:${newline}${exception:format=tostring}} ${all-event-properties}"
      archiveFileName="${basedir}/logs/archive/error_{#}.log"
      archiveEvery="Day"
      archiveNumbering="Rolling"
      maxArchiveFiles="30"
      concurrentWrites="true"
      keepFileOpen="false"
      encoding="utf-8" />
      
    <target name="console" xsi:type="ColoredConsole"
      layout="${longdate}|${level:uppercase=true}|${logger:shortName=true}|${message} [${callsite:fileName=true:includeSourcePath=false:methodName=true:cleanNamesOfAsyncContinuations=true}:${callsite-linenumber}]${onexception:${newline}${exception:format=message}} ${event-properties:itemSeparator=|:pairSeparator==}" />
  </targets>
  <rules>
    <!-- 应用业务逻辑的详细日志 -->
    <logger name="HdProject.*" minlevel="Debug" writeTo="file" />
    
    <!-- 框架组件只记录警告和错误 -->
    <logger name="Microsoft.*" minlevel="Warning" writeTo="file" />
    <logger name="System.*" minlevel="Warning" writeTo="file" />
    
    <!-- 特别关注的中间件可以单独配置 -->
    <logger name="Microsoft.AspNetCore.Hosting.Diagnostics" minlevel="Info" writeTo="file" />
    <logger name="Microsoft.EntityFrameworkCore.Database.Command" minlevel="Info" writeTo="file" />
    
    <!-- 所有错误级别日志单独存储，包含更详细信息 -->
    <logger name="*" minlevel="Error" writeTo="errorfile" />
    
    <!-- 控制台输出 -->
    <logger name="*" minlevel="Info" writeTo="console" />
  </rules>
</nlog>