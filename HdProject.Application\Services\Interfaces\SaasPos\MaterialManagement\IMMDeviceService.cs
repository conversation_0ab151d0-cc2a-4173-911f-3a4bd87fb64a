﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMDevice;

namespace HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement
{
    /// <summary>
    /// 设备业务接口类
    /// </summary>
    public interface IMMDeviceService
    {
        /// <summary>
        /// 根据设备ID查询设备关联信信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMDeviceGetDeviceDetailsAsyncResponseDto> GetDeviceDetails(MMDeviceGetDeviceDetailsAsyncRequestDto requestDto);
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMDeviceGetByIdAsyncResponseDto> GetByIdAsync(MMDeviceGetByIdAsyncRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<MMDeviceGetAllAsyncResponseDto> GetAllAsync(MMDeviceGetAllAsyncRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMDeviceAddResponseDto> AddAsync(MMDeviceAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMDeviceUpdateResponseDto> UpdateAsync(MMDeviceUpdateRequestDto requestDto);
        /// <summary>
        /// 保存设备的宽高
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMDeviceSaveWhResponseDto> SaveDeviceWhAsync(MMDeviceSaveWhRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MMDeviceDeleteResponseDto> DeleteAsync(MMDeviceDeleteRequestDto requestDto);
        /// <summary>
        /// 修改投放设备信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<MMDeviceUpdateLaunchCampaignResponseDto> UpdateLaunchAdCampaignAsyns(MMDeviceUpdateLaunchCampaignRequestDto requestDto);
    }
}
