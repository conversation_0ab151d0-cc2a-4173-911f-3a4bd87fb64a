﻿using HdProject.Application.Services.Interfaces.SaasPos.Commission;
using HdProject.Domain.Context.SaasPos.Commission.AssignEmployee;
using HdProject.Domain.Context.SaasPos.Commission.AssignShowings;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.Commission
{
    /// <summary>
    /// 看管指派接口控制器
    /// </summary>
    public class AssignShowingsController : PublicControllerBase
    {
        private readonly IAssignShowingsService _assignShowingsService;
        private readonly ILogger<AssignShowingsController> _logger;

        public AssignShowingsController(IAssignShowingsService assignShowingsService, ILogger<AssignShowingsController> logger)
        {
            _assignShowingsService = assignShowingsService;
            _logger = logger;
        }

        /// <summary>
        /// 根据房间ID查询看房历史记录列表的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetByRoom")]
        public async Task<IActionResult> GetByRoomAssignShowings([FromQuery] AssignShowingsGetByRoomRequestDto request)
        {
            var result = await _assignShowingsService.GetByRoomAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 根据ID查询看房信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdAssignShowings([FromQuery] AssignShowingsGetByIdRequestDto request)
        {
            var result = await _assignShowingsService.GetByIdAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 新增看房信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> AssignShowingsAddValue([FromBody] AssignShowingsAddRequestDto request)
        {
            var result = await _assignShowingsService.AddAsync(request);
            if (result.CfStatus == 580)
            {
                return ApiError(
                    message: result.CfMessage,
                    statusCode: result.CfStatus

                );
            }
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 修改看房信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> AssignShowingsUpdateValue([FromBody] AssignShowingsUpdateRequestDto request)
        {
            var result = await _assignShowingsService.UpdateAsync(request);
            if (result.CfStatus == 580)
            {
                return ApiError(
                    message: result.CfMessage,
                    statusCode: result.CfStatus

                );
            }
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 删除看房信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> AssignShowingsDeletedValue([FromBody] AssignShowingsDeleteRequestDto request)
        {
            var result = await _assignShowingsService.DeleteAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 传入门店和房号返回房间状态的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRoomStatus")]
        public async Task<IActionResult> GetRoomStatus([FromQuery] int ShopId, string RmNo,string InvNo)
        {
            return ApiData(
                new
                {
                    CommissionNum = 3//可享受提成人数

                }
            );
        }
    }
}
