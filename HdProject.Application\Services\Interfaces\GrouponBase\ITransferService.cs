﻿using HdProject.Domain.Context.GrouponBase;
using HdProject.Domain.DTOs.GrouponBase;
using HdProject.Domain.Entities.GroupBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Application.Services.Interfaces.GrouponBase
{
    public interface ITransferService
    {
        /// <summary>
        /// 可分享卡卷列表查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<GetTransferDto>> GetSharedCardList(GetTransferCardContext context);
        /// <summary>
        /// 转赠卡卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<TransferCardDto>> TransferCardRecord(TransferCardContext context);
        /// <summary>
        /// 领取卡卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<ClaimDto>> ClaimRecord(ClaimContext context);

       /// <summary>
       /// 根据ClaimKey获取卡卷列表
       /// </summary>
       /// <param name="context"></param>
       /// <returns></returns>
        Task<List<GetClaimKeyListDto>> GetClaimKeyList(GetClaimKeyListContext context);

        /// <summary>
        /// 获取已分享
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<GroupedGiftDto>> GetNoSharedCardList(GetNoSharedCardListContext context);

    }
}
