﻿using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.MainFood.Room
{
    /// <summary>
    /// 房间信息接口控制器
    /// </summary>
    public class RoomManageController : PublicControllerBase
    {
        private readonly IRoomService _roomService;
        private readonly ILogger<RoomManageController> _logger;
        public RoomManageController(IRoomService roomService, ILogger<RoomManageController> logger)
        {
            _roomService = roomService;
            _logger = logger;
        }
        /// <summary>
        /// 查询房间全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllRoom([FromQuery] RoomGetAllAsyncRequestDto request)
        {
            var result = await _roomService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询房间信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdRoom([FromQuery] RoomGetByIdAsyncRequestDto request)
        {
            var result = await _roomService.GetByIdAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 新增房间信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> RoomAddValue([FromBody] RoomAddRequestDto request)
        {
            var result = await _roomService.AddAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 修改房间信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> RoomUpdateValue([FromBody] RoomUpdateRequestDto request)
        {
            var result = await _roomService.UpdateAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 删除房间信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> RoomDeletedValue([FromBody] RoomDeleteRequestDto request)
        {
            var result = await _roomService.DeleteAsync(request);
            return ApiData(result);
        }
    }
}
