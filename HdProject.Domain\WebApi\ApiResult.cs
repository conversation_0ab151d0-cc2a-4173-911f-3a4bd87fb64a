﻿namespace HdProject.Domain.WebApi
{
    public class ApiResult
    {
        public object state { get; set; }
        public string message { get; set; }
        public object data { get; set; }
        public int code { get; set; }

        // 成功响应的静态方法
        public static ApiResult Success(object data, string message = "操作成功")
        {
            return new ApiResult { code = (int)ResultType.Success, message = message, state = true, data = data };
        }

        // 失败响应的静态方法
        public static ApiResult Fail(string message, ResultType code = ResultType.Error)
        {
            return new ApiResult { code = (int)code, message = message, state = false, data = null };
        }
    }
}
