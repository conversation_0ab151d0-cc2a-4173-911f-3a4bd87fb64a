﻿using Furion.DatabaseAccessor;
using Furion.LinqBuilder;
using HdProject.Application.Services.Interfaces.CommodityManagement;
using HdProject.Domain.Context;
using HdProject.Domain.Context.MarketingActivityManagement;
using HdProject.Domain.Context.RMS;
using HdProject.Domain.DTOs.RMS;
using HdProject.Domain.Entities.GroupBase;
using HdProject.Domain.Entities.Rms;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static HdProject.Domain.Context.MarketingActivityManagement.MarketingActivityContext;
using static HdProject.Domain.DTOs.MarketingActivityManagement.MarketingActivityDto;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
//using WeChatApiException = HdProject.Domain.Context.MarketingActivityManagement.MarketingActivityContext.WeChatApiException;

namespace HdProject.Application.Services.CommodityManagement
{
    /// <summary>
    /// 营销活动管理
    /// </summary>
    public class MarketingActivityService : IMarketingActivityService
    {
        private readonly IWeChatCloudDbService _weChatCloudDbService;
        private readonly IRepositoryGroupBase<NGrouponInfo> _ngrouponInfoRepository;//卡卷详情表
        public MarketingActivityService(IWeChatCloudDbService weChatCloudDbService,
           IRepositoryGroupBase<NGrouponInfo> ngrouponInfoRepository
            )
        {
            _weChatCloudDbService = weChatCloudDbService;
            _ngrouponInfoRepository = ngrouponInfoRepository;
        }

        /// <summary>
        /// 查询销售和卡卷管理信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<JObject> GetSalesAndCardsInfoRecord(GetSalesAndCardsInfoContext context)
        {
            try
            {
                string collection = "BasicsInfo";
                string query = "";
                int skip = (context.Paging.Page - 1) * context.Paging.Rows;

                // 1. 查询Sales列表（不传SalesId）
                if (context.TypeName == "Sales" && context.SalesId == null)
                {
                    var whereObj = new JObject
                    {
                        ["Type"] = "Sales"

                    };

                    if (!string.IsNullOrEmpty(context.ActivityName))
                    {
                        whereObj["title"] = new JObject
                        {
                            ["$regex"] = context.ActivityName,
                            ["$options"] = "i"
                        };
                    }

                    query = $@"
                db.collection('{collection}')
                .where({whereObj.ToString(Formatting.None)})
                .field({{
                    '_id': true,
                    'Type': true,
                    'SalesId': true,
                    'begintime': true,
                    'endtime': true,
                    'title': true,
                    'IsDelete': true,
                    'CreationTime': true,
                    'bana': true
                }})
                .orderBy('CreationTime', 'desc')
                .skip({skip})
                .limit({context.Paging.Rows})
                .get()";
                }
                // 2. 查询CardSheet列表（不传CardSheetId）
                else if (context.TypeName == "CardSheet" && context.CardSheetId == null)
                {
                    var whereObj = new JObject
                    {
                        ["Type"] = "CardSheet"

                    };

                    if (!string.IsNullOrEmpty(context.ActivityName))
                    {
                        whereObj["title"] = new JObject
                        {
                            ["$regex"] = context.ActivityName,
                            ["$options"] = "i"
                        };
                    }

                    query = $@"
                db.collection('{collection}')
                .where({whereObj.ToString(Formatting.None)})
                .field({{
                    '_id': true,
                    'Type': true,
                    'CardSheetId': true,
                    'begintime': true,
                    'endtime': true,
                    'title': true,
                    'IsDelete': true,
                    'CreationTime': true,
                    'bana': true
                }})
                .orderBy('CreationTime', 'desc')
                .skip({skip})
                .limit({context.Paging.Rows})
                .get()";
                }
                // 3. 查询单个Sales详情（传了SalesId）
                else if (context.TypeName == "Sales" && context.SalesId != null)
                {
                    var whereObj = new JObject
                    {
                        ["Type"] = "Sales",
                        ["SalesId"] = context.SalesId

                    };

                    query = $@"
                db.collection('{collection}')
                .where({whereObj.ToString(Formatting.None)})
                .get()";
                }
                // 4. 查询单个CardSheet详情（传了CardSheetId）
                else if (context.TypeName == "CardSheet" && context.CardSheetId != null)
                {
                    var whereObj = new JObject
                    {
                        ["Type"] = "CardSheet",
                        ["CardSheetId"] = context.CardSheetId

                    };

                    query = $@"
                db.collection('{collection}')
                .where({whereObj.ToString(Formatting.None)})
                .get()";
                }
                else
                {
                    throw new ArgumentException("无效的查询参数");
                }

                // 执行查询
                var result = await _weChatCloudDbService.QueryDatabaseAsync(query);
                var response = JObject.Parse(result);
                int total = response["pager"]?["Total"]?.Value<int>() ?? 0;

                // 处理返回的所有数据
                JArray resultArray = new JArray();
                if (response["data"] is JArray dataArray && dataArray.Count > 0)
                {
                    foreach (var item in dataArray)
                    {
                        var itemString = item?.ToString();
                        if (!string.IsNullOrEmpty(itemString))
                        {
                            try
                            {
                                var itemObj = JObject.Parse(itemString);

                                // //处理图片下载链接
                                var imageUrl = itemObj["bana"]?.ToString();
                                if (!string.IsNullOrEmpty(imageUrl))
                                {
                                    try
                                    {
                                        //拿到图片地址了
                                        var downloadUrl = await _weChatCloudDbService.DownloadImageToCloudStorage(imageUrl);

                                        itemObj["DownloadUrl"] = downloadUrl;


                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"获取图片下载链接失败: {ex.Message}");
                                    }
                                }

                                resultArray.Add(itemObj);
                            }
                            catch (JsonException ex)
                            {
                                Console.WriteLine($"解析JSON失败: {ex.Message}");
                                Console.WriteLine($"原始数据: {itemString}");
                                // 如果解析失败，返回原始字符串
                                resultArray.Add(itemString);
                            }
                        }
                    }
                }

                // 构建响应 - 返回所有数据
                return new JObject
                {
                    ["code"] = 200,
                    ["success"] = true,
                    ["message"] = "请求成功",
                    ["data"] = new JObject
                    {
                        ["list"] = resultArray,
                        ["pagination"] = new JObject
                        {
                            ["total"] = total,
                            ["currentPage"] = context.Paging.Page,
                            ["pageSize"] = context.Paging.Rows,
                            ["totalPages"] = (int)Math.Ceiling((double)total / context.Paging.Rows)
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                return new JObject
                {
                    ["code"] = 500,
                    ["success"] = false,
                    ["message"] = "服务器内部错误",
                    ["error"] = ex.Message
                };
            }
        }

        /// <summary>
        /// 新增、修改销售或卡卷管理信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<EditorSalesAndCardsInfoDto>> EditorSalesAndCardsInfoRecord([FromForm] EditorSalesAndCardsInfoContext context)
        {
            var resultList = new List<EditorSalesAndCardsInfoDto>();
            string? imageUrl = null;

            try
            {
                // 处理图片上传
                if (context.File != null && context.File.Length > 0)
                {
                    imageUrl = await UploadImage(context.File);
                }

                // 解析JSON数据
                var jsonData = JObject.Parse(context.Data);
                var dataType = jsonData["Type"]?.ToString();

                // 移除t字段
                //jsonData.Remove("t");

                switch (dataType)
                {
                    case "Sales":
                    case "CardSheet":
                        var result = await ProcessJsonData(
                            jsonData: jsonData.ToString(),
                            dataType: dataType,
                            idFieldName: dataType == "Sales" ? "SalesId" : "CardSheetId",
                            imageUrl: imageUrl,
                            originalData: jsonData);
                        resultList.Add(result);
                        break;

                    default:
                        throw new InvalidOperationException($"未知的数据类型: {dataType}");
                }

                return resultList;
            }
            catch (Exception ex)
            {

                throw new Exception("处理请求失败", ex);
            }
        }

        private async Task<EditorSalesAndCardsInfoDto> ProcessJsonData(
        string jsonData,
        string dataType,
        string idFieldName,
        string? imageUrl = null,
        JObject? originalData = null)
        {
            string collection = "BasicsInfo";

            var result = new EditorSalesAndCardsInfoDto { DataType = dataType };

            try
            {
                // 确保原始数据存在
                if (originalData == null)
                {
                    originalData = JObject.Parse(jsonData);
                }



                // 自动处理ID
                var isNew = !originalData.TryGetValue(idFieldName, out var idToken) || string.IsNullOrEmpty(idToken?.ToString());
                var idValue = isNew ? Guid.NewGuid().ToString() : idToken.ToString();
                originalData[idFieldName] = idValue;

                // 更新图片和时间
                if (!string.IsNullOrEmpty(imageUrl))
                {
                    originalData["bana"] = imageUrl;
                }
                else if (!isNew && originalData["bana"] != null)
                {
                    // 修改操作且没有新图片，保留原有图片URL
                    imageUrl = originalData["bana"].ToString();
                }

                originalData["CreationTime"] = isNew ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : originalData["CreationTime"];
                // 复制原始数据用于构建data字段，移除t字段
                var dataForJson = originalData.DeepClone() as JObject;
                if (dataForJson != null)
                {
                    dataForJson.Remove("t"); // 移除data字段中的t字段
                }
                // 构建数据库存储结构
                var dbDocument = new JObject();

                // 复制顶层字段到根级别（包括t字段和指定的数组字段）
                foreach (var property in originalData.Properties())
                {
                    // 特殊处理cardList数组，始终复制到根级别
                    if (property.Name == "cardList")
                    {
                        dbDocument[property.Name] = property.Value;
                        continue;
                    }

                    // 保留t字段在根级别
                    if (property.Name == "t")
                    {
                        dbDocument[property.Name] = property.Value;
                        continue;
                    }

                    // 其他字段按类型处理
                    if (property.Value.Type != JTokenType.Object && property.Value.Type != JTokenType.Array)
                    {
                        dbDocument[property.Name] = property.Value;
                    }
                }

                // 确保关键字段存在于根级别
                EnsureRootField(dbDocument, "Type", dataType);
                EnsureRootField(dbDocument, idFieldName, idValue);
                EnsureRootField(dbDocument, "CreationTime", originalData["CreationTime"]);
                EnsureRootField(dbDocument, "IsDelete", originalData["IsDelete"] ?? false);


                if (isNew)
                {
                    // 将处理后的JSON存入data字段（已移除t字段）
                    if (dataForJson != null)
                    {
                        dbDocument["data"] = JValue.CreateString(dataForJson.ToString(Formatting.None));
                    }
                    else
                    {
                        dbDocument["data"] = JValue.CreateString(originalData.ToString(Formatting.None));
                    }

                }
                else
                {//修改后data值处理
                    if (dataForJson != null)
                    {
                       //data
                        dataForJson.Remove("data");
                        string dataJson = dataForJson.ToString(Formatting.None);

                        /*string unescapedLevel1 = JsonConvert.DeserializeObject<string>(dataJson);
                        JObject parsed = JObject.Parse(unescapedLevel1);
                        string quotedJsonString = (string)JValue.CreateString(parsed.ToString(Formatting.None));*/
                        dbDocument["data"] = dataJson;

                    }

                }

                // 构建数据库命令
                var cleanJson = dbDocument.ToString(Formatting.None)
                            .Replace("\\", "\\\\")
                            .Replace("\"", "\\\"");

                string command;
                if (isNew)
                {
                    command = $"db.collection('{collection}').add({{data:{cleanJson}}})";
                }
                else
                {
                    // 检查 IsDelete 是否为 false（如果存在）
                    if (originalData["IsDelete"] != null && originalData["IsDelete"].Value<bool>())
                    {
                        throw new Exception("无法修改已删除的记录");
                    }
                    var documentId = originalData["_id"]?.ToString() ?? "";
                    string escapedId = documentId.Replace("\"", "\\\"");

                    command = $"db.collection(\"{collection}\")" +
                             $".doc(\"{escapedId}\")" +
                             $".update({{data:{cleanJson}}})";
                }

                // 执行数据库操作
                var response = isNew
                    ? await _weChatCloudDbService.AddeDatabaseCommandAsync(command)
                    : await _weChatCloudDbService.ExecuteDatabaseCommandAsync(command);

                var responseJson = JObject.Parse(response);

                //BasicsCou集合存储数据
                if (dataType == "Sales" && originalData["couTag"] != null && !string.IsNullOrEmpty(originalData["couTag"].ToString()))
                {
                    var salesId = idValue;
                    var couTagValue = originalData["couTag"].ToString();

                    // 1. 准备基础数据
                    var basicsCouDoc = new JObject
                    {
                        ["t"] = couTagValue,
                        ["SalesId"] = salesId,
                    };

                    // 2. 平铺所有G_id到根层级
                    if (originalData["cardList"] is JArray cardList)
                    {
                        foreach (var card in cardList)
                        {

                            if (card["G_id"] != null)
                            {
                                string gId = card["G_id"].ToString();
                                basicsCouDoc[gId] = 0; // 直接添加到根对象
                            }
                           
                        }
                    }

                    // 3. 格式化JSON
                    string basicsCouJson = basicsCouDoc.ToString(Formatting.None)
                        .Replace("\\", "\\\\")
                        .Replace("\"", "\\\"");

                    // 4. 构建操作命令
                    string commandCou;
                    string responseCou;
                    const string collectionName = "BasicsCou";

                    // 先查询是否已存在对应couTag的记录
                    string findCommand = $"db.collection('{collectionName}').where({{'SalesId':'{salesId}'}}).get()";
                    var findResponse = await _weChatCloudDbService.QueryDatabaseAsync(findCommand);
                    var findResponseJson = JObject.Parse(findResponse);

                    var existingDocs = findResponseJson["data"] as JArray;

                    if (findResponseJson["errcode"]?.Value<int>() == 0 && existingDocs != null && existingDocs.Count > 0)
                    {
                        // 存在则更新
                        var firstDoc = JObject.Parse(existingDocs[0].ToString());

                        // 4. 遍历 basicsCouDoc 的所有字段（排除 SalesId, _id, t）
                        foreach (var property in basicsCouDoc.Properties().ToList())
                        {
                            string fieldName = property.Name;

                            //保留字段（SalesId, t），跳过不处理
                            if (fieldName == "SalesId" || fieldName == "t")
                                continue;

                            // 如果 firstDoc 有相同字段，则保留 firstDoc 的值（不强制设为 0）
                            if (firstDoc[fieldName] != null)
                            {
                                basicsCouDoc[fieldName] = firstDoc[fieldName];
                            }
                            // 如果 firstDoc 没有该字段，保持默认 0（basicsCouDoc 已初始化）
                        }

                        if (firstDoc["_id"] != null)
                        {
                            string escapedDocId = firstDoc["_id"].ToString().Replace("\"", "\\\"");
                             basicsCouJson = basicsCouDoc.ToString(Formatting.None)
                             .Replace("\\", "\\\\")
                             .Replace("\"", "\\\"");
                            commandCou = $"db.collection('{collectionName}')" +
                                        $".doc('{escapedDocId}')" +
                                        $".set({{data:{basicsCouJson}}})";

                            responseCou = await _weChatCloudDbService.ExecuteDatabaseCommandAsync(commandCou);
                        }
                        else
                        {
                            throw new Exception("查询到的文档缺少_id字段");
                        }
                    }
                    else
                    {
                        // 不存在则新增
                        commandCou = $"db.collection('{collectionName}').add({{data:{basicsCouJson}}})";
                        responseCou = await _weChatCloudDbService.AddeDatabaseCommandAsync(commandCou);
                    }

                    // 5. 检查执行结果
                    var responseObj = JObject.Parse(responseCou);
                    if (responseObj["errcode"]?.Value<int>() != 0)
                    {
                        throw new Exception($"BasicsCou操作失败: {responseObj["errmsg"]}");
                    }
                }



                if (responseJson["errcode"]?.Value<int>() != 0)
                {
                    throw new Exception($"{dataType} 操作失败: {responseJson["errmsg"]}");
                }

                // 处理结果
                if (isNew)
                {
                    // 新增操作，使用云数据库返回的id_list
                    result.IdList = responseJson["id_list"]?[0]?.Value<string>();
                }
                else
                {
                    // 修改操作，使用前端传来的原始数据中的_id
                    result.IdList = originalData["_id"]?.ToString();
                }

                result.DataId = idValue;

                return result;
            }
            catch (Exception ex)
            {

                throw new Exception("新增、修改销售或卡卷管理信息失败", ex);
            }
        }

        private void EnsureRootField(JObject document, string fieldName, JToken? value)
        {
            if (value != null && !document.ContainsKey(fieldName))
            {
                document[fieldName] = value;
            }
        }
        /// <summary>
        /// 操作图片
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<string> UploadImage(IFormFile file)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("文件无效");

            try
            {
                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";

                // 第一步：获取上传凭证
                var uploadResult = await _weChatCloudDbService.UploadImageToCloudStorage(file, fileName);

                var resultJson = JObject.Parse(uploadResult);
                //拿到file_id值
                string fileId = resultJson["file_id"]?.ToString() ?? string.Empty;

                // 检查错误码（微信API返回的errcode）
                if (resultJson["errcode"]?.Value<int>() != 0)
                {
                    throw new Exception($"获取上传凭证失败: {resultJson["errmsg"]}");
                }

                // 第二步：执行实际上传
                var StatusCode = await _weChatCloudDbService.CompleteCloudStorageUpload(file, uploadResult);
                if (StatusCode == 204)
                {
                    return fileId;
                }

                throw new Exception($"图片上传失败");
            }
            catch (Exception ex)
            {
                throw new Exception($"图片上传失败: {ex.Message}", ex);
            }
        }
        /// <summary>
        /// 删除销售和卡卷管理信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<bool> DeleteSalesAndCardsInfoRecord(DeleteSalesAndCardsInfoContext context)
        {
            string collection = "BasicsInfo";
            try
            {
                string formattedDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                string command;

                // 根据可用的ID字段构建命令
                if (!string.IsNullOrEmpty(context.SalesId))
                {
                    // 使用SalesId作为查询条件
                    string escapedSalesId = context.SalesId.Replace("\"", "\\\"");

                    command = $@"
                db.collection('{collection}')
                .where({{
                    'Type': 'Sales',
                    'SalesId': '{escapedSalesId}'
                }})
                .update({{
                    data: {{
                        'IsDelete': true,
                        'DeleteTime': '{formattedDate}'
                    }}
                }})";
                }
                else if (!string.IsNullOrEmpty(context.CardSheetId))
                {
                    // 使用CardSheetId作为查询条件
                    string escapedCardSheetId = context.CardSheetId.Replace("\"", "\\\"");

                    command = $@"
                db.collection('{collection}')
                .where({{
                    'Type': 'CardSheet',
                    'CardSheetId': '{escapedCardSheetId}'
                }})
                .update({{
                    data: {{
                        'IsDelete': true,
                        'DeleteTime': '{formattedDate}'
                    }}
                }})";
                }
                else
                {
                    // 没有提供有效的ID
                    return false;
                }

                // 执行数据库命令
                string result = await _weChatCloudDbService.ExecuteDatabaseCommandAsync(command);
                var json = JObject.Parse(result);

                return json["errcode"]?.Value<int>() == 0 &&
                       json["errmsg"]?.ToString() == "ok" &&
                       json["matched"]?.Value<int>() >= 1 &&
                       json["modified"]?.Value<int>() >= 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除记录时发生错误: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 获取卡卷列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<GetCardSheetListDto>> GetCardSheetListRecord(GetCardSheetListContext context)
        {

            try
            {
                Expression<Func<NGrouponInfo, bool>> listExpression = w => w.IsDel == false;

                if (context.CardSheetListId != null)
                {
                    listExpression = w =>
                   w.GrouponKey == context.CardSheetListId;
                }
                else if (!string.IsNullOrWhiteSpace(context.CardSheetListName))
                {
                    listExpression = w => w.GrouponName.Contains(context.CardSheetListName);
                }

                var ngrouponInfoLists = await _ngrouponInfoRepository.GetPageListAsync(
                    context.Paging,
                    listExpression);

                if (!ngrouponInfoLists.Any())
                {
                    return new List<GetCardSheetListDto>();
                }

                // 映射为DTO
                return ngrouponInfoLists.Select(a => new GetCardSheetListDto
                {
                    GrouponKey = a.GrouponKey,
                    GrouponName = a.GrouponName,
                    GoodsPrice = a.GoodsPrice,
                    GrouponPrice = a.GrouponPrice,
                    Explain = a.Explain
                }).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception("获取卡卷列表失败，请稍后重试", ex);
            }

        }


    }

}
