﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplateDetails;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicITemplateDetails;

namespace HdProject.Application.Services.SongBase.WallPainting
{
    /// <summary>
    /// 模板明细实现类
    /// </summary>
    public class WPTopicITemplateDetailsService : IWPTopicITemplateDetailsService
    {
        public Task<WPTopicITemplateDetailsAddResponseDto> AddAsync(WPTopicITemplateDetailsAddRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicITemplateDetailsDeleteResponseDto> DeleteAsync(WPTopicITemplateDetailsDeleteRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicITemplateDetailsGetAllResponseDto> GetAllAsync(WPTopicITemplateDetailsGetAllRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicITemplateDetailsGetByIdResponseDto> GetByIdAsync(WPTopicITemplateDetailsGetByIdRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicITemplateDetailsUpdateResponseDto> UpdateAsync(WPTopicITemplateDetailsUpdateRequestDto requestDto)
        {
            throw new NotImplementedException();
        }
    }
}
