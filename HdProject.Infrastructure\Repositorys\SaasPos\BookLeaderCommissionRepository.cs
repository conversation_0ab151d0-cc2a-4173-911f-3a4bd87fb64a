﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Entities;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;

namespace HdProject.Infrastructure.Repositorys.SaasPos
{
    internal class BookLeaderCommissionRepository : RepositorySaas<UserTest>, IRepository<UserTest>
    {
        public BookLeaderCommissionRepository(ISqlSugarClient db) : base(db) { }



    }
}