﻿using System;
using System.Collections.Generic;
using System.DirectoryServices.Protocols;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMPlaylist;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using SqlSugar;

namespace HdProject.Application.Services.SaasPos.MaterialManagement
{
    /// <summary>
    /// 节目单服务接口实现类
    /// </summary>
    public class MMPlaylistService : IMMPlaylistService
    {
        private readonly IRepositorySaas<MMPlaylist> _repositoryMMPlaylist;
        private readonly IMapper _mapper;
        private readonly ISqlSugarClient _sqlSugarClient;
        public MMPlaylistService(IRepositorySaas<MMPlaylist> repositoryMMPlaylist, IMapper mapper, ISqlSugarClient sqlSugarClient)
        {
            _repositoryMMPlaylist = repositoryMMPlaylist;
            _mapper = mapper;
            _sqlSugarClient = sqlSugarClient;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMPlaylistAddResponseDto> AddAsync(MMPlaylistAddRequestDto requestDto)
        {
            MMPlaylistAddResponseDto responseDto = new MMPlaylistAddResponseDto();
            try
            {
                var resultModel = new MMPlaylist()
                {
                    PlaylistName = requestDto.Model.PlaylistName,
                    LayoutID = requestDto.Model.LayoutID,
                    CreatedBy = "张三",
                    CreatedTime = DateTime.Now
                };
                var result = await _repositoryMMPlaylist.InsertAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMPlaylistDeleteResponseDto> DeleteAsync(MMPlaylistDeleteRequestDto requestDto)
        {
            MMPlaylistDeleteResponseDto responseDto = new MMPlaylistDeleteResponseDto();
            try
            {
                var resultModel = await _repositoryMMPlaylist.GetFirstAsync(a => a.PlaylistID == requestDto.PlaylistID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法删除，请检查该节目单是否存在！");
                }
                resultModel.IsActive = true;
                resultModel.DisabledBy = "";
                resultModel.DisabledTime = DateTime.Now;
                var result = await _repositoryMMPlaylist.UpdateAsync(resultModel);
                responseDto.Index=result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMPlaylistGetAllAsyncResponseDto> GetAllAsync(MMPlaylistGetAllAsyncRequestDto requestDto)
        {
            MMPlaylistGetAllAsyncResponseDto responseDto = new MMPlaylistGetAllAsyncResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<MMPlaylist>(true);
                predicate = predicate.And(it => it.IsActive == false);//过滤非禁用的数据
                                                                      // 动态添加条件
                if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
                {
                    predicate = predicate.And(it => it.PlaylistName.Contains(requestDto.QueryCriteria));
                }
                var result = await GetPageAllAssociationBdAsync(requestDto, predicate);
                //var model = _mapper.Map<List<MMDeviceDto>>(result);//使用AutoMapper进行对象属性映射
                //foreach (var item in result)
                //{
                //    var DeviceStatusModel = await _repositoryMMPlaylistStatus.GetByIdAsync(item.OnlineStatusCode);//根据ID查询设备状态
                //    item.OnlineStatus = new MMDeviceStatusDto()
                //    {
                //        StatusID = DeviceStatusModel.StatusID,
                //        StatusName = DeviceStatusModel.StatusName,
                //        Remarks = DeviceStatusModel.Remarks,
                //    };
                //}
                var model = _mapper.Map<List<MMPlaylistDto>>(result);
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 查询全部信息(分页)，需要绑定关联表信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<MMPlaylist>> GetPageAllAssociationBdAsync(Pagination page, Expression<Func<MMPlaylist, bool>> whereExpression = null)
        {
            RefAsync<int> totalCount = 0;
            var query = _db.Queryable<MMPlaylist>().
                Includes(
                c => c.PlaylistDetailList,
                c2 => new { c2.PlaylistDetailXqList, c2.MMLayoutRegionEntity }
                );

            if (whereExpression != null)
            {
                query = query.Where(whereExpression);
            }

            var list = await query.ToPageListAsync(page.Page, page.Rows, totalCount);
            page.Records = totalCount;
            return list;
        }

        /// <summary>
        /// 根据ID查询，需要绑定关联表信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MMPlaylist> GetByIdAssociationBdAsync(int id)
        {
            return await _db.Queryable<MMPlaylist>()
                .Includes(
                c => c.PlaylistDetailList,
                c2 => new { c2.PlaylistDetailXqList, c2.MMLayoutRegionEntity }
                )
                .Where(c => c.PlaylistID == id && c.IsActive == false)
                .FirstAsync();
        }
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMPlaylistGetByIdAsyncResponseDto> GetByIdAsync(MMPlaylistGetByIdAsyncRequestDto requestDto)
        {
            MMPlaylistGetByIdAsyncResponseDto responseDto = new MMPlaylistGetByIdAsyncResponseDto();
            try
            {
                var result = await GetByIdAssociationBdAsync(requestDto.PlaylistID);
                if (result != null)
                {
                    var model = _mapper.Map<MMPlaylistDto>(result);//使用AutoMapper进行对象属性映射
                    responseDto.Model = model;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMPlaylistUpdateResponseDto> UpdateAsync(MMPlaylistUpdateRequestDto requestDto)
        {
            MMPlaylistUpdateResponseDto responseDto = new MMPlaylistUpdateResponseDto();
            try
            {
                var resultModel = await _repositoryMMPlaylist.GetFirstAsync(a => a.PlaylistID == requestDto.Model.PlaylistID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法删除，请检查该节目单是否存在！");
                }
                var model = _mapper.Map<MMPlaylist>(requestDto.Model);
                model.CreatedBy = resultModel.CreatedBy;
                model.CreatedTime = resultModel.CreatedTime;
                model.DisabledBy = resultModel.DisabledBy;
                model.DisabledTime = resultModel.DisabledTime;
                model.ModifiedBy = "张三";
                model.ModifiedTime = DateTime.Now;
                var result = await _repositoryMMPlaylist.UpdateAsync(model);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
    }
}
