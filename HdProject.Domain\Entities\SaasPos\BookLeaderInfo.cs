﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos
{
    public partial class BookLeaderInfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public System.Guid LeaderId { get; set; }
        public string Name { get; set; }
        // 确保 BankCard 有默认值或允许为空
        [Required(ErrorMessage = "银行卡号不能为空")]
        public string BankCard { get; set; }
        public string Phone { get; set; }
        public string LeaderCode { get; set; }
        public bool IsDeleted { get; set; }
        public Nullable<System.DateTime> CreateTime { get; set; }
        public Nullable<System.DateTime> UpdateTime { get; set; }
        public Nullable<System.DateTime> DeleteTime { get; set; }
        public string DeletedBy { get; set; }
        public string UserId { get; set; }
     
    }
}
