﻿using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Common.Utility;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIFile;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SongBase.WallPainting
{
    /// <summary>
    /// 上传用户壁画或壁画模板素材的接口
    /// </summary>
    public class WPTopicIFileController : PublicControllerBase
    {
        private readonly IWPTopicIFileService _wPTopicIFileService;
        private readonly FileManagement _management;
        private readonly ILogger<WPTopicIFileController> _logger;

        public WPTopicIFileController(IWPTopicIFileService wPTopicIFileService, FileManagement management, ILogger<WPTopicIFileController> logger)
        {
            _wPTopicIFileService = wPTopicIFileService;
            _management = management;
            _logger = logger;
        }

        /// <summary>
        /// 上传素材信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Upload")]
        [DisableRequestSizeLimit]
        public async Task<IActionResult> WPTopicIFileAddValue([FromForm] List<IFormFile> files)
        {
            WPTopicIFileAddRequestDto fileDto = new WPTopicIFileAddRequestDto();
            var fileResult = await _management.FileUploadAsync(files, true);//上传文件
            var ModelFile = fileResult.FileEntity.Select(a => new WPTopicIFileCreateDto
            {
                FileName = a.FileName,
                FilePath = a.FilePath,
                FormatType = a.FormatType,
                FileSize = a.FileSize,
                Width = a.Width,
                Height = a.Height,
                ThumbnailPath = a.ThumbnailPath,
            }).ToList();
            fileDto.Model = ModelFile?.FirstOrDefault();
            var result = await _wPTopicIFileService.AddAsync(fileDto);
            string ma = "";
            if (result.FileID > 0)
            {
                return ApiData(result.FileID);
            }
            else
            {
                ma = "素材上传失败！";
                return ApiData(new
                {
                    message = ma,
                    FailedFiles = fileResult.FailedFiles
                });
            }

        }
    }
}
