﻿using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Infrastructure.Repositorys.Imp
{
    public class RepositoryGroupBase<T> : Repository<T>, IRepositoryGroupBase<T>
       where T : class, new()
    {
        public RepositoryGroupBase(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "GroupBase";
            
        }


    }

}
