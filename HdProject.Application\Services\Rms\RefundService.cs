﻿using Furion;
using Furion.DependencyInjection;
using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Domain.Context.RMS;
using HdProject.Domain.Context.WeChat;
using HdProject.Domain.DTOs.RMS;
using HdProject.Domain.Entities;
using HdProject.Domain.Entities.GroupBase;
using HdProject.Domain.Entities.Rms;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using Microsoft.AspNetCore.Mvc.RazorPages;
using SqlSugar;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Drawing.Drawing2D;
using System.Drawing.Printing;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using static HdProject.Domain.Context.RMS.RefundContext;
using static HdProject.Domain.DTOs.RMS.RefundDto;
using NCodeRefund = HdProject.Domain.Entities.GroupBase.NCodeRefund;

namespace HdProject.Application.Services.Rms
{
    /// <summary>
    /// 退款管理
    /// </summary>
    public class RefundService : IRefundService
    {
        private readonly IWeChatCloudDbService _weChatCloudDbService;
        private readonly IRepositoryGroupBase<ApplyListDto> _getRefundRecord;
        private readonly IRepositoryGroupBase<ApplyRefundListDto> _refundListRecord;
        private readonly IRepositoryGroupBase<ApplyRefundDto> _applyRefundRecord;





        public RefundService(
            IWeChatCloudDbService weChatCloudDbService,
            IRepositoryGroupBase<ApplyListDto> getRefundRecord,
             IRepositoryGroupBase<ApplyRefundListDto> refundListRecord,
             IRepositoryGroupBase<ApplyRefundDto> applyRefundRecord



            )
        {
            _weChatCloudDbService = weChatCloudDbService;
            _getRefundRecord = getRefundRecord;
            _refundListRecord = refundListRecord;
            _applyRefundRecord = applyRefundRecord;

        }

        /// <summary>
        ///  卡卷查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<CardDto>> GetRefundRecord(CardContext context)
        {
            try
            {

                // 创建参数对象
                var cards = new
                {
                    t = 0,
                    PhoneNumber = context.PhoneNumber,
                    NCodeNo = context.NCodeNo

                };

                // 调用存储过程并返回结果
                var cardList = await _getRefundRecord.GetProcedureAsync(
                    context.Paging,
                  "Ex_Refund_Test",
                  cards);

                //失效数
                var invalidTotal = cardList.Count(i => i.NCodeNo == "3" && i.isDel == false);
                //可用数
                var effectiveTotal = cardList.Count(i => i.NCodeNo == "0" && i.isDel == false);
                var totalAll = cardList.Count();
                if (context.NCodeNo != null)
                {
                    cardList = cardList.Where(c => c.NCodeNo == context.NCodeNo.ToString() && c.isDel == false).ToList();
                }
                int totalCount = cardList.Count;

                var groupedData = cardList
                .GroupBy(c => c.transaction_id)
                .Select(g => new CardDto
                {
                    TotalAll = totalAll,
                    InvalidTotal = invalidTotal,
                    EffectiveTotal = effectiveTotal,
                    Total = totalCount,
                    transaction_id = g.Key,
                    list = g.ToList()
                })
                .ToList();




                return groupedData;
            }
            catch (Exception ex) { throw new Exception("卡卷查询失败，请稍后重试", ex); }

        }
        /// <summary>
        /// 申请退款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<ApplyRefundDto>> ApplyRefundRecord(ApplyRefundContext context)
        {

            try
            {
                // 创建参数对象
                var applyRefunds = new
                {
                    t = 1,
                    InCodeKey = context.InCodeKey,
                    Torecharge = context.Torecharge,
                    ApplyMoney = context.ApplyMoney,
                    ApplyShopId = context.ApplyShopId,
                    ApplyUserID = context.ApplyUserID,
                    ApplyName = context.ApplyName,
                    ApplyReason = context.ApplyReason,
                    ApplyNum = context.ApplyNum,
                    NCodeNo = context.NCodeNo//卷的状态
                };

                // 调用存储过程并返回结果
                return await _applyRefundRecord.GetAllByProcedureAsync(
                 "Ex_Refund_Test",
                 applyRefunds);


            }

            catch (Exception ex)
            {
                throw new Exception("申请退款失败，请稍后重试", ex);
            }
        }
        /// <summary>
        /// 申请退款列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<ApplyRefundListDto>> ApplyRefundListRecord(ApplyRefundListContext context)
        {
            try
            {

                var headerParams = new
                {
                    t = 0,
                    ApplyNo = context.ApplyNo,
                    ConfirmNo = context.ConfirmNo,
                    ApplyUserID = context.ApplyUserID,
                    IsEnd = context.IsEnd,
                    BeginDate = context.BeginDate,
                    EndDate = context.EndDate,
                    ShopID = context.ShopID,
                    //PageIndex = context.Paging.Page,
                    //PageSize = context.Paging.Rows,
                };

                var headerResults = await _refundListRecord.GetProcedureAsync(context.Paging, "Ex_ConfirmRefund_Test", headerParams);


                var resultList = new List<ApplyRefundListDto>();

                foreach (var header in headerResults)
                {
                    var detailParams = new
                    {
                        t = 4,
                        ApplyNo = header.ApplyNo,
                    };

                    var detailResults = await _getRefundRecord.GetAllByProcedureAsync("Ex_Refund", detailParams);

                    // 组合数据
                    var resultItem = new ApplyRefundListDto
                    {
                        Total = header.Total,
                        ApplyNo = header.ApplyNo,
                        ShopName = header.ShopName,
                        ApplyName = header.ApplyName,
                        ApplyMoney = header.ApplyMoney,
                        ApplyNum = header.ApplyNum,
                        ApplyReason = header.ApplyReason,
                        ApplyTime = header.ApplyTime,
                        ConfirmName = header.ConfirmName,
                        ConfirmNo = header.ConfirmNo,
                        CheckName = header.CheckName,
                        Openid = header.Openid,
                        ApplyUserID = header.ApplyUserID,

                        list = detailResults.Select(d => new ApplyListDto
                        {
                            GrouponName = d.GrouponName,
                            GrouponPrice = d.GrouponPrice,
                            NCode = d.NCode,
                            transaction_id = d.transaction_id,
                            NValid = d.NValid,
                            NValidEnd = d.NValidEnd,
                            NCodeNo = d.NCodeNo,
                            ApplyNo = d.ApplyNo,
                            DelDateTime = d.DelDateTime,
                        }).ToList()
                    };

                    resultList.Add(resultItem);
                }

                return resultList;
            }
            catch (Exception ex)
            {
                throw new Exception("获取申请退款列表详情失败");
            }


        }

        /// <summary>
        /// 退款
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<bool> RefundRecord(ConfirmRefundContext context)
        {
            try
            {


                // 创建参数对象
                var confirmRefunds = new
                {
                    t = 1,
                    ApplyNo = context.ApplyNo,
                    ConfirmNo = context.ConfirmNo,
                    ConfirmName = context.ConfirmName,
                    CheckName = context.CheckName,

                };

                // 调用存储过程并返回结果
                var confirmRefundList = await _refundListRecord.GetAllByProcedureAsync(
                 "Ex_ConfirmRefund",
                 confirmRefunds);

                //OpenId: app.getOpenId(), //用户ID
                //OrderId: id, //订单ID
                //RefundCash: pay, //退款金额
                //RefundType: 1, //退款类型-暂时固定的
                //RefundRemark: "", //退款说明-暂时为空

                //var cloudResult = await _weChatCloudDbService.invokeCloudFunction(
                //    "SaasPos", new { Action = "CreateOrderRefundData", OpenId = "", OrderId = "", RefundCash = "", RefundType = "", RefundRemark = "" });

                //var refundNo = GenerateRefundNo();//生成商户退款单号

                //var param = new RefundParams
                //{
                //    openid = context.Openid,
                //    transaction_id = context.Transaction_id,//微信支付订单号
                //    refund_no = refundNo,//商户退款单号
                //    total_amount = context.TotalAmount,//订单总金额
                //    refund_amount = context.RefundAmount//退款金额
                //};
                //var result = await _weChatCloudDbService.ApplyRefund(param);

                return true;
            }
            catch (Exception ex) { return false; }

        }

        public static string GenerateRefundNo()
        {
            string timePart = DateTime.Now.ToString("yyyyMMddHHmmss");
            string guidPart = Math.Abs(Guid.NewGuid().GetHashCode()).ToString("D10");//十位数字
            return $"REF{timePart}{guidPart}";
        }


    }
}


