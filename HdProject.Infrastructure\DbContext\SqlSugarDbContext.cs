﻿using Microsoft.Extensions.Configuration;
using SqlSugar;

namespace HdProject.Infrastructure.DbContext
{
    public static class SqlSugarDbContext
    {
        public static SqlSugarScope InitDb(IConfiguration configuration)
        {

            return new SqlSugarScope(new List<ConnectionConfig>() {
                new ConnectionConfig(){
                    ConfigId= "Default",
                    ConnectionString = configuration.GetConnectionString("Default"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true,
                    InitKeyType = InitKeyType.Attribute//从实体特性中读取主键自增列信息,不加这段，根据主键查询时可能会报错
                },
                 new ConnectionConfig(){
                    ConfigId= "Saas",
                    ConnectionString = configuration.GetConnectionString("Saas"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                 new ConnectionConfig(){
                    ConfigId= "MainFood",
                    ConnectionString = configuration.GetConnectionString("MainFood"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                 new ConnectionConfig(){
                    ConfigId= "Rms",
                    ConnectionString = configuration.GetConnectionString("Rms"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                 new ConnectionConfig(){
                    ConfigId= "SongBase",
                    ConnectionString = configuration.GetConnectionString("SongBase"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                 new ConnectionConfig(){
                    ConfigId= "OperateData",
                    ConnectionString = configuration.GetConnectionString("OperateData"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                new ConnectionConfig(){
                    ConfigId= "dbfood",
                    ConnectionString = configuration.GetConnectionString("dbfood"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                new ConnectionConfig(){
                    ConfigId= "Rms2009",
                    ConnectionString = configuration.GetConnectionString("Rms2009"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },

              new ConnectionConfig(){
                    ConfigId= "Rms2019",
                    ConnectionString = configuration.GetConnectionString("Rms2019"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                 new ConnectionConfig(){
                    ConfigId= "GroupBase",
                    ConnectionString = configuration.GetConnectionString("GroupBase"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                  new ConnectionConfig(){
                    ConfigId= "GroupBase2",
                    ConnectionString = configuration.GetConnectionString("GroupBase2"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                },
                   new ConnectionConfig(){
                    ConfigId= "MIMS",
                    ConnectionString = configuration.GetConnectionString("MIMS"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                }, new ConnectionConfig(){
                    ConfigId= "WxInfo",
                    ConnectionString = configuration.GetConnectionString("WxInfo"),
                    DbType = DbType.SqlServer,
                    IsAutoCloseConnection = true
                }
            },

            db =>
            {
                // 全局配置（如 AOP 日志）
                db.Aop.OnLogExecuting = (sql, pars) => Console.WriteLine(sql);
            });
        }
    }
}