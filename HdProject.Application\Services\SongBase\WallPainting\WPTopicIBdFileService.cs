﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIBdFile;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicIBdFile;

namespace HdProject.Application.Services.SongBase.WallPainting
{
    /// <summary>
    /// 主题绑定关系
    /// </summary>
    public class WPTopicIBdFileService : IWPTopicIBdFileService
    {
        public Task<WPTopicIBdFileAddResponseDto> AddAsync(WPTopicIBdFileAddRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicIBdFileDeleteResponseDto> DeleteAsync(WPTopicIBdFileDeleteRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicIBdFileGetAllResponseDto> GetAllAsync(WPTopicIBdFileGetAllRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicIBdFileGetByIdResponseDto> GetByIdAsync(WPTopicIBdFileGetByIdRequestDto requestDto)
        {
            throw new NotImplementedException();
        }

        public Task<WPTopicIBdFileUpdateResponseDto> UpdateAsync(WPTopicIBdFileUpdateRequestDto requestDto)
        {
            throw new NotImplementedException();
        }
    }
}
