﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.DirectoryServices.Protocols;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Azure.Core;
using Furion.DatabaseAccessor;
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using OracleInternal.Secure.Network;
using SqlSugar;

namespace HdProject.Application.Services.SaasPos.MaterialManagement
{
    /// <summary>
    /// 节目投放接口实现类
    /// </summary>
    public class MMAdCampaignService : IMMAdCampaignService
    {
        private readonly IRepositorySaas<MMAdCampaign> _repositoryMMAdCampaign;//节目投放表
        private readonly IRepositorySaas<MMPlaybackDevice> _repositoryMMPlaybackDevice;//投放设备表
        private readonly IMapper _mapper;
        private readonly ISqlSugarClient _sqlSugarClient;
        public MMAdCampaignService(IRepositorySaas<MMAdCampaign> repositorySaasMMAdCampaign, IRepositorySaas<MMPlaybackDevice> repositoryMMPlaybackDevice, IMapper mapper, ISqlSugarClient sqlSugarClient)
        {
            _repositoryMMAdCampaign = repositorySaasMMAdCampaign;
            _repositoryMMPlaybackDevice = repositoryMMPlaybackDevice;
            _mapper = mapper;
            _sqlSugarClient = sqlSugarClient;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMAdCampaignAddResponseDto> AddAsync(MMAdCampaignAddRequestDto requestDto)
        {
            MMAdCampaignAddResponseDto responseDto = new MMAdCampaignAddResponseDto();
            try
            {
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    var TemplateModel = new MMLayoutTemplate()
                    {
                        LayoutName = requestDto.Model.MMLayoutTemplateEntity.LayoutName,
                        LayoutDescription = requestDto.Model.MMLayoutTemplateEntity.LayoutDescription,
                        LayoutRows = requestDto.Model.MMLayoutTemplateEntity.LayoutRows,
                        LayoutCols = requestDto.Model.MMLayoutTemplateEntity.LayoutCols,
                        TemplateGridCount = requestDto.Model.MMLayoutTemplateEntity.TemplateGridCount,
                        CreatedBy = "张三",
                        CreatedTime = DateTime.Now
                    };
                    var templateIdLayoutID = await _db.Insertable(TemplateModel).ExecuteReturnIdentityAsync();//首先新增布局模板，并返回其自增列ID

                    var MMPlModel = new MMPlaylist()
                    {
                        PlaylistName = requestDto.Model.CampaignName,
                        LayoutID = templateIdLayoutID,
                        CreatedBy = "张三",
                        CreatedTime = DateTime.Now
                    };
                    var MMPlaylistId = await _db.Insertable(MMPlModel).ExecuteReturnIdentityAsync();//第二步：新增大节目，并返回其自增列ID

                    var mac = new MMAdCampaign()
                    {
                        CampaignName = requestDto.Model.CampaignName,
                        PlaylistID = MMPlaylistId,
                        //DeviceID = requestDto.Model.DeviceID,
                        LayoutID = templateIdLayoutID,
                        StartTime = requestDto.Model.StartTime,
                        EndTime = requestDto.Model.EndTime,
                        IsItDaily = requestDto.Model.IsItDaily,
                        CreatedBy = "张三",
                        CreatedTime = DateTime.Now
                    };
                    var MMAdCampaignId = await _db.Insertable(mac).ExecuteReturnIdentityAsync();//第三步：新增节目投放任务信息，并返回其自增列ID

                    var Campaign = await _db.Queryable<MMAdCampaign>().Where(c => c.CampaignID == MMAdCampaignId && c.IsActive == false).FirstAsync();
                    Campaign.CampaignVersion = MMAdCampaignId + "0001";
                    await _db.Updateable(Campaign).UpdateColumns(it => new { it.CampaignVersion }).ExecuteCommandAsync();//修改节目版本


                    var mpbd = requestDto.Model.MMDeviceList.Select(d => new MMPlaybackDevice
                    {
                        CampaignID = MMAdCampaignId,
                        DeviceID = d.DeviceID
                    }).ToList();
                    await _db.Insertable(mpbd).ExecuteCommandAsync();//第四步：新增节目投放设备信息

                    //List<MMPlaylistDetail> PlaylistDetail = new List<MMPlaylistDetail>();
                    foreach (var rlist in requestDto.Model.MMLayoutTemplateEntity.RegionList)
                    {
                        if (rlist.PlaylistDetails.PlaylistDetailXqList.Count > 0)
                        {
                            var mlr = new MMLayoutRegion()
                            {
                                LayoutID = templateIdLayoutID,//将新增成功的布局模板ID插入布局区域表中
                                RegionName = rlist.RegionName,
                                StartX = rlist.StartX,
                                StartY = rlist.StartY,
                                RegionWidth = rlist.RegionWidth,
                                RegionHeight = rlist.RegionHeight,
                                CreatedBy = rlist.CreatedBy,
                                HtmlTemplate = rlist.HtmlTemplate,
                            };
                            var LayoutRegionId = await _db.Insertable(mlr).ExecuteReturnIdentityAsync();//第五步：新增区域，并返回其自增列ID

                            var mpld = new MMPlaylistDetail()
                            {
                                PlaylistID = MMPlaylistId,
                                ProgramName = rlist.PlaylistDetails.ProgramName,
                                RegionID = LayoutRegionId,
                                CreatedBy = "张三",
                                CreatedTime = DateTime.Now
                            };
                            var PlaylistDetaiId = await _db.Insertable(mpld).ExecuteReturnIdentityAsync();//第六步：在指定区域新增节目单，并返回其自增列ID

                            //循环节目单下面的节目明细详情
                            foreach (var mMPlaylistDetailXq in rlist.PlaylistDetails.PlaylistDetailXqList)
                            {
                                var mpldx = new MMPlaylistDetailXq()
                                {
                                    DetailID = PlaylistDetaiId,
                                    FileID = mMPlaylistDetailXq.MMFile.FileID,
                                    Sequence = mMPlaylistDetailXq.Sequence,
                                    AdjustedDuration = mMPlaylistDetailXq.AdjustedDuration,
                                };
                                await _db.Insertable(mpldx).ExecuteCommandAsync();//最后：将素材绑定到对应节目明细，并返回成功行数
                            }
                        }
                    }
                });

                if (result.IsSuccess)
                {
                    responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMAdCampaignDeleteResponseDto> DeleteAsync(MMAdCampaignDeleteRequestDto requestDto)
        {
            MMAdCampaignDeleteResponseDto responseDto = new MMAdCampaignDeleteResponseDto();
            try
            {
                var resultModel = await _repositoryMMAdCampaign.GetFirstAsync(a => a.CampaignID == requestDto.CampaignID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法删除，请检查该投放任务是否存在！");
                }
                resultModel.IsActive = true;
                resultModel.DisabledBy = "张三";
                resultModel.DisabledTime = DateTime.Now;
                var result = await _repositoryMMAdCampaign.UpdateAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 根据ID查询，需要关联其他实体信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MMAdCampaignDto> GetAssociationBdDetailsAsync(int id)
        {
            var Campaign = await _db.Queryable<MMAdCampaign>()
                .Where(c => c.CampaignID == id && c.IsActive == false)
                .FirstAsync();
            if (Campaign == null)
            {
                return null;
            }

            // 查询节目单关联的布局模板
            var layoutTemplate = await _db.Queryable<MMPlaylist>()
                .Where(p => p.PlaylistID == Campaign.PlaylistID && p.IsActive == false)
                .Select(p => p.LayoutID)
                .FirstAsync();

            // 查询布局模板详情
            var layoutInfo = await _db.Queryable<MMLayoutTemplate>()
                .Where(lt => lt.LayoutID == layoutTemplate && lt.IsActive == false)
                .Select(lt => new MMLayoutTemplateDto
                {
                    LayoutID = lt.LayoutID,
                    LayoutName = lt.LayoutName,
                    LayoutDescription = lt.LayoutDescription,
                    LayoutRows = lt.LayoutRows,
                    LayoutCols = lt.LayoutCols,
                    TemplateGridCount = lt.TemplateGridCount
                })
                .FirstAsync();

            // 查询布局区域
            var regions = await _db.Queryable<MMLayoutRegion>()
                .Where(lr => lr.LayoutID == layoutTemplate)
                .Select(lr => new MMLayoutRegionDto
                {
                    RegionID = lr.RegionID,
                    RegionName = lr.RegionName,
                    LayoutID = lr.LayoutID,
                    StartX = lr.StartX,
                    StartY = lr.StartY,
                    RegionWidth = lr.RegionWidth,
                    RegionHeight = lr.RegionHeight,
                    HtmlTemplate = lr.HtmlTemplate
                })
                .ToListAsync();
            // 查询每个区域的节目详情
            foreach (var region in regions)
            {
                // 查询节目编排明细
                var playlistDetails = await _db.Queryable<MMPlaylistDetail>()
                    .Where(pd => pd.PlaylistID == Campaign.PlaylistID && pd.RegionID == region.RegionID && pd.IsActive == false)
                    .Select(pd => new MMPlaylistDetailDto
                    {
                        DetailID = pd.DetailID,
                        ProgramName = pd.ProgramName,
                        PlaylistID = pd.PlaylistID,
                        RegionID = pd.RegionID
                    })
                    .FirstAsync();

                // 查询每个节目明细的详情项
                //foreach (var detail in playlistDetails)
                //{
                // 先查询所有详情项
                var detailItems = await _db.Queryable<MMPlaylistDetailXq>()
                    .Where(px => px.DetailID == playlistDetails.DetailID)
                    .OrderBy(px => px.Sequence)
                    .Select(px => new
                    {
                        px.DetailXqID,
                        px.Sequence,
                        px.AdjustedDuration,
                        px.DetailID,
                        px.FileID
                    })
                    .ToListAsync();

                // 获取所有文件ID
                var fileIds = detailItems.Select(x => x.FileID).Distinct().ToList();

                // 批量查询所有文件
                var files = await _db.Queryable<MMFile>()
                    .Where(f => fileIds.Contains(f.FileID) && f.IsActive == false)
                    .Select(f => new MMFileDto
                    {
                        FileID = f.FileID,
                        FileName = f.FileName,
                        FilePath = f.FilePath,
                        FormatType = f.FormatType,
                        FileSize = f.FileSize,
                        //ThumbnailPath = f.ThumbnailPath,
                        Width = f.Width,
                        Height = f.Height,
                        VideoDuration = f.VideoDuration
                    })
                    .ToListAsync();

                // 组合结果
                playlistDetails.PlaylistDetailXqList = detailItems.Select(px => new MMPlaylistDetailXqDto
                {
                    DetailXqID = px.DetailXqID,
                    DetailID = px.DetailID,
                    FileID = px.FileID,
                    Sequence = px.Sequence,
                    AdjustedDuration = px.AdjustedDuration,
                    MMFile = files.FirstOrDefault(f => f.FileID == px.FileID)
                }).ToList();
                //}

                region.PlaylistDetails = playlistDetails;
            }

            // 查询投放任务下的设备
            var PlaybackDeviceList = await _db.Queryable<MMPlaybackDevice>()
                .Where(pd => pd.CampaignID == Campaign.CampaignID)
                .Select(pd => new MMPlaybackDeviceDto
                {
                    PlaybackDeviceID = pd.PlaybackDeviceID,
                    CampaignID = pd.CampaignID,
                    DeviceID = pd.DeviceID,
                })
                .ToListAsync();
            if (PlaybackDeviceList.Count == 0)
            {
                return new MMAdCampaignDto
                {
                    CampaignID = Campaign.CampaignID,
                    CampaignName = Campaign.CampaignName,
                    PlaylistID = Campaign.PlaylistID,
                    LayoutID = Campaign.LayoutID,
                    StartTime = Campaign.StartTime,
                    EndTime = Campaign.EndTime
                };
            }
            List<MMDeviceDto> DevicesList = new List<MMDeviceDto>();
            foreach (var backDeviceList in PlaybackDeviceList)
            {
                var device = await _db.Queryable<MMDevice>()
                .Where(c => c.DeviceID == backDeviceList.DeviceID && c.IsActive == false)
                .Select(lt => new MMDeviceDto
                {
                    DeviceID = lt.DeviceID,
                    DeviceName = lt.DeviceName
                })
                .FirstAsync();
                DevicesList.Add(device);
            }

            return new MMAdCampaignDto
            {
                CampaignID = Campaign.CampaignID,
                CampaignName = Campaign.CampaignName,
                PlaylistID = Campaign.PlaylistID,
                LayoutID = Campaign.LayoutID,
                StartTime = Campaign.StartTime,
                EndTime = Campaign.EndTime,
                IsItDaily = Campaign.IsItDaily,
                MMDeviceList = DevicesList,//设备
                MMLayoutTemplateEntity = layoutInfo,//布局模板
                LayoutRegions = regions,//布局区域
            };
        }
        /// <summary>
        /// 根据投放任务查询投放任务详情信息的接口
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMAdCampaignGetCampaignDetailsAsyncResponseDto> GetCampaignDetails(MMAdCampaignGetCampaignDetailsAsyncRequestDto requestDto)
        {
            MMAdCampaignGetCampaignDetailsAsyncResponseDto responseDto = new MMAdCampaignGetCampaignDetailsAsyncResponseDto();
            try
            {
                var result = await GetAssociationBdDetailsAsync(requestDto.CampaignID);
                if (result != null)
                {
                    //var model = _mapper.Map<MMAdCampaignDto>(result);//使用AutoMapper进行对象属性映射
                    responseDto.Model = result;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMAdCampaignGetAllAsyncResponseDto> GetAllAsync(MMAdCampaignGetAllAsyncRequestDto requestDto)
        {
            MMAdCampaignGetAllAsyncResponseDto responseDto = new MMAdCampaignGetAllAsyncResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<MMAdCampaign>(true);
                predicate = predicate.And(it => it.IsActive == false);//过滤非禁用的数据
                // 动态添加条件
                if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
                {
                    predicate = predicate.And(it => it.CampaignName.Contains(requestDto.QueryCriteria));
                }
                var result = await _repositoryMMAdCampaign.GetPageListAsync(requestDto, predicate);
                //var result = await GetPageAllAssociationBdAsync(requestDto, predicate);
                var model = _mapper.Map<List<MMAdCampaignDto>>(result.OrderByDescending(it => it.CampaignID));//使用AutoMapper进行对象属性映射
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 根据ID查询，需要绑定关联表信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MMAdCampaign> GetByIdAssociationBdAsync(int id)
        {
            if (id == 0)
            {
                throw new Exception("无法查询，编码需大于0！");
            }
            var mdcModel = await _db.Queryable<MMAdCampaign>()
                .Where(c => c.CampaignID == id && c.IsActive == false)
                .FirstAsync();
            var mpbdModel = await _db.Queryable<MMPlaybackDevice>()
                .Where(c => c.CampaignID == id)
                .ToListAsync();
            var mdlist = new List<MMDevice>();
            foreach (var mpbd in mpbdModel)
            {
                var device = await _db.Queryable<MMDevice>()
                .Where(c => c.DeviceID == mpbd.DeviceID && c.IsActive == false)
                .FirstAsync();
                mdlist.Add(new MMDevice()
                {
                    DeviceID = device.DeviceID,
                    DeviceName = device.DeviceName,
                });
            }
            return new MMAdCampaign
            {
                CampaignID = mdcModel.CampaignID,
                CampaignName = mdcModel.CampaignName,
                MMDeviceList = mdlist,
                StartTime = mdcModel.StartTime,
                EndTime = mdcModel.EndTime,
            };
        }

        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMAdCampaignGetByIdAsyncResponseDto> GetByIdAsync(MMAdCampaignGetByIdAsyncRequestDto requestDto)
        {
            MMAdCampaignGetByIdAsyncResponseDto responseDto = new MMAdCampaignGetByIdAsyncResponseDto();
            try
            {
                var result = await GetByIdAssociationBdAsync(requestDto.CampaignID);
                if (result != null)
                {
                    var model = _mapper.Map<MMAdCampaignDto>(result);//使用AutoMapper进行对象属性映射
                    responseDto.Model = model;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMAdCampaignUpdateResponseDto> UpdateAsync(MMAdCampaignUpdateRequestDto requestDto)
        {
            MMAdCampaignUpdateResponseDto responseDto = new MMAdCampaignUpdateResponseDto();
            try
            {
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    var mlt = await _db.Queryable<MMLayoutTemplate>().Where(a => a.LayoutID == requestDto.Model.MMLayoutTemplateEntity.LayoutID && a.IsActive == false).FirstAsync();
                    if (mlt == null)
                    {
                        throw new Exception("无法修改，请检查该投放任务是否存在！");
                    }
                    var TemplateModel = new MMLayoutTemplate()
                    {
                        LayoutID = requestDto.Model.MMLayoutTemplateEntity.LayoutID,
                        LayoutName = requestDto.Model.MMLayoutTemplateEntity.LayoutName,
                        LayoutDescription = requestDto.Model.MMLayoutTemplateEntity.LayoutDescription,
                        LayoutRows = requestDto.Model.MMLayoutTemplateEntity.LayoutRows,
                        LayoutCols = requestDto.Model.MMLayoutTemplateEntity.LayoutCols,
                        TemplateGridCount = requestDto.Model.MMLayoutTemplateEntity.TemplateGridCount,
                        CreatedBy = mlt.CreatedBy,
                        CreatedTime = mlt.CreatedTime,
                        DisabledBy = mlt.DisabledBy,
                        DisabledTime = mlt.DisabledTime,
                        ModifiedBy = "张三",
                        ModifiedTime = DateTime.Now,
                    };
                    await _db.Updateable(TemplateModel).ExecuteCommandAsync();//首先修改布局模板

                    var mpl = await _db.Queryable<MMPlaylist>().Where(a => a.PlaylistID == requestDto.Model.PlaylistID && a.IsActive == false).FirstAsync();//根据投放任务的节目单ID查询节目大单信息
                    if (mpl == null)
                    {
                        throw new Exception("无法修改，请检查该节目单是否存在！");
                    }
                    var MMPlModel = new MMPlaylist()
                    {
                        PlaylistID = requestDto.Model.PlaylistID,
                        PlaylistName = requestDto.Model.CampaignName,
                        LayoutID = requestDto.Model.LayoutID,
                        CreatedBy = mpl.CreatedBy,
                        CreatedTime = mpl.CreatedTime,
                        DisabledBy = mpl.DisabledBy,
                        DisabledTime = mpl.DisabledTime,
                        ModifiedBy = "张三",
                        ModifiedTime = DateTime.Now,
                    };
                    await _db.Updateable(MMPlModel).ExecuteCommandAsync();//第二步：修改大节目

                    var macp = await _db.Queryable<MMAdCampaign>().Where(a => a.CampaignID == requestDto.Model.CampaignID && a.IsActive == false).FirstAsync();//根据ID查询投放任务信息
                    if (macp == null)
                    {
                        throw new Exception("无法修改，请检查该投放任务单是否存在！");
                    }

                    var mac = new MMAdCampaign()
                    {
                        CampaignID = requestDto.Model.CampaignID,
                        CampaignName = requestDto.Model.CampaignName,
                        PlaylistID = requestDto.Model.PlaylistID,
                        //DeviceID = requestDto.Model.DeviceID,
                        LayoutID = requestDto.Model.LayoutID,
                        StartTime = requestDto.Model.StartTime,
                        EndTime = requestDto.Model.EndTime,
                        CreatedBy = macp.CreatedBy,
                        CreatedTime = macp.CreatedTime,
                        DisabledBy = macp.DisabledBy,
                        DisabledTime = macp.DisabledTime,
                        ModifiedBy = "张三",
                        ModifiedTime = DateTime.Now,
                        IsItDaily = requestDto.Model.IsItDaily,
                        CampaignVersion = Convert.ToString(Convert.ToInt32(macp.CampaignVersion) + 1)//版本号变更
                    };
                    await _db.Updateable(mac).ExecuteCommandAsync();//第三步：修改节目投放任务信息


                    var pbd = await _db.Queryable<MMPlaybackDevice>().Where(a => a.CampaignID == requestDto.Model.CampaignID).ToListAsync();
                    foreach (var p in pbd)
                    {
                        var mcount = requestDto.Model.MMDeviceList.Where(a => a.DeviceID == p.DeviceID).ToList();
                        if (mcount.Count == 0) //如果数据库中的设备不存在于参数对象中，则为删除
                        {
                            await _db.Deleteable(new MMPlaybackDevice() { PlaybackDeviceID = p.PlaybackDeviceID }).ExecuteCommandAsync();
                        }
                    }
                    var mpbdlist = new List<MMPlaybackDevice>();
                    var mdvlist = new List<MMDevice>();
                    foreach (var m in requestDto.Model.MMDeviceList)
                    {
                        var mpbdcount = await _db.Queryable<MMPlaybackDevice>().Where(a => a.CampaignID == requestDto.Model.CampaignID && a.DeviceID == m.DeviceID).ToListAsync();
                        //var mdv = await _db.Queryable<MMDevice>().Where(a => a.DeviceID == m.DeviceID).FirstAsync();//获取需要修改设备信息
                        // var verValue = Convert.ToDouble(mdv.DeviceVersion) + 0.1;//版本号变更
                        // mdv.DeviceVersion = Convert.ToString(verValue);
                        // mdvlist.Add(mdv);
                        if (mpbdcount.Count == 0) //如果参数对象中的设备不存在于数据库中，则为新增
                        {
                            mpbdlist.Add(new MMPlaybackDevice
                            {
                                CampaignID = requestDto.Model.CampaignID,
                                DeviceID = m.DeviceID,
                            });
                        }
                    }
                    // await _db.Updateable(mdvlist).UpdateColumns(it => new { it.DeviceVersion }).ExecuteCommandAsync();//修改设备版本
                    await _db.Insertable(mpbdlist).ExecuteCommandAsync();//第四步：修改节目投放设备信息

                    //根据布局模板ID获取到区域信息以及区域关联节目信息，最后删除
                    var delMlrModel = await _db.Queryable<MMLayoutRegion>().Where(a => a.LayoutID == requestDto.Model.MMLayoutTemplateEntity.LayoutID).Select(it => it.RegionID).ToListAsync();
                    foreach (var dmlrm in delMlrModel)
                    {
                        //根据区域ID查询区域所绑定节目明细
                        var delMpldModel = await _db.Queryable<MMPlaylistDetail>().Where(a => a.RegionID == dmlrm).Select(it => it.DetailID).ToListAsync();
                        foreach (var dmpldm in delMpldModel)
                        {
                            //根据节目明细ID查询所绑定节目明细详情
                            var delMpldxModel = await _db.Queryable<MMPlaylistDetailXq>().Where(a => a.DetailID == dmpldm).Select(b => b.DetailXqID).ToListAsync();
                            await _db.Deleteable<MMPlaylistDetailXq>(delMpldxModel).ExecuteCommandAsync();
                        }
                        await _db.Deleteable<MMPlaylistDetail>(delMpldModel).ExecuteCommandAsync();
                    }
                    await _db.Deleteable<MMLayoutRegion>(delMlrModel).ExecuteCommandAsync();


                    //重新新增布局模板下面的区域与节目信息
                    foreach (var rlist in requestDto.Model.MMLayoutTemplateEntity.RegionList)
                    {
                        if (rlist.PlaylistDetails.PlaylistDetailXqList.Count > 0)
                        {
                            var mlr = new MMLayoutRegion()
                            {
                                LayoutID = requestDto.Model.MMLayoutTemplateEntity.LayoutID,//将需要修改的布局模板ID插入新增的布局区域表中
                                RegionName = rlist.RegionName,
                                StartX = rlist.StartX,
                                StartY = rlist.StartY,
                                RegionWidth = rlist.RegionWidth,
                                RegionHeight = rlist.RegionHeight,
                                CreatedBy = rlist.CreatedBy,
                                HtmlTemplate = rlist.HtmlTemplate,
                            };
                            var LayoutRegionId = await _db.Insertable(mlr).ExecuteReturnIdentityAsync();//第五步：新增区域，并返回其自增列ID

                            var mpld = new MMPlaylistDetail()
                            {
                                PlaylistID = requestDto.Model.PlaylistID,
                                ProgramName = rlist.PlaylistDetails.ProgramName,
                                RegionID = LayoutRegionId,
                                CreatedBy = "张三",
                                CreatedTime = DateTime.Now
                            };
                            var PlaylistDetaiId = await _db.Insertable(mpld).ExecuteReturnIdentityAsync();//第六步：在指定区域新增节目单，并返回其自增列ID

                            //循环节目单下面的节目明细详情
                            foreach (var mMPlaylistDetailXq in rlist.PlaylistDetails.PlaylistDetailXqList)
                            {
                                var mpldx = new MMPlaylistDetailXq()
                                {
                                    DetailID = PlaylistDetaiId,
                                    FileID = mMPlaylistDetailXq.MMFile.FileID,
                                    Sequence = mMPlaylistDetailXq.Sequence,
                                    AdjustedDuration = mMPlaylistDetailXq.AdjustedDuration,
                                };
                                await _db.Insertable(mpldx).ExecuteCommandAsync();//最后：将素材绑定到对应节目明细，并返回成功行数
                            }
                        }
                    }

                });

                if (result.IsSuccess)
                {
                    responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 修改投放任务的绑定设备
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMAdCampaignUpdateLaunchDeviceResponseDto> UpdateLaunchDeviceAsyns(MMAdCampaignUpdateLaunchDeviceRequestDto requestDto)
        {
            MMAdCampaignUpdateLaunchDeviceResponseDto responseDto = new MMAdCampaignUpdateLaunchDeviceResponseDto();
            try
            {
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    var mlt = await _db.Queryable<MMAdCampaign>().Where(a => a.CampaignID == requestDto.CampaignID && a.IsActive == false).FirstAsync();
                    if (mlt == null)
                    {
                        throw new Exception("无法修改，请检查该投放任务是否存在！");
                    }
                    //await _db.Updateable(new MMAdCampaign() { PlaybackDeviceID = p.PlaybackDeviceID }).ExecuteCommandAsync();

                    //var mMAdCampaign = new MMAdCampaign()
                    //{
                    //    LayoutID = requestDto.Model.MMLayoutTemplateEntity.LayoutID,
                    //    LayoutName = requestDto.Model.MMLayoutTemplateEntity.LayoutName,
                    //    LayoutDescription = requestDto.Model.MMLayoutTemplateEntity.LayoutDescription,
                    //    LayoutRows = requestDto.Model.MMLayoutTemplateEntity.LayoutRows,
                    //    LayoutCols = requestDto.Model.MMLayoutTemplateEntity.LayoutCols,
                    //    TemplateGridCount = requestDto.Model.MMLayoutTemplateEntity.TemplateGridCount,
                    //    CreatedBy = mlt.CreatedBy,
                    //    CreatedTime = mlt.CreatedTime,
                    //    DisabledBy = mlt.DisabledBy,
                    //    DisabledTime = mlt.DisabledTime,
                    //    ModifiedBy = "张三",
                    //    ModifiedTime = DateTime.Now,
                    //};
                    var ver = Convert.ToInt32(mlt.CampaignVersion) + 1;//修改绑定节目同时需要修改节目版本
                    mlt.CampaignVersion = Convert.ToString(ver);
                    await _db.Updateable(mlt).UpdateColumns(it => it.CampaignVersion).ExecuteCommandAsync();//首先修改布局模板

                    var pbd = await _db.Queryable<MMPlaybackDevice>().Where(a => a.CampaignID == requestDto.CampaignID).ToListAsync();
                    foreach (var p in pbd)
                    {
                        var mcount = requestDto.mMPlaybackDevice.Where(a => a.DeviceID == p.DeviceID).ToList();
                        if (mcount.Count == 0) //如果数据库中的设备不存在于参数对象中，则为删除
                        {
                            await _db.Deleteable(new MMPlaybackDevice() { PlaybackDeviceID = p.PlaybackDeviceID }).ExecuteCommandAsync();
                        }
                    }
                    var mpbdlist = new List<MMPlaybackDevice>();
                    foreach (var m in requestDto.mMPlaybackDevice)
                    {
                        var mpbdcount = await _db.Queryable<MMPlaybackDevice>().Where(a => a.CampaignID == requestDto.CampaignID && a.DeviceID == m.DeviceID).ToListAsync();
                        if (mpbdcount.Count == 0) //如果参数对象中的设备不存在于数据库中，则为新增
                        {
                            mpbdlist.Add(new MMPlaybackDevice
                            {
                                CampaignID = m.CampaignID,
                                DeviceID = m.DeviceID,
                            });
                        }
                    }
                    await _db.Insertable(mpbdlist).ExecuteCommandAsync();
                });

                if (result.IsSuccess)
                {
                    responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
    }
}
