﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Common
{
    public class 叫号规则
    {
        public string id { get; set; }
        /// <summary>
        /// 号码长度
        /// </summary>
        public int 长度 { get; set; }
        public int[] 过滤指定数字 { get; set; }
        public int 开始号码 { get; set; }
        public int 自增步长 { get; set; } = 1;


        public string 取号(int 原始号码)
        {
            //var 原始号码 += 自增步长;
            return null;
        }



        public static List<叫号规则> 获取叫好规则()
        {
            List<叫号规则> list = new List<叫号规则>()
            {
                new  叫号规则(){  id="1", 长度=4, 过滤指定数字=[8], 开始号码=1, 自增步长=1},
                new  叫号规则(){  id="2", 长度=6, 过滤指定数字=[4,7], 开始号码=2000, 自增步长=1},
                new  叫号规则(){  id="3", 长度=6, 过滤指定数字=[4,7], 开始号码=2000, 自增步长=1},
                new  叫号规则(){  id="3", 长度=6, 过滤指定数字=[4,7], 开始号码=2000, 自增步长=1},



            };
            return list;
        }
        public static 叫号规则 根据规则ID获取(string id)
        {
            return 获取叫好规则().FirstOrDefault(i => i.id == id);
        }
    }
}
