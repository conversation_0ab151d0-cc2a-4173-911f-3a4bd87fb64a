﻿using Azure;
using HdProject.Application.Services.Interfaces.GrouponBase;
using HdProject.Domain.Context.GrouponBase;
using HdProject.Domain.DTOs.GrouponBase;
using HdProject.Domain.DTOs.RMS.SummaryStoreTimeSlotDailyList;
using HdProject.Domain.Entities.GroupBase;
using HdProject.Domain.Entities.Rms;
using HdProject.Domain.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using static HdProject.Domain.DTOs.RMS.RefundDto;

namespace HdProject.Application.Services.GrouponBase
{
    public class TransferService : ITransferService
    {
        private readonly IRepositoryGroupBase<NGrouponCodeInfo> _nGrouponCodeInfoRecord;
        private readonly IRepositoryGroupBase<NGrouponInfo> _nGrouponInfoRecord;
        private readonly IRepositoryGroupBase<TransferCardDto> _transferCardRecord;
        private readonly IRepositoryGroupBase<ClaimDto> _claimDtoRecord;
        private readonly IRepositoryGroupBase<GetClaimKeyListDto> _claimKeyListRecord;
        private readonly IRepositoryGroupBase<NGrouponGiveCodeInfo> _nGrouponGiveCodeInfoRecord;
        public TransferService(
            IRepositoryGroupBase<NGrouponCodeInfo> nGrouponCodeInfoRecord,
            IRepositoryGroupBase<NGrouponInfo> nGrouponInfoRecord,
            IRepositoryGroupBase<TransferCardDto> transferCardRecord,
            IRepositoryGroupBase<ClaimDto> claimDtoRecord,
             IRepositoryGroupBase<GetClaimKeyListDto> claimKeyListRecord,
             IRepositoryGroupBase<NGrouponGiveCodeInfo> nGrouponGiveCodeInfoRecord


            )
        {
            _nGrouponCodeInfoRecord = nGrouponCodeInfoRecord;
            _nGrouponInfoRecord = nGrouponInfoRecord;
            _claimKeyListRecord = claimKeyListRecord;
            _transferCardRecord = transferCardRecord;
            _claimDtoRecord = claimDtoRecord;
            _nGrouponGiveCodeInfoRecord = nGrouponGiveCodeInfoRecord;


        }



        /// <summary>
        /// 可分享卡卷列表查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<GetTransferDto>> GetSharedCardList(GetTransferCardContext context)
        {
            //查询
            try
            {
                var nGrouponCodeInfoList = await _nGrouponCodeInfoRecord.GetListAsync(w => w.Openid == context.openid && w.CodeStatus == context.CodeStatus && w.isDel == false);
                if (!nGrouponCodeInfoList.Any())
                {
                    return new List<GetTransferDto>();
                }
                var GrouponKeys = nGrouponCodeInfoList.Select(x => x.GrouponKey).ToList();

                Expression<Func<NGrouponInfo, bool>> listExpression = w => GrouponKeys.Contains(w.GrouponKey) && w.IsGive == context.IsGive && w.IsDel == false;

                var nGrouponInfoList = await _nGrouponInfoRecord.GetPageListAsync(
                    context.Paging,
                    listExpression);

                var result = (from g in nGrouponCodeInfoList
                              join n in nGrouponInfoList on g.GrouponKey equals n.GrouponKey
                              select new GetTransferDto
                              {
                                  CodeStatu=g.CodeStatus,
                                  Code = g.Code,
                                  CodeStatus = g.CodeStatus,
                                  CodeKey = g.CodeKey,
                                  GrouponKey = n.GrouponKey,
                                  ProjectKey = n.ProjectKey,
                                  GrouponName = n.GrouponName,
                                  GoodsPrice = n.GoodsPrice,
                                  GrouponPrice = n.GrouponPrice,
                                  ImgUrl = n.ImgUrl,
                                  Period = n.Period,
                                  Explain = n.Explain,
                                  FdNo = n.FdNo,
                                  CardModelNo = n.CardModelNo,
                                  IsSpecialOffer = n.IsSpecialOffer,
                                  IsUse = n.IsUse,
                                  IsDel = n.IsDel,
                                  UseShopIds = n.UseShopIds,
                                  UseAreaNo = n.UseAreaNo,
                                  SortNumber = n.SortNumber,
                                  UserId = n.UserId,
                                  UserName = n.UserName,
                                  InputTime = n.InputTime,
                                  ValidModelNo = n.ValidModelNo,
                                  Valid = n.Valid,
                                  ValidEnd = n.ValidEnd,
                                  SaleModelNo = n.SaleModelNo,
                                  SaleStartTime = n.SaleStartTime,
                                  SaleEndTime = n.SaleEndTime,
                                  IsInvalid = n.IsInvalid,
                                  CodeNumber = n.CodeNumber,
                                  CodeSaleMin = n.CodeSaleMin,
                                  CodeSaleMax = n.CodeSaleMax,
                                  BookDay = n.BookDay,
                                  IsMember = n.IsMember,
                                  SingleConsume = n.SingleConsume,
                                  DisableDay = n.DisableDay,
                                  CardLableNo = n.CardLableNo,
                                  BuildNo = n.BuildNo,
                                  CodeFormat = n.CodeFormat,
                                  UseAreaField = n.UseAreaField,
                                  UseModelNo = n.UseModelNo,
                                  SelType = n.SelType,
                                  IsGive = n.IsGive,
                                  //GiftTime = g.GiftTime
                              }).ToList();


                return result;
            }
            catch (Exception ex)
            {
                throw new Exception("可分享卡卷列表查询失败，请稍后重试", ex);
            }
        }

        /// <summary>
        /// 转赠卡卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<TransferCardDto>> TransferCardRecord(TransferCardContext context)
        {
            try
            {
              
                var claimKey = Guid.NewGuid();
               

                    // 创建参数对象GroupedGiftDto
                    var TransferCards = new
                    {
                        t = 1,
                        CodeKeys = context.CodeKey,
                        openid = context.openid,
                        ClaimKey = claimKey
                    };

                    // 调用存储过程并返回结果
                     return await _transferCardRecord.GetAllByProcedureAsync(
                       "Ex_NGrouponGiveCodeInfo_Test",
                       TransferCards);

                    

        }

            catch (Exception ex)
            {

                throw new Exception("转赠卡卷失败，请稍后重试", ex);
    }
}

        /// <summary>
        /// 领取卡卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<ClaimDto>> ClaimRecord(ClaimContext context)
        {
            try
            {


                // 创建参数对象
                var TransferCards = new
                {
                    t = 2,
                    openid = context.openid,
                    ClaimKey = context.ClaimKey

                };

                // 调用存储过程并返回结果
                return await _claimDtoRecord.GetAllByProcedureAsync(
                   "Ex_NGrouponGiveCodeInfo_Test",
                   TransferCards);

            }

            catch (Exception ex)
            {
                throw new Exception("领取卡卷失败，请稍后重试", ex);
            }
        }
        /// <summary>
        /// 根据ClaimKey获取已转赠的卡卷详情
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<GetClaimKeyListDto>> GetClaimKeyList(GetClaimKeyListContext context)
        {
            try
            {


                // 创建参数对象
                var TransferCards = new
                {
                    t = 0,

                    ClaimKey = context.ClaimKey

                };

                // 调用存储过程并返回结果
                return await _claimKeyListRecord.GetAllByProcedureAsync(
                   "Ex_NGrouponGiveCodeInfo_Test",
                   TransferCards);

            }

            catch (Exception ex)
            {
                throw new Exception("根据ClaimKey获取已转赠的卡卷详情失败，请稍后重试", ex);
            }
        }
        /// <summary>
        /// 获取已分享卡卷列表//
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<GroupedGiftDto>> GetNoSharedCardList(GetNoSharedCardListContext context)
        {

            try
            {

                //var nGrouponCodeInfoList = await _nGrouponCodeInfoRecord.GetListAsync(w => w.Openid == context.openid && w.CodeStatus == context.CodeStatus && w.isDel == false);
                var nGrouponGiveCodeInfoList = await _nGrouponGiveCodeInfoRecord.GetListAsync(w => w.Openid == context.openid && w.GiveCodeStatu != 0);
                var CodeKeys = nGrouponGiveCodeInfoList.Select(x => x.CodeKey).ToList();
                var nGrouponCodeInfoList = await _nGrouponCodeInfoRecord.GetListAsync(w => w.isDel == false && CodeKeys.Contains(w.CodeKey) && (w.CodeStatus == 0 || w.CodeStatus == 4));
                var GrouponKeys = nGrouponCodeInfoList.Select(x => x.GrouponKey).ToList();

                if (!nGrouponGiveCodeInfoList.Any())
                {
                    return new List<GroupedGiftDto>();
                }


                Expression<Func<NGrouponInfo, bool>> listExpression = w => GrouponKeys.Contains(w.GrouponKey) && w.IsGive == context.IsGive && w.IsDel == false;

                var nGrouponInfoList = await _nGrouponInfoRecord.GetPageListAsync(
                    context.Paging,
                    listExpression);
                var result = (
 from c in nGrouponGiveCodeInfoList
 join g in nGrouponCodeInfoList on c.CodeKey equals g.CodeKey
 join n in nGrouponInfoList on g.GrouponKey equals n.GrouponKey
 select new GetTransferDto
 {
     GiveCodeStatu = c.GiveCodeStatu,
     Code = g.Code,
     ClaimKey = c.ClaimKey,
     CodeStatus = g.CodeStatus,
     CodeKey = g.CodeKey,
     GrouponKey = n.GrouponKey,
     ProjectKey = n.ProjectKey,
     GrouponName = n.GrouponName,
     GoodsPrice = n.GoodsPrice,
     GrouponPrice = n.GrouponPrice,
     ImgUrl = n.ImgUrl,
     Period = n.Period,
     Explain = n.Explain,
     FdNo = n.FdNo,
     CardModelNo = n.CardModelNo,
     IsSpecialOffer = n.IsSpecialOffer,
     IsUse = n.IsUse,
     IsDel = n.IsDel,
     UseShopIds = n.UseShopIds,
     UseAreaNo = n.UseAreaNo,
     SortNumber = n.SortNumber,
     UserId = n.UserId,
     UserName = n.UserName,
     InputTime = n.InputTime,
     ValidModelNo = n.ValidModelNo,
     Valid = n.Valid,
     ValidEnd = n.ValidEnd,
     SaleModelNo = n.SaleModelNo,
     SaleStartTime = n.SaleStartTime,
     SaleEndTime = n.SaleEndTime,
     IsInvalid = n.IsInvalid,
     CodeNumber = n.CodeNumber,
     CodeSaleMin = n.CodeSaleMin,
     CodeSaleMax = n.CodeSaleMax,
     BookDay = n.BookDay,
     IsMember = n.IsMember,
     SingleConsume = n.SingleConsume,
     DisableDay = n.DisableDay,
     CardLableNo = n.CardLableNo,
     BuildNo = n.BuildNo,
     CodeFormat = n.CodeFormat,
     UseAreaField = n.UseAreaField,
     UseModelNo = n.UseModelNo,
     SelType = n.SelType,
     IsGive = n.IsGive,
     GiftTime = c.GiftTime
 }
)
.GroupBy(x => x.GiftTime)  // 按 GiftTime 分组
.Select(g => new GroupedGiftDto
{
    GiftTime = g.Key,  // 分组键（GiftTime）
    List = g.ToList()  // 该 GiftTime 下的所有记录
})
.ToList();

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception("获取已分享卡卷列表失败，请稍后重试", ex);
            }

        }
    }
}
