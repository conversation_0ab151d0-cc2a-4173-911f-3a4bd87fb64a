﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SongBase.WallPainting
{
    /// <summary>
    /// 模板明细
    /// </summary>
    /// 
    [SugarTable("WP_TopicITemplateDetails")]
    public class WPTopicITemplateDetails
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int DetailsID { get; set; }
        public int TemplateID { get; set; }
        public string Proportion { get; set; }
        public int? StartX { get; set; }
        public int? StartY { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public int? Sequence { get; set; }

        // 导航属性
        [SugarColumn(IsIgnore = true)]
        public WPTopicITemplate wPTopicITemplate { get; set; }
    }
}
