﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MaterialManagement
{
    /// <summary>
    /// 节目主表
    /// </summary>
    [SugarTable("MM_Playlist")]
    public class MMPlaylist
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int PlaylistID { get; set; }
        /// <summary>
        /// 节目单名称
        /// </summary>
        public string PlaylistName { get; set; }
        /// <summary>
        /// 布局模板ID
        /// </summary>
        public int LayoutID { get; set; }
        public bool IsActive { get; set; }
        public string? DisabledBy { get; set; }
        public DateTime? DisabledTime { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedTime { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedTime { get; set; }
        /// <summary>
        /// 节目详情实体集合
        /// </summary>
        /// 
        [Navigate(NavigateType.OneToMany, nameof(MMPlaylistDetail.PlaylistID))]
        public List<MMPlaylistDetail> PlaylistDetailList { get; set; }
    }

    /// <summary>
    /// 节目详情表
    /// </summary>
    [SugarTable("MM_PlaylistDetail")]
    public class MMPlaylistDetail
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int DetailID { get; set; }
        public int PlaylistID { get; set; }
        public string? ProgramName { get; set; }
        [Navigate(NavigateType.OneToOne,nameof(RegionID))]
        public MMLayoutRegion MMLayoutRegionEntity { get; set; }
        public int RegionID { get; set; }
        public bool IsActive { get; set; }
        public string? DisabledBy { get; set; }
        public DateTime? DisabledTime { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedTime { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedTime { get; set; }
        /// <summary>
        /// 节目详情明细实体集合
        /// </summary>
        /// 
        [Navigate(NavigateType.OneToMany, nameof(MMPlaylistDetailXq.DetailID))]
        public List<MMPlaylistDetailXq> PlaylistDetailXqList { get; set; }
    }

    /// <summary>
    /// 节目详情明细表
    /// </summary>
    [SugarTable("MM_PlaylistDetailXq")]
    public class MMPlaylistDetailXq
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int DetailXqID { get; set; }
        public int DetailID { get; set; }
        public int FileID { get; set; }
        public int Sequence { get; set; }
        public int? AdjustedDuration { get; set; }
        [Navigate(NavigateType.OneToOne, nameof(FileID))]
        public MMFile MMFile { get; set; }
    }
}
