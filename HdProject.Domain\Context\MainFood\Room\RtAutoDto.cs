﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Result.Page;
using SqlSugar;

namespace HdProject.Domain.Context.MainFood.Room
{
    /// <summary>
    /// 房间配送业务请求类
    /// </summary>
    public class RtAutoDto
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int IKey { get; set; }

        [SugarColumn(Length = 2)]
        public string RtNo { get; set; }

        [SugarColumn(Length = 5)]
        public string FdNo { get; set; }

        public short FdQty { get; set; }

        [SugarColumn(Length = 1)]
        public string CashType { get; set; }

        [SugarColumn(Length = 4)]
        public string CashUserId { get; set; }

        [SugarColumn(ColumnName = "msrepl_tran_version")]
        public Guid MsreplTranVersion { get; set; }
    }
    /// <summary>
    /// 查询
    /// </summary>
    public class RtAutoGetAllAsyncRequestDto: Pagination
    {
        /// <summary>
        /// 房间配送分页查询请求参数
        /// </summary>
        //public string UserId { get; set; }
        public int? Status { get; set; }
        public DateTime? AllDates { get; set; }
        /// <summary>
        /// 查询条件字符串
        /// </summary>
        public string? QueryCriteria { get; set; }
        //public Pagination Paging { get; set; }
    }

    /// <summary>
    /// 根据ID查询房间配送信息
    /// </summary>
    public class RtAutoGetByIdAsyncRequestDto
    {
        public string IKey { get; set; }
    }
    /// <summary>
    /// 新增房间信息请求类
    /// </summary>
    public class RtAutoAddRequestDto
    {
        public RtAutoDto Model { get; set; }
    }
    /// <summary>
    /// 删除房间配送信息请求类
    /// </summary>
    public class RtAutoDeleteRequestDto
    {
        public string? RtAutoID { get; set; }
    }
    /// <summary>
    /// 修改房间配送信息请求类
    /// </summary>
    public class RtAutoUpdateRequestDto
    {
        public RtAutoDto Model { get; set; }
    }
}
