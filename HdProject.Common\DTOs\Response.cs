﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Common.DTOs
{
    public class ResponseContext<T>
    {
        private string _message;

        public int code { get; set; }

        public string message
        {
            get
            {
                return _message;
            }
            set
            {
                _message = value;
            }
        }

        public ResponseType state { get; set; }

        public T data { get; set; }

        public ResponseContext()
        {
            state = ResponseType.success;
        }
    }
    public enum ResponseType
    {
        info,
        success,
        warning,
        error
    }
}
