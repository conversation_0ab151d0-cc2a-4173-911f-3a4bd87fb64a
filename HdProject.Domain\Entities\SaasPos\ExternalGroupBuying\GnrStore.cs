﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.ExternalGroupBuying
{
    /// <summary>
    /// 团购门店表
    /// </summary>
    [SugarTable("Gnr_Store")]
    public class GnrStore
    {
        [SugarColumn(ColumnName = "StoreId", IsPrimaryKey = true)]
        public int StoreId { get; set; }
        public string StoreName { get; set; }
    }
}
