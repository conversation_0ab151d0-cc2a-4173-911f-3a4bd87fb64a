﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.CommodityManagement;
using HdProject.Domain.Context.RMS.SummaryStoreTimeSlotDailyList;
using HdProject.Domain.DTOs.CommodityManagement;
using HdProject.Domain.DTOs.RMS.SummaryStoreTimeSlotDailyList;

namespace HdProject.Application.Services.Interfaces.Rms
{
    public interface ISummaryStoreTimeSlotDailyService
    {

        /// <summary>
        /// 获取预约时段详情
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<List<SummaryStoreTimeSlotDailyDto>> GetSummaryStoreTimeSlotDailyRecord(SummaryStoreTimeSlotDailyContext context);
        //统计预约时段数据
        Task<bool> GetSummaryStoreTimeSlotDailyListRecord(SummaryStoreTimeSlotDailyListContext context);
    }

}
