﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SaasPos;
using HdProject.Domain.Entities;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.SaasPos
{
    public class BookLeaderCommissionService : IBookLeaderCommissionService
    {
        private readonly IRepositorySaas<BookLeaderCommission> _Repository;
        public BookLeaderCommissionService(IRepositorySaas<BookLeaderCommission> userRepository) => _Repository = userRepository;
        //ShopBookLeaderService _leaderCommissionSerice;
        //public ShopBookLeaderService(IRepositorySaas<BookLeaderCommission> leaderRepository) => _leaderCommissionRepository = leaderRepository;

        

    }
}
