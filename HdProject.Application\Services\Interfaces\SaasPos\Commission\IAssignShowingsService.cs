﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.Commission.AssignEmployee;
using HdProject.Domain.Context.SaasPos.Commission.AssignShowings;
using HdProject.Domain.DTOs.SaasPos.Commission.AssignShowings;

namespace HdProject.Application.Services.Interfaces.SaasPos.Commission
{
    /// <summary>
    /// 指派看房接口
    /// </summary>
    public interface IAssignShowingsService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<AssignShowingsGetByIdResponseDto> GetByIdAsync(AssignShowingsGetByIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<AssignShowingsGetAllResponseDto> GetAllAsync(AssignShowingsGetAllRequestDto requestDto);
        /// <summary>
        /// 根据房间ID查询房间历史派房记录
        /// </summary>
        /// <returns></returns>
        Task<AssignShowingsGetByRoomResponseDto> GetByRoomAsync(AssignShowingsGetByRoomRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<AssignShowingsAddResponseDto> AddAsync(AssignShowingsAddRequestDto requestDto);

        /// <summary>
        /// 传入员工列表判断当前员工是否重复派房
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<AssignShowingsSWRResponseDto> ShouldWeReassign(AssignShowingsSWRRequestDto requestDto);

        /// <summary>
        /// 查询当前专员上次（最后一次）订单的结账状态
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<AssignShowingsCLOSResponseDto> ComLastOrderSettled(AssignShowingsCLOSRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<AssignShowingsUpdateResponseDto> UpdateAsync(AssignShowingsUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<AssignShowingsDeleteResponseDto> DeleteAsync(AssignShowingsDeleteRequestDto requestDto);
    }
}
