﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.SongSyncCenter
{
    /// <summary>
    /// 歌星信息表
    /// </summary>
    public partial class SingInfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public int SingId { get; set; }
        public string SingName1 { get; set; }
        public string SingName2 { get; set; }
        public int SingSexId { get; set; }
        public int SingCountryId { get; set; }
        public int SongCount { get; set; }
        public string PYStr { get; set; }
        public string BHStr { get; set; }
        public int SeqNo { get; set; }
        public int HotClick { get; set; }
        public string StageName { get; set; }
        public string DeleteReason { get; set; }
        public bool IsDeleted { get; set; }
        public string DebutYear { get; set; }
    }
}
