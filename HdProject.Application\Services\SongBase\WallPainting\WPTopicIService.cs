﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Furion.DatabaseAccessor;
using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Common.Utility;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicI;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIBdFile;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIFile;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplate;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplateDetails;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicI;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Entities.SongBase.WallPainting;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Routing.Template;
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using SqlSugar;
using StackExchange.Redis;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace HdProject.Application.Services.SongBase.WallPainting
{
    /// <summary>
    /// 主题
    /// </summary>
    public class WPTopicIService : IWPTopicIService
    {
        private readonly IRepositorySongBase<WPTopicI> _repositorySaasWPTopicI;//主题
        private readonly IWebHostEnvironment _env;
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;
        private readonly FileManagement _management;
        public WPTopicIService(IRepositorySongBase<WPTopicI> repositorySaasWPTopicI, IWebHostEnvironment env, ISqlSugarClient sqlSugarClient, IConfiguration configuration, IMapper mapper, FileManagement management)
        {
            _repositorySaasWPTopicI = repositorySaasWPTopicI;
            _env = env;
            _sqlSugarClient = sqlSugarClient;
            _configuration = configuration;
            _mapper = mapper;
            _management = management;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("SongBase");
            }
        }
        /// <summary>
        /// 新增主题
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WPTopicIAddResponseDto> AddAsync(WPTopicIAddRequestDto requestDto)
        {
            WPTopicIAddResponseDto responseDto = new WPTopicIAddResponseDto();
            try
            {
                var result = await _db.Ado.UseTranAsync(async () =>
                {

                    if (requestDto.Model.topicIBdFiles.Count == 0)
                    {
                        throw new Exception("无法新增，未上传图片信息！");
                    }
                    var TopicFileId = await MergeMuralsAsync(requestDto.Model);//调用合并图片的逻辑

                    var wPTopicIModel = new WPTopicI()
                    {
                        TopicName = requestDto.Model.TopicName,
                        TemplateID = requestDto.Model.TemplateID,
                        CreatedBy = requestDto.Model.CreatedBy,
                        CreatedTime = DateTime.Now,
                        FileID = TopicFileId
                    };
                    var WPTopicIID = await _db.Insertable(wPTopicIModel).ExecuteReturnIdentityAsync();//首先新增主题，并返回其自增列ID

                    //List<MMPlaylistDetail> PlaylistDetail = new List<MMPlaylistDetail>();
                    foreach (var tbfs in requestDto.Model.topicIBdFiles)
                    {
                        var wtbf = new WPTopicIBdFile()
                        {
                            TopicID = WPTopicIID,
                            DetailsID = tbfs.DetailsID,
                            FileID = tbfs.FileID
                        };
                        await _db.Insertable(wtbf).ExecuteCommandAsync();//新增关联表，返回成功数

                    }
                });

                if (result.IsSuccess)
                {
                    responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        //合并图片
        public async Task<int> MergeMuralsAsync(WPTopicICreateDto requestDto)
        {
            int FId = 0;
            var TemplateFile = await _db.Queryable<WPTopicITemplate>()
            .Where(c => c.TemplateID == requestDto.TemplateID && c.IsActive == false).Select(a => new
            {
                FileID = a.FileID,
            }).FirstAsync();//查询主题模板的fileID
            var uploadFolder = _configuration["FileUpload:WallPaintingUploadFolder"] ?? "wpuploads";
            var FileModel = await _db.Queryable<WPTopicIFile>()
            .Where(c => c.FileID == TemplateFile.FileID && c.IsActive == false).Select(a => new
            {
                FileID = a.FileID,
                FileName = a.FileName,
                FilePath = a.FilePath,
                Width = a.Width,
                Height = a.Height
            }).FirstAsync();//查询模板的素材信息

            //var filelist = new List<WPTopicIFile>();

            //循环获取用户自定义图

            //requestDto.Model.topicIBdFiles.ToList().
            //    .Select((o, i) => new { Id = o.Id, Name = o.Name, SchoolName = i.Name }).ToList();

            List<WPTopicIBdFileSpecialDto> wtbfsd = new List<WPTopicIBdFileSpecialDto>();
            foreach (var tbfs in requestDto.topicIBdFiles)
            {


                var wttd = await _db.Queryable<WPTopicITemplateDetails>().Where(o => o.DetailsID == tbfs.DetailsID)
                 .Select(a =>
                 new
                 {
                     StartX = a.StartX,
                     StartY = a.StartY,
                     Width = a.Width,
                     Height = a.Height,
                 })
                 .FirstAsync();

                var wtf = await _db.Queryable<WPTopicIFile>().Where(o => o.FileID == tbfs.FileID)
                 .Select(a =>
                 new
                 {
                     FilePath = a.FilePath,
                 })
                 .FirstAsync();

                wtbfsd.Add(new WPTopicIBdFileSpecialDto
                {
                    StartX = (int)wttd.StartX,
                    StartY = (int)wttd.StartY,
                    Width = (int)wttd.Width,
                    Height = (int)wttd.Height,
                    FilePath = wtf.FilePath,

                });
            }
            try
            {
                using (var templateImage = await Image.LoadAsync(Path.Combine(_env.WebRootPath, FileModel.FilePath)))//先加载模板图
                {
                    using (var ig = new Image<Rgba32>(templateImage.Width, templateImage.Height))
                    {
                        foreach (var path in wtbfsd)//循环小图路径列表
                        {
                            using (var image = await Image.LoadAsync(Path.Combine(_env.WebRootPath, path.FilePath))) //加载小图   
                            {
                                //调整小图片尺寸以匹配挖空区域
                                image.Mutate(ctx => ctx.Resize(path.Width, path.Height));
                                //合并图片(小图在下，模板在上)
                                ig.Mutate(ctx => ctx
                                .DrawImage(image, new Point(path.StartX, path.StartY), 1f)  // 先绘制小图
                                .DrawImage(templateImage, new Point(0, 0), 1f));  // 再绘制模板
                            }
                        ;
                        }
                        //保存合并结果
                        var outputPath = Path.Combine(uploadFolder, $"{Guid.NewGuid()}.png");
                        var fullPath = Path.Combine(_env.WebRootPath, outputPath);
                        await ig.SaveAsync(fullPath);
                        var fileInfo = new FileInfo(fullPath);
                        int? width = null;
                        int? height = null;
                        string? thumbnailPath = null;
                        // 生成缩略图
                        thumbnailPath = await _management.GenerateThumbnailAsync(fullPath, uploadFolder);
                        // 获取图片尺寸
                        using (var image = await Image.LoadAsync(fullPath))
                        {
                            width = image.Width;
                            height = image.Height;
                        }
                        var model = new WPTopicIFile
                        {
                            FileName = Path.GetFileName(outputPath),
                            FilePath = outputPath,
                            FormatType = "image/png",
                            FileSize = fileInfo.Length,
                            UploadedTime = DateTime.Now,
                            Width = width,
                            Height = height,
                            ThumbnailPath = thumbnailPath,
                        };
                        var result = await _db.Ado.UseTranAsync(async () =>
                        {
                            FId = await _db.Insertable(model).ExecuteReturnIdentityAsync();
                        });
                    }
                }
            }
            catch (Exception ex)
            {

                throw new Exception("操作失败：" + ex.Message);
            }
            return FId;

        }

        public async Task<WPTopicIDeleteResponseDto> DeleteAsync(WPTopicIDeleteRequestDto requestDto)
        {
            WPTopicIDeleteResponseDto responseDto = new WPTopicIDeleteResponseDto();
            try
            {
                var resultModel = await _repositorySaasWPTopicI.GetFirstAsync(a => a.TopicID == requestDto.TopicID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法删除，请检查该主题是否存在！");
                }
                resultModel.IsActive = true;
                resultModel.DisabledBy = resultModel.CreatedBy;
                resultModel.DisabledTime = DateTime.Now;
                var result = await _repositorySaasWPTopicI.UpdateAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        public async Task<WPTopicIGetAllResponseDto> GetAllAsync(WPTopicIGetAllRequestDto requestDto)
        {
            throw new NotImplementedException();
        }
        /// <summary>
        /// 根据ID查询主题信息以及关联信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WPTopicIGetByIdResponseDto> GetByIdAsync(WPTopicIGetByIdRequestDto requestDto)
        {
            WPTopicIGetByIdResponseDto responseDto = new WPTopicIGetByIdResponseDto();
            try
            {
                var result = await GetAssociationBdDetailsAsync(requestDto.TopicID);
                if (result != null)
                {
                    responseDto.Model = result;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }


        /// <summary>
        /// 根据ID查询，需要关联其他实体信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<WPTopicIDto> GetAssociationBdDetailsAsync(int id)
        {
            var topicI = await _db.Queryable<WPTopicI>()
                .Where(c => c.TopicID == id && c.IsActive == false)
                .FirstAsync();//查询主题信息
            if (topicI == null)
            {
                return null;
            }
            var templateModel = await _db.Queryable<WPTopicITemplate>()
            .Where(c => c.TemplateID == topicI.TemplateID && c.IsActive == false).Select(a => new WPTopicITemplateDto
            {
                TemplateID = a.TemplateID,
                TemplateName = a.TemplateName,
                TSupportFileNum = a.TSupportFileNum,
                FileID = a.FileID,
            }).FirstAsync();//查询主题模板

            templateModel.topicFile = await _db.Queryable<WPTopicIFile>().Where(f => f.FileID == templateModel.FileID && f.IsActive == false)
            .Select(k => new WPTopicIFileDto
            {
                FileID = k.FileID,
                FileName = k.FileName,
                FilePath = k.FilePath,
                FormatType = k.FormatType,
                FileSize = k.FileSize,
                Width = k.Width,
                Height = k.Height,
                ThumbnailPath = k.ThumbnailPath,
            }).FirstAsync();//查询主题模板关联的素材

            var DetailsModel = await _db.Queryable<WPTopicITemplateDetails>()
            .Where(c => c.TemplateID == templateModel.TemplateID).Select(a => new WPTopicITemplateDetailsDto
            {
                DetailsID = a.DetailsID,
                TemplateID = a.TemplateID,
                Proportion = a.Proportion,
                StartX = a.StartX,
                StartY = a.StartY,
                Width = a.Width,
                Height = a.Height,
                Sequence = a.Sequence,
            }).ToListAsync();//查询主题模板关联的区域明细

            foreach (var item in DetailsModel)
            {
                var wtbf = await _db.Queryable<WPTopicIBdFile>()
                    .Where(c => c.TopicID == topicI.TopicID && c.DetailsID == item.DetailsID)
                    .Select(a => new WPTopicIBdFileDto
                    {
                        TbfID = a.TbfID,
                        TopicID = a.TopicID,
                        DetailsID = a.DetailsID,
                        FileID = a.FileID,
                    }).FirstAsync();

                if (wtbf == null)
                {
                    continue;
                }

                wtbf.topicFile = await _db.Queryable<WPTopicIFile>()
                .Where(c => c.FileID == wtbf.FileID)
                .Select(a => new WPTopicIFileDto
                {
                    FileID = a.FileID,
                    FileName = a.FileName,
                    FilePath = a.FilePath,
                    FormatType = a.FormatType,
                    FileSize = a.FileSize,
                    Width = a.Width,
                    Height = a.Height,
                    ThumbnailPath = a.ThumbnailPath,
                }).FirstAsync();
                item.topicIBdFile = wtbf;
            }

            return new WPTopicIDto
            {
                TopicID = topicI.TopicID,
                TopicName = topicI.TopicName,
                TemplateID = topicI.TemplateID,
                Remarks = topicI.Remarks,
                FileID = topicI.FileID,
                template = templateModel,
                templateDetails = DetailsModel
            };
        }
        /// <summary>
        /// 根据用户OpenID(小程序传入)查询主题列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WPTopicIGetByUserOpenIdResponseDto> GetByUserOpenIdAsync(WPTopicIGetByUserOpenIdRequestDto requestDto)
        {
            WPTopicIGetByUserOpenIdResponseDto responseDto = new WPTopicIGetByUserOpenIdResponseDto();
            try
            {
                var predicate = PredicateBuilder.New<WPTopicI>(true);
                predicate = predicate.And(it => it.CreatedBy.Equals(requestDto.UserOpenID) && it.IsActive == false);//过滤数据
                var result = await GetUserOpenIDTolistAsync(requestDto, predicate);
                var model = _mapper.Map<List<WPTopicIDto>>(result);//使用AutoMapper进行对象属性映射
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 根据OpenID查询主题信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<WPTopicI>> GetUserOpenIDTolistAsync(Pagination page, Expression<Func<WPTopicI, bool>> whereExpression = null)
        {
            RefAsync<int> totalCount = 0;
            var query = _db.Queryable<WPTopicI>().Where(whereExpression).Mapper(d => d.wPTopicIFile, d => d.FileID).OrderByDescending(st => st.TopicID);
            if (whereExpression != null)
            {
                query = query.Where(whereExpression);
            }

            var list = await query.ToPageListAsync(page.Page, page.Rows, totalCount);
            page.Records = totalCount;
            return list;
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WPTopicIUpdateResponseDto> UpdateAsync(WPTopicIUpdateRequestDto requestDto)
        {
            WPTopicIUpdateResponseDto responseDto = new WPTopicIUpdateResponseDto();
            try
            {
                var result = await _db.Ado.UseTranAsync(async () =>
                {

                    if (requestDto.Model.topicIBdFiles.Count == 0)
                    {
                        throw new Exception("无法修改，未上传图片信息！");
                    }
                    var TopicFileId = await MergeMuralsAsync(requestDto.Model);//调用合并图片的逻辑
                    var mlt = await _db.Queryable<WPTopicI>().Where(a => a.TopicID == requestDto.Model.TopicID && a.IsActive == false).FirstAsync();
                    if (mlt == null)
                    {
                        throw new Exception("无法修改，请检查该主题是否存在！");
                    }
                    mlt.TopicName = requestDto.Model.TopicName;
                    mlt.ModifiedBy = mlt.CreatedBy;
                    mlt.ModifiedTime = DateTime.Now;
                    mlt.FileID = TopicFileId;
                    var WPTopicIID = await _db.Updateable(mlt).ExecuteCommandAsync();//修改主题

                    //List<MMPlaylistDetail> PlaylistDetail = new List<MMPlaylistDetail>();

                    var del = await _db.Deleteable<WPTopicIBdFile>().AS("[WP_TopicIBdFile]").Where("TopicID=@TopicID", new { TopicID = requestDto.Model.TopicID }).ExecuteCommandAsync();

                    var tbfsList = new List<WPTopicIBdFile>();

                    foreach (var tbfs in requestDto.Model.topicIBdFiles)
                    {
                        tbfsList.Add(new WPTopicIBdFile()
                        {
                            TopicID = tbfs.TopicID,
                            DetailsID = tbfs.DetailsID,
                            FileID = tbfs.FileID
                        });

                    }
                    await _db.Insertable(tbfsList).ExecuteCommandAsync();//批量新增主题关联素材表信息
                });

                if (result.IsSuccess)
                {
                    responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
    }
}
