﻿using NLog;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace HdProject.Common.Logging
{
    /// <summary>
    /// 全局日志服务，基于 NLog 实现
    /// </summary>
    public static class LogService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 获取 NLog 原生日志记录器
        /// </summary>
        /// <param name="name">日志记录器名称</param>
        /// <returns>NLog 日志记录器</returns>
        public static Logger GetNLogLogger(string name)
        {
            return LogManager.GetLogger(name);
        }

        /// <summary>
        /// 获取 NLog 原生日志记录器
        /// </summary>
        /// <typeparam name="T">日志记录器类型</typeparam>
        /// <returns>NLog 日志记录器</returns>
        public static Logger GetNLogLogger<T>()
        {
            return LogManager.GetLogger(typeof(T).FullName);
        }

        /// <summary>
        /// 获取当前类的日志记录器
        /// </summary>
        /// <returns>NLog 日志记录器</returns>
        public static Logger GetCurrentClassLogger()
        {
            return LogManager.GetCurrentClassLogger();
        }

        #region 日志记录方法

        /// <summary>
        /// 写入跟踪日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="callerMemberName">调用方法名</param> // 将由 NLog layout 处理
        /// <param name="callerFilePath">调用文件路径</param> // 将由 NLog layout 处理
        /// <param name="callerLineNumber">调用行号</param> // 将由 NLog layout 处理
        public static void Trace(string message)
        {
            // 直接记录消息，上下文由 NLog layout 处理
            _logger.Trace(message);
        }

        /// <summary>
        /// 写入调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="callerMemberName">调用方法名</param> // 将由 NLog layout 处理
        /// <param name="callerFilePath">调用文件路径</param> // 将由 NLog layout 处理
        /// <param name="callerLineNumber">调用行号</param> // 将由 NLog layout 处理
        public static void Debug(string message)
        {
            _logger.Debug(message);
        }

        /// <summary>
        /// 写入信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="callerMemberName">调用方法名</param> // 将由 NLog layout 处理
        /// <param name="callerFilePath">调用文件路径</param> // 将由 NLog layout 处理
        /// <param name="callerLineNumber">调用行号</param> // 将由 NLog layout 处理
        public static void Info(string message)
        {
            _logger.Info(message);
        }

        /// <summary>
        /// 写入警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="callerMemberName">调用方法名</param> // 将由 NLog layout 处理
        /// <param name="callerFilePath">调用文件路径</param> // 将由 NLog layout 处理
        /// <param name="callerLineNumber">调用行号</param> // 将由 NLog layout 处理
        public static void Warn(string message)
        {
            _logger.Warn(message);
        }

        /// <summary>
        /// 写入错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="callerMemberName">调用方法名</param> // 将由 NLog layout 处理
        /// <param name="callerFilePath">调用文件路径</param> // 将由 NLog layout 处理
        /// <param name="callerLineNumber">调用行号</param> // 将由 NLog layout 处理
        public static void Error(string message)
        {
            _logger.Error(message);
        }

        /// <summary>
        /// 写入错误日志
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">日志消息</param>
        /// <param name="callerMemberName">调用方法名</param> // 将由 NLog layout 处理
        /// <param name="callerFilePath">调用文件路径</param> // 将由 NLog layout 处理
        /// <param name="callerLineNumber">调用行号</param> // 将由 NLog layout 处理
        public static void Error(Exception exception, string message = null)
        {
            // NLog 会自动处理异常和消息，上下文由 layout 处理
            _logger.Error(exception, message);
        }

        /// <summary>
        /// 写入致命错误日志
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">日志消息</param>
        /// <param name="callerMemberName">调用方法名</param> // 将由 NLog layout 处理
        /// <param name="callerFilePath">调用文件路径</param> // 将由 NLog layout 处理
        /// <param name="callerLineNumber">调用行号</param> // 将由 NLog layout 处理
        public static void Fatal(Exception exception, string message = null)
        {
            // NLog 会自动处理异常和消息，上下文由 layout 处理
            _logger.Fatal(exception, message);
        }

        #endregion

        #region 请求响应日志

        /// <summary>
        /// 记录方法调用参数
        /// </summary>
        /// <param name="args">参数对象</param>
        /// <param name="callerMemberName">调用方法名 (自动获取)</param>
        public static void LogMethodParams(object args, [CallerMemberName] string methodName = "")
        {
            // 使用结构化日志记录参数
            // NLog layout 需要配置为能处理 @ 序列化标记 (如使用 JsonLayout 或 ${event-properties})
            _logger.Info("方法 [{MethodName}] 的输入参数: {@MethodArgs}", methodName, args);
        }

        /// <summary>
        /// 记录方法返回值
        /// </summary>
        /// <param name="result">返回值对象</param>
        /// <param name="callerMemberName">调用方法名 (自动获取)</param>
        public static void LogMethodResult(object result, [CallerMemberName] string methodName = "")
        {
            _logger.Info("方法 [{MethodName}] 的返回值: {@MethodResult}", methodName, result);
        }

        /// <summary>
        /// 记录API调用信息
        /// </summary>
        /// <param name="request">请求参数对象</param>
        /// <param name="response">返回结果对象</param>
        /// <param name="executionTimeMs">执行时间(毫秒)</param>
        public static void LogApiCall(string apiName, object request, object response, long executionTimeMs = 0)
        {
            _logger.Info("API调用 [{ApiName}] 执行时间: {ExecutionTimeMs}ms 请求: {@Request} 响应: {@Response}",
                         apiName, executionTimeMs, request, response);
        }

        /// <summary>
        /// 记录API异常信息
        /// </summary>
        /// <param name="request">请求参数对象</param>
        /// <param name="ex">异常</param>
        public static void LogApiError(string apiName, object request, Exception ex)
        {
            // 异常信息会由 NLog 自动附加
            _logger.Error(ex, "API异常 [{ApiName}] 请求: {@Request}", apiName, request);
        }

        #endregion
    }
}