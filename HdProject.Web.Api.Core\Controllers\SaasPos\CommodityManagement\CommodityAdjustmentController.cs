﻿using HdProject.Application.Services.Interfaces.SaasPos.CommodityManagement;
using HdProject.Application.Services.Interfaces.SaasPos;
using HdProject.Application.Services.SaasPos;
using HdProject.Domain.Context;
using HdProject.Domain.Context.CommodityManagement;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using HdProject.Domain.DTOs.CommodityManagement;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.CommodityAddition
{
    /// <summary>
    /// 商品调整
    /// </summary>
    [Route("[controller]/[Action]")]
    public class CommodityAdjustmentController : PublicControllerBase
    {
        private readonly ICommodityAdjustmentService _commodityAdjustmentService;


        public CommodityAdjustmentController(ICommodityAdjustmentService commodityAdjustmentService)
            => _commodityAdjustmentService = commodityAdjustmentService;

        /// <summary>
        /// 获取草稿箱个人记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetCommDraftRecord([FromQuery] DraftContext context)
        {
            var res = await _commodityAdjustmentService.GetCommDraftRecord(context);
            return ApiPaged(res, context.Paging);

        }
        /// <summary>
        /// 获取草稿详情个人记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetCommDraftListRecord([FromQuery] DraftListContext context)
        {
            var res = await _commodityAdjustmentService.GetCommDraftListRecord(context);
            return ApiPaged(res,context.Paging);

        }
        /// <summary>
        /// 删除草稿个人记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DeleteCommDraftRecord(DelectDraftRecordContext context)
        {
            var res = await _commodityAdjustmentService.DeleteCommDraftRecord(context);
            return ApiData(res);

        }
        ///// <summary>
        ///// 添加或修改草稿记录
        ///// </summary>
        ///// <param name="context"></param>
        ///// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> AddOrUpdateCommDraftRecords(List<AdjustmentRequestContext> context)
        {
            var res = await _commodityAdjustmentService.AddOrUpdateCommDraftRecords(context);
            return ApiData(res);

        }
        ///// <summary>
        /////  查询已有商品
        ///// </summary>
        ///// <param name="context"></param>
        ///// <returns></returns>

        [HttpGet]
        public async Task<IActionResult> GetCommExistingGoods([FromQuery] GetExistingGoodsContext context)
        {
            var res = await _commodityAdjustmentService.GetCommExistingGoods(context);
            return ApiPaged(res, context.Paging);

        }
        /// <summary>
        /// 获取历史调价
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetCommHistoricalPrice([FromQuery] CommodityPriceHistoryContext context)
        {
            var res = await _commodityAdjustmentService.GetCommHistoricalPrice(context);
            return ApiPaged(res, context.Paging);

        }

        /// <summary>
        /// 查看已发布
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetCommPublished([FromQuery] GetCommPublishedContext context)
        {
            var res = await _commodityAdjustmentService.GetCommPublished(context);
            return ApiPaged(res, context.Paging);

        }
        /// <summary>
        /// 同步门店
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> CommSynchronousStore(CommSynchronousStoreContext context)
        {
            var res = await _commodityAdjustmentService.CommSynchronousStore(context);
            return ApiData(res);

        }
    }
}
