﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Common.SysService
{
    public class CallNumberStandard
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 号码开始长度
        /// </summary>
        public int CallNumberLength { get; set; }
        /// <summary>
        /// 过滤数字
        /// </summary>
        public int[] FilterNumbers { get; set; }
        /// <summary>
        /// 起始号码
        /// </summary>
        public int StartNumber { get; set; }
        /// <summary>
        /// 自增步长
        /// </summary>
        public int StepLength { get; set; } = 1;

        public long LogicalOperation(int number)
        {

            number += StepLength;//增加步长的结果
            char[] chars = Convert.ToString(number).ToCharArray();
            for (int i = 0; i < chars.Length; i++)
            {
                for (int j = 0; j < FilterNumbers.Length; j++)
                {
                    if (int.TryParse(chars[i].ToString(), out int digit) && digit == FilterNumbers[j])
                    {
                        do
                        {
                            chars[i] = (char)(chars[i] + 1);

                        } while (int.TryParse(chars[i].ToString(), out int digit2) && digit2 == FilterNumbers[j]);
                    }
                }
            }
            var newNumber = long.Parse(new string(chars));
            return newNumber;
        }


        /// <summary>
        /// 查询全部规则
        /// </summary>
        /// <returns></returns>
        public static List<CallNumberStandard> GetAllStandard()
        {
            List<CallNumberStandard> list = new List<CallNumberStandard>()
            {
                new  CallNumberStandard(){  Id=1, CallNumberLength=4, FilterNumbers=[4,7], StartNumber=1000, StepLength=1},
                new  CallNumberStandard(){  Id=2, CallNumberLength=4, FilterNumbers=[4,7], StartNumber=2000, StepLength=1},
                new  CallNumberStandard(){  Id=3, CallNumberLength=6, FilterNumbers=[4,7], StartNumber=200000, StepLength=100},
            };
            return list;
        }
        /// <summary>
        /// 根据规则ID获取规则信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static CallNumberStandard GetByIdStandard(int id)
        {
            return GetAllStandard().FirstOrDefault(i => i.Id == id);
        }



    }
}
