﻿using HdProject.Application.Services.Interfaces;
using HdProject.Common.DTOs;
using HdProject.Domain.Entities;
using HdProject.Domain.Interfaces;
using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using HdProject.Common.Logging;

namespace HdProject.Application.Services
{
    public class UserService : IUserService
    {
        private readonly IRepositorySaas<User> _userRepository; 
        private readonly IRepository<UserTest> _userTestRepository;
        private readonly IJwtService _jwtService;

        public UserService(
            IRepositorySaas<User> userRepository,
            IRepository<UserTest> userTestRepository,
            IJwtService jwtService)
        {
            _userRepository = userRepository;
            _userTestRepository = userTestRepository;
            _jwtService = jwtService;
        }

        public async Task<UserDto> Get()
        {
            var user = await _userTestRepository.GetByIdAsync(1);
            return new UserDto { Name = user.Name };
        }

        public async Task<UserDto> GetUserByIdAsync(int id)
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
                return null;

            return MapToUserDto(user);
        }

        public async Task<User> GetUserByUsernameAsync(string username)
        {
            try
            {
                var user = await _userRepository.GetFirstAsync(u => u.UserName == username);
                return user;
            }
            catch
            {
                // 如果查询失败，返回null
                return null;
            }
        }

        public async Task<LoginResult> LoginAsync(UserLoginDto loginDto)
        {
            LogService.Info($"用户 {loginDto.UserName} 尝试登录");

            // 检查用户是否存在
            var user = await GetUserByUsernameAsync(loginDto.UserName);
            if (user == null)
            {
                LogService.Warn($"登录失败：用户名 {loginDto.UserName} 不存在");
                return LoginResult.Fail("用户名不存在");
            }

            // 验证密码
            var passwordHash = ComputePasswordHash(loginDto.Password);
            if (user.PasswordHash != passwordHash)
            {
                LogService.Warn($"登录失败：用户 {loginDto.UserName} 密码错误");
                return LoginResult.Fail("密码错误");
            }

            // 更新最后登录时间
            user.LastLoginDate = DateTime.Now;
            await _userRepository.UpdateAsync(user);

            // 生成JWT令牌
            var tokenResponse = _jwtService.GenerateToken(user);

            // 更新用户的刷新令牌到数据库
            user.RefreshToken = tokenResponse.RefreshToken;
            user.RefreshTokenExpiryTime = tokenResponse.RefreshTokenExpiresAt;
            await _userRepository.UpdateAsync(user);

            LogService.Info($"用户 {loginDto.UserName} 登录成功");
            return LoginResult.Success(tokenResponse);
        }

        public async Task<bool> ValidateUserCredentialsAsync(string username, string password)
        {
            var user = await GetUserByUsernameAsync(username);
            if (user == null)
                return false;

            var passwordHash = ComputePasswordHash(password);
            return user.PasswordHash == passwordHash;
        }

        public async Task<LoginResult> RefreshTokenAsync(string refreshToken)
        {
            LogService.Debug($"尝试刷新令牌: {refreshToken.Substring(0, Math.Min(10, refreshToken.Length))}...");

            // 验证刷新令牌
            var principal = _jwtService.ValidateRefreshToken(refreshToken);
            if (principal == null)
            {
                LogService.Warn("刷新令牌失败: 无效的刷新令牌");
                return LoginResult.Fail("无效的刷新令牌");
            }

            // 获取用户ID
            var userIdClaim = principal.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                LogService.Warn("刷新令牌失败: 无效的用户信息");
                return LoginResult.Fail("无效的用户信息");
            }

            // 获取用户信息
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                LogService.Warn("刷新令牌失败: 用户不存在");
                return LoginResult.Fail("用户不存在");
            }

            // 检查刷新令牌是否匹配数据库中的记录
            if (user.RefreshToken != refreshToken || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
            {
                LogService.Warn("刷新令牌失败: 刷新令牌已过期或无效");
                return LoginResult.Fail("刷新令牌已过期或无效");
            }

            // 生成新的访问令牌和刷新令牌
            var tokenResponse = _jwtService.GenerateToken(user);

            // 更新数据库中的刷新令牌
            user.RefreshToken = tokenResponse.RefreshToken;
            user.RefreshTokenExpiryTime = tokenResponse.RefreshTokenExpiresAt;
            await _userRepository.UpdateAsync(user);

            LogService.Info("刷新令牌成功");
            return LoginResult.Success(tokenResponse);
        }

        public async Task<bool> ChangePasswordAsync(int userId, string oldPassword, string newPassword)
        {
            LogService.Info($"用户 ID: {userId} 尝试修改密码");

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                LogService.Warn($"修改密码失败：用户ID {userId} 不存在");
                return false;
            }

            var oldPasswordHash = ComputePasswordHash(oldPassword);
            if (user.PasswordHash != oldPasswordHash)
            {
                LogService.Warn($"修改密码失败：用户ID {userId} 旧密码不匹配");
                return false;
            }

            user.PasswordHash = ComputePasswordHash(newPassword);
            // 更改密码时使现有的刷新令牌失效
            user.RefreshToken = null;
            user.RefreshTokenExpiryTime = DateTime.MinValue;

            await _userRepository.UpdateAsync(user);
            LogService.Info($"用户 ID: {userId} 密码修改成功");
            return true;
        }

        private string ComputePasswordHash(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var bytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                var builder = new StringBuilder();
                foreach (var b in bytes)
                {
                    builder.Append(b.ToString("x2"));
                }
                return builder.ToString();
            }
        }

        private UserDto MapToUserDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                Name = user.UserName,
                UserName = user.UserName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                IsActive = user.IsActive,
                CreatedDate = user.CreatedDate,
                LastLoginDate = user.LastLoginDate,
                Roles = user.Roles?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>()
            };
        }
    }
}
