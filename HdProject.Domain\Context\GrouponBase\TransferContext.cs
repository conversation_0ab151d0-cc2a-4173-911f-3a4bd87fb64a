﻿using Azure;
using HdProject.Domain.Result.Page;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Context.GrouponBase
{
    public class TransferContext
    {

    }
    public class GetTransferCardContext
    {
        public string openid { get; set; }
        public int IsGive { get; set; }//是否可赠送
        public int CodeStatus { get; set; }
        public Pagination Paging { get; set; }//分页
       
    }
    public class GetNoSharedCardListContext
    {
        public string openid { get; set; }
        public int IsGive { get; set; }//是否可赠送
    
        public Pagination Paging { get; set; }//分页
       
    }
    public class TransferCardContext
    {
        public string CodeKey { get; set; }
        public string openid { get; set; }


    }
    public class ClaimContext {
        public string ClaimKey { get; set; }
        public string openid { get; set; }
    }
    public class GetClaimKeyListContext
    {
        public string ClaimKey { get; set; }
       

    }
    //public class GetNoSharedCardContext
    //{
    //    public string openid { get; set; }
    //    public int IsGive { get; set; }//是否可赠送
    //    public int CodeStatus { get; set; }
      

    //}

}
