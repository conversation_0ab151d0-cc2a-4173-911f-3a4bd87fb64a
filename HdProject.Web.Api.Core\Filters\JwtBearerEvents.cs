using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Threading.Tasks;

namespace HdProject.Web.Api.Core.Filters
{
    /// <summary>
    /// 自定义 JwtBearerEvents，用于处理 JWT 认证相关事件
    /// </summary>
    public class CustomJwtBearerEvents : JwtBearerEvents
    {
        /// <summary>
        /// 在令牌验证通过后触发，可以在这里刷新用户信息
        /// </summary>
        /// <param name="context">令牌验证上下文</param>
        /// <returns>任务</returns>
        public override async Task TokenValidated(TokenValidatedContext context)
        {
            // 可以在这里添加逻辑以刷新用户信息
            // 例如，通过依赖注入 JwtService 来调用 RefreshClaims 方法
            // var jwtService = context.HttpContext.RequestServices.GetService<IJwtService>();
            // var refreshedPrincipal = await jwtService.RefreshClaims(context.Principal);
            // if (refreshedPrincipal != null)
            // {
            //     context.Principal = refreshedPrincipal;
            // }
            // else
            // {
            //     context.Fail("用户信息刷新失败");
            // }

            await base.TokenValidated(context);
        }

        /// <summary>
        /// 在认证失败时触发，可以在这里处理令牌过期等情况
        /// </summary>
        /// <param name="context">认证失败上下文</param>
        /// <returns>任务</returns>
        public override async Task AuthenticationFailed(AuthenticationFailedContext context)
        {
            if (context.Exception is SecurityTokenExpiredException)
            {
                context.Response.Headers.Add("Token-Expired", "true");
            }

            await base.AuthenticationFailed(context);
        }
    }
}