﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Entities.GroupBase
{
    public class NCodeRefund
    {

        [SugarColumn(IsPrimaryKey = true)]
        public Guid? Ikey { get; set; }
        public string ApplyNo { get; set; }
        public int ApplyShopId { get; set; } = 0;
        public string ApplyUserID { get; set; }
        public string ApplyName { get; set; }
        public DateTime? ApplyTime { get; set; }
        public string ApplyReason { get; set; }
        public string ConfirmName { get; set; }
        public DateTime? ConfirmTime { get; set; }
        public string ConfirmNo { get; set; }
        public string CheckName { get; set; }
        public bool IsEnd { get; set; } = false;
        public decimal ApplyMoney { get; set; } = 0;
        public int ApplyNum { get; set; } = 0;
        public string InEx { get; set; }
        public string CallBackEx { get; set; }
    }
}
