﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplate;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicITemplate;

namespace HdProject.Application.Services.Interfaces.SongBase.WallPainting
{
   /// <summary>
   /// 主题模板明细
   /// </summary>
    public interface IWPTopicITemplateService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicITemplateGetByIdResponseDto> GetByIdAsync(WPTopicITemplateGetByIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息(不带分页)
        /// </summary>
        /// <returns></returns>
        Task<WPTopicITemplateGetAllResponseDto> GetAll();
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<WPTopicITemplateGetAllResponseDto> GetAllAsync(WPTopicITemplateGetAllRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicITemplateAddResponseDto> AddAsync(WPTopicITemplateAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicITemplateUpdateResponseDto> UpdateAsync(WPTopicITemplateUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicITemplateDeleteResponseDto> DeleteAsync(WPTopicITemplateDeleteRequestDto requestDto);
    }
}
