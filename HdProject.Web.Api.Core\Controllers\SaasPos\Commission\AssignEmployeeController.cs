﻿using HdProject.Application.Services.Interfaces.SaasPos.Commission;
using HdProject.Application.Services.SaasPos.Commission;
using HdProject.Domain.Context.SaasPos.Commission.AssignEmployee;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.Commission
{
    public class AssignEmployeeController: PublicControllerBase
    {
        private readonly IAssignEmployeeService _assignEmployeeService;
        private readonly ILogger<AssignEmployeeController> _logger;

        public AssignEmployeeController(IAssignEmployeeService assignEmployeeService, ILogger<AssignEmployeeController> logger)
        {
            _assignEmployeeService = assignEmployeeService;
            _logger = logger;
        }

        /// <summary>
        /// 查询员工列表的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetByRoomAssignEmployee([FromQuery] AssignEmployeeGetAllRequestDto request)
        {
            var result = await _assignEmployeeService.GetAllAsync(request);
            return ApiData(result.Model.wechat[0]);
        }
    }
}
