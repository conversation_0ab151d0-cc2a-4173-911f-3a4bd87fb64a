2025-08-13 16:34:42.4522|ERROR|Microsoft.Extensions.Hosting.Internal.Host|Hosting failed to start [Microsoft.Extensions.Hosting.Internal.HostingLoggerExtensions.HostedServiceStartupFaulted:0]
System.IO.IOException: Failed to bind to address http://127.0.0.1:5005: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation) 
2025-08-13 16:35:32.6430|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/swagger?id=e70fed0d-ce5f-44db-b4ca-9deaad38b0df&vscodeBrowserReqId=1755074132605 - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:35:32.7221|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/swagger?id=e70fed0d-ce5f-44db-b4ca-9deaad38b0df&vscodeBrowserReqId=1755074132605 - 404 0 - 75.1358ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:35:32.7221|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5079/swagger, Response status code: 404 [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestUnhandled:0] 
2025-08-13 16:35:44.3400|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/robots.txt - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:35:44.3533|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/robots.txt - 404 0 - 13.2143ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:35:44.3533|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5079/robots.txt, Response status code: 404 [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestUnhandled:0] 
2025-08-13 16:35:46.2570|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:35:46.4773|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - 200 - application/json;charset=utf-8 220.3307ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:35:49.5412|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/swagger - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:35:49.5412|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/swagger - 404 0 - 4.8172ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:35:49.5474|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5079/swagger, Response status code: 404 [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestUnhandled:0] 
2025-08-13 16:35:56.3652|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/robots.txt - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:35:56.3652|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/robots.txt - 404 0 - 3.2679ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:35:56.3652|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5079/robots.txt, Response status code: 404 [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestUnhandled:0] 
2025-08-13 16:35:58.2477|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:35:58.2750|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - 200 - application/json;charset=utf-8 27.2268ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:36:04.2967|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/robots.txt - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:36:04.2967|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/robots.txt - 404 0 - 2.4202ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:36:04.2967|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5079/robots.txt, Response status code: 404 [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestUnhandled:0] 
2025-08-13 16:36:06.1873|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:36:06.2129|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - 200 - application/json;charset=utf-8 25.5423ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:36:11.2829|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/robots.txt - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:36:11.2829|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/robots.txt - 404 0 - 5.4351ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:36:11.2829|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5079/robots.txt, Response status code: 404 [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestUnhandled:0] 
2025-08-13 16:36:13.1773|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:36:13.1928|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - 200 - application/json;charset=utf-8 15.4631ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:36:21.1776|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/robots.txt - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:36:21.1776|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/robots.txt - 404 0 - 2.0062ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
2025-08-13 16:36:21.1776|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5079/robots.txt, Response status code: 404 [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestUnhandled:0] 
2025-08-13 16:36:23.0924|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - - - [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestStarting:0] 
2025-08-13 16:36:23.1084|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5079/swagger/v1/swagger.json - 200 - application/json;charset=utf-8 16.0466ms [Microsoft.AspNetCore.Hosting.HostingApplicationDiagnostics.LogRequestFinished:0] 
