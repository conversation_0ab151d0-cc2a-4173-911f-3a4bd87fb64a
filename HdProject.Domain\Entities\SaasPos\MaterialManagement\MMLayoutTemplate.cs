﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MaterialManagement
{
    /// <summary>
    /// 布局模板表
    /// </summary>
    [SugarTable("MM_LayoutTemplate")]
    public class MMLayoutTemplate
    {
        /// <summary>
        ///布局编号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int LayoutID { get; set; }
        /// <summary>
        /// 布局名称
        /// </summary>
        public string LayoutName { get; set; }
        /// <summary>
        /// 布局描述
        /// </summary>
        public string? LayoutDescription { get; set; }
        /// <summary>
        /// 布局行数
        /// </summary>
        public int? LayoutRows { get; set; }
        /// <summary>
        /// 布局列数
        /// </summary>
        public int? LayoutCols { get; set; }
        /// <summary>
        /// 布局模版格数
        /// </summary>
        public int? TemplateGridCount { get; set; }
        public bool IsActive { get; set; }
        public string? DisabledBy { get; set; }
        public DateTime? DisabledTime { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedTime { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedTime { get; set; }
        /// <summary>
        /// 区域实体集合
        /// </summary>
        /// 
        [Navigate(NavigateType.OneToMany, nameof(MMLayoutRegion.LayoutID))]
        public List<MMLayoutRegion> RegionList { get; set; }
    }
    /// <summary>
    /// 布局区域表
    /// </summary>
    [SugarTable("MM_LayoutRegion")]
    public class MMLayoutRegion
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int RegionID { get; set; }
        public int LayoutID { get; set; }
        public string? RegionName { get; set; }
        public int? StartX { get; set; }
        public int? StartY { get; set; }
        public int? RegionWidth { get; set; }
        public int? RegionHeight { get; set; }
        public string? CreatedBy { get; set; }
        public string? HtmlTemplate { get; set; }
        /// <summary>
        /// 节目单集合
        /// </summary>
        /// 
        [Navigate(NavigateType.OneToMany, nameof(MMPlaylistDetail.RegionID))]
        public List<MMPlaylistDetail>  mMPlaylists { get; set; }
    }
}
