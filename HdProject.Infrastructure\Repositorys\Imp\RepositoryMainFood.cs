﻿using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Infrastructure.Repositories.Imp
{
    public class RepositoryMainFood<T> : Repository<T>, IRepositoryMainFood<T>
        where T : class, new()
    {
        public RepositoryMainFood(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "MainFood";
        }
    }
}
