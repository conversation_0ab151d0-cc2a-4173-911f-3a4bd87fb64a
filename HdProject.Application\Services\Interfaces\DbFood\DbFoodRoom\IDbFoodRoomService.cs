﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.DbFood.Room;
using HdProject.Domain.DTOs.DbFood;

namespace HdProject.Application.Services.Interfaces.DbFood.DbFoodRoom
{
    /// <summary>
    /// 房间
    /// </summary>
    public interface IDbFoodRoomService
    {
        /// <summary>
        /// 查询全部区域房间消费人数统计信息
        /// </summary>
        /// <returns></returns>
        Task<DbFoodRoomGetAllResponseDto> GetAllAsync(DbFoodRoomGetAllRequestDto requestDto);

        /// <summary>
        /// 查询门店全部区域信息
        /// </summary>
        /// <returns></returns>
        Task<DbFoodRoomRegionGetAllResponseDto> GetAllRegionAsync(DbFoodRoomRegionGetAllRequestDto requestDto);
    }
}
