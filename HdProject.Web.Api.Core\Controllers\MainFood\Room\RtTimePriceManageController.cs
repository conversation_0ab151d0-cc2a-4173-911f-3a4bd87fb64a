﻿using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.MainFood.Room
{
    /// <summary>
    /// 房间价格接口控制器
    /// </summary>
    public class RtTimePriceManageController : PublicControllerBase
    {
        private readonly IRtTimePriceService _rtTimePriceService;
        private readonly ILogger<RtTimePriceManageController> _logger;
        public RtTimePriceManageController(IRtTimePriceService rtTimePricePriceService, ILogger<RtTimePriceManageController> logger)
        {
            _rtTimePriceService = rtTimePricePriceService;
            _logger = logger;
        }
        /// <summary>
        /// 查询房型全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllRtTimePrice([FromQuery] RtTimePriceGetAllAsyncRequestDto request)
        {
            var result = await _rtTimePriceService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询房型信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdRtTimePrice([FromQuery] RtTimePriceGetByIdAsyncRequestDto request)
        {
            var result = await _rtTimePriceService.GetByIdAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 新增房型信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> RtTimePriceAddValue([FromBody] RtTimePriceAddRequestDto request)
        {
            var result = await _rtTimePriceService.AddAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 修改房型信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> RtTimePriceUpdateValue([FromBody] RtTimePriceUpdateRequestDto request)
        {
            var result = await _rtTimePriceService.UpdateAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 删除房型信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> RtTimePriceDeleteValue([FromBody] RtTimePriceDeleteRequestDto request)
        {
            var result = await _rtTimePriceService.DeleteAsync(request);
            return ApiData(result);
        }
    }
}
