﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;

namespace HdProject.Infrastructure.Repositorys.Imp
{
    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class RepositoryRms2009<T> : Repository<T>, IRepositoryRms2009<T>
        where T : class, new()
    {
        public RepositoryRms2009(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "Rms2009";
        }
    }
}
