﻿using HdProject.Application.Services.Interfaces.SaasPos.SongSyncCenter;
using HdProject.Application.Services.Interfaces.SaasPos;
using HdProject.Domain.Context;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using HdProject.Domain.Context.SongManagement;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.Song
{
    //歌曲接口
    [Route("[controller]/[Action]")]
    public class SongController : PublicControllerBase
    {
        private readonly ISongService _songService;


        public SongController(ISongService songService)
            => _songService = songService;
        /// <summary>
        /// 单曲录入
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SingleSongEntry(SingleSongEntryContext context)
        {
            var res = await _songService.SingleSongEntry(context);
            return ApiData(res);
        }
        /// <summary>
        /// 批量导入歌曲
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> BatchImportSong(SongContext context)
        {
            var res = await _songService.BatchImportSong(context);
            return ApiData(res);
        }
        /// <summary>
        /// 歌曲查询
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> SongQuery([FromQuery] SongContext context)
        {
            var res = await _songService.SongQuery(context);
            return ApiData(res);
        }
        /// <summary>
        /// 歌曲编辑
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> SongEditing(SongContext context) 
        {
            var res = await _songService.SongEditing(context);
            return ApiData(res);
        }
        /// <summary>
        /// 歌曲删除(软删除）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> SongDeletion(SongContext context)
        {
            var res = await _songService.SongDeletion(context);
            return ApiData(res);
        }
    }
}
