using SqlSugar;

namespace HdProject.Domain.Entities.GroupBase
{
    [SugarTable("Dim_Bank_Deal")]
    public class Dim_Bank_Deal
    {
        [SugarColumn(IsPrimaryKey = true)]
        public int DealSK { get; set; }
        public int BankSK { get; set; }
        [SugarColumn(Length = 50)]
        public string FdNo { get; set; } = string.Empty;
        [SugarColumn(Length = 500)]
        public string DealName { get; set; } = string.Empty;
        [SugarColumn(ColumnDataType = "decimal(18,2)")]
        public decimal DealAmount { get; set; }
        [SugarColumn(ColumnDataType = "decimal(18,2)")]
        public decimal SubsidyAmount { get; set; }
        [SugarColumn(ColumnDataType = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        [SugarColumn(ColumnDataType = "decimal(18,2)")]
        public decimal ServiceFee { get; set; }
        [SugarColumn(ColumnDataType = "decimal(18,2)")]
        public decimal NetAmount { get; set; }
    }
}
