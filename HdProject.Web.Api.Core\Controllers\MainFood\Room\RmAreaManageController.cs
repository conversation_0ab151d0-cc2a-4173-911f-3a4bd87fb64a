﻿using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.MainFood.Room
{
    /// <summary>
    /// 房间区域区域接口控制器
    /// </summary>
    public class RmAreaManageController : PublicControllerBase
    {
        private readonly IRmAreaService _rmAreaService;
        private readonly ILogger<RmAreaManageController> _logger;
        public RmAreaManageController(IRmAreaService rmAreaService, ILogger<RmAreaManageController> logger)
        {
            _rmAreaService = rmAreaService;
            _logger = logger;
        }
        /// <summary>
        /// 查询房间区域全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllRmArea([FromQuery] RmAreaGetAllAsyncRequestDto request)
        {
            var result = await _rmAreaService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询房间区域信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdRmArea([FromQuery] RmAreaGetByIdAsyncRequestDto request)
        {
            var result = await _rmAreaService.GetByIdAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 新增房间区域信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> RmAreaAddValue([FromBody] RmAreaAddRequestDto request)
        {
            var result = await _rmAreaService.AddAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 修改房间区域信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> RmAreaUpdateValue([FromBody] RmAreaUpdateRequestDto request)
        {
            var result = await _rmAreaService.UpdateAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 删除房间区域信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> RmAreaDeleteValue([FromBody] RmAreaDeleteRequestDto request)
        {
            var result = await _rmAreaService.DeleteAsync(request);
            return ApiData(result);
        }
    }
}
