﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Result.Page;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace HdProject.Domain.Context.MarketingActivityManagement
{
    public class MarketingActivityContext
    {
        public class GetSalesAndCardsInfoContext
        {
            public string? ActivityName { get; set; }//活动名称
            public string? CardSheetName { get; set; }//卡卷名称
            public string TypeName { get; set; }//类型名称
            public string? SalesId { get; set; }//活动ID
            public string? CardSheetId { get; set; }//卡卷ID

            public Pagination Paging { get; set; }


        }
        public class DeleteSalesAndCardsInfoContext
        {
            public string? SalesId { get; set; }//活动ID
            public string? CardSheetId { get; set; }//卡卷ID
        }
        public class EditorSalesAndCardsInfoContext
        {
           //json字符串
            public string Data { get; set; }//data
         
            public IFormFile? File { get; set; } // 添加文件属性
        }
        
        public class ApiResponse
        {
            /// <summary>
            /// 操作是否成功
            /// </summary>
            public bool Success { get; set; }

            /// <summary>
            /// 消息
            /// </summary>
            public string Message { get; set; }

            /// <summary>
            /// 返回数据
            /// </summary>
            public object Data { get; set; }
        }

        public class GetCardSheetListContext
        {
            public string? CardSheetListName { get; set; }//卡卷名称
            public Guid? CardSheetListId { get; set; }//卡卷id
            public Pagination Paging { get; set; }
        }
        public class CloudUploadResult
        {
            public bool IsSuccess { get; set; }
            public string FileId { get; set; }
            public string PublicUrl { get; set; }
            public string ETag { get; set; }
            public string CosRequestId { get; set; }
            public string ErrorMessage { get; set; }
        }
        public class GetWXQrCodeContext
        {
            public string Path { get; set; }
        }
       
    }
}
