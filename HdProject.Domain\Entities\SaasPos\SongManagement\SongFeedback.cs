﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.SongManagement
{
    /// <summary>
    /// 缺歌登记
    /// </summary>
    [SugarTable("User_Feedback_Song")]
    public class SongFeedback
    {
        [SugarColumn(ColumnName = "FBID", IsPrimaryKey = true, IsIdentity = true)]
        public int FbId { get; set; }
        public string SongName { get; set; }
        public string SingerName { get; set; }
        public string? SongVersion { get; set; }
        public DateTime? CreateTime { get; set; }
        public string? Remark { get; set; }
        public string? ContactWay { get; set; }
        public string? FeedbackByID { get; set; }  
    }
}
