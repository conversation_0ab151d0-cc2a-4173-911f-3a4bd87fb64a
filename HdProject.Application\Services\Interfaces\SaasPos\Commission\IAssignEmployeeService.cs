﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.Commission.AssignEmployee;
using HdProject.Domain.DTOs.SaasPos.Commission.AssignEmployee;

namespace HdProject.Application.Services.Interfaces.SaasPos.Commission
{
    public interface IAssignEmployeeService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<AssignEmployeeGetByIdResponseDto> GetByIdAsync(AssignEmployeeGetByIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<AssignEmployeeGetAllResponseDto> GetAllAsync(AssignEmployeeGetAllRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<AssignEmployeeAddResponseDto> AddAsync(AssignEmployeeAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<AssignEmployeeUpdateResponseDto> UpdateAsync(AssignEmployeeUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<AssignEmployeeDeleteResponseDto> DeleteAsync(AssignEmployeeDeleteRequestDto requestDto);
    }
}
