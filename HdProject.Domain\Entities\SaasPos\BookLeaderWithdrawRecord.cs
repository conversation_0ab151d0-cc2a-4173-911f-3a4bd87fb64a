﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos
{
    public partial class BookLeaderWithdrawRecord
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string RecordId { get; set; }
        public System.Guid LeaderId { get; set; }
        public string Title { get; set; }
        public System.DateTime WithdrawTime { get; set; }
        public byte Status { get; set; }
        public decimal Amount { get; set; }
        public string BankCard { get; set; }
        public string Remark { get; set; }
        public string OperatorId { get; set; }
        public Nullable<System.DateTime> OperateTime { get; set; }
        public bool IsDeleted { get; set; }
        public Nullable<System.DateTime> CreateTime { get; set; }
       
        public Nullable<System.DateTime> UpdateTime { get; set; }
        public Nullable<System.DateTime> DeleteTime { get; set; }
        public string DeletedBy { get; set; }

       
    }
}
