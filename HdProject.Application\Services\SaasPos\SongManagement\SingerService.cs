﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SaasPos.SongSyncCenter;
using HdProject.Domain.Context;
using HdProject.Domain.Context.SongManagement;
using HdProject.Domain.DTOs;
using HdProject.Domain.DTOs.SongManagement;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Entities.SaasPos.SongSyncCenter;
using HdProject.Domain.Interfaces;

namespace HdProject.Application.Services.SaasPos.SongManagement
{
    public class SingerService : ISingerService
    {
        //私有字段（依赖的仓储接口），每个仓储对应一个数据库表
        private readonly IRepositorySaas<SingInfo> _singInfoRepository;
        private readonly IRepositorySaas<SingerSong> _singerSongRepository;

        public SingerService(IRepositorySaas<SingInfo> singInfoRepository
            , IRepositorySaas<SingerSong> singerSongRepository

            )
        {
            _singInfoRepository = singInfoRepository;//歌星信息表
            _singerSongRepository = singerSongRepository;//歌星与歌曲关联表
        }
        
        
        /// <summary>
        /// 新增歌星
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<SingerDto> NewSingerAdded(SingerContext context)
        {
            throw new NotImplementedException();
        }
        /// <summary>
        /// 歌星列表详细信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<SingerDto> SingerlistInfo(SingerContext context)
        {
            throw new NotImplementedException();
        }
        /// <summary>
        /// 歌星信息更新
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<SingerDto> SingerInfoUpdate(SingerContext context)
        {
            throw new NotImplementedException();
        }
        /// <summary>
        /// 歌星删除（软删除）
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<SingerDto> DeleteSinger(SingerContext context)
        {
            throw new NotImplementedException();
        }
        /// <summary>
        /// 获取歌星相关歌曲
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<SingerDto> GetSingerSong(SingerContext context)
        {
            throw new NotImplementedException();
        }
    }
}
