﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="WebApi\ApiResult.cs" />
  </ItemGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
	  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	  <PackageReference Include="SqlSugarCore" Version="5.1.4.188" />

  </ItemGroup>

  <ItemGroup>
    <Folder Include="Context\OperateData\" />
    <Folder Include="DTOs\OperateData\" />
  </ItemGroup>

</Project>
