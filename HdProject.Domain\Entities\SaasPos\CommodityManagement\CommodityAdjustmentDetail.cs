﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.CommodityManagement
{
    // 商品调整详情表（CommodityAdjustmentDetail）

    public partial class CommodityAdjustmentDetail
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public Guid DetailID { get; set; }
        public Guid AdjustmentID { get; set; }
        public byte Type { get; set; }//单品/套餐
        public string? FdNo { get; set; }
        public string? FtNo { get; set; }//类别号
        public string? ComboNo { get; set; }
        public string? FdCName { get; set; }
        public string? ComboName { get; set; }
        public decimal MarketPrice { get; set; }
        public decimal SalePrice { get; set; }
        public string PriceMode { get; set; }
        public string ApplicableStores { get; set; }
        [SugarColumn(DefaultValue = "1")]
        public int FdQty { get; set; }
        public DateTime CreateTime { get; set; }
        [SugarColumn(DefaultValue = "0")]
        public bool IsDeleted { get; set; }
        public DateTime? DeleteTime { get; set; }
        //操作类型
        public int? DetailActionType { get; set; }
        public byte Status { get; set; }

    }
}
