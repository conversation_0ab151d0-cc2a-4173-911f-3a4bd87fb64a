﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SongBase.WallPainting
{
    /// <summary>
    /// 壁画素材表
    /// </summary>
    [SugarTable("WP_TopicIFile")] 
    public class WPTopicIFile
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int FileID { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string? FormatType { get; set; }
        public long? FileSize { get; set; }
        public string? UploadedBy { get; set; }
        public DateTime? UploadedTime { get; set; }
        public string? ThumbnailPath { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public bool IsActive { get; set; }
    }
}
