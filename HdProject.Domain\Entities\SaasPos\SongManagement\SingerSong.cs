﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.SongSyncCenter
{
    //歌星与歌曲关联表
    public partial class SingerSong
    {
        [SugarColumn(IsPrimaryKey = true)]
        public int Id { get; set; }
        /// <summary>
        /// 歌星ID
        /// </summary>
        public int SingId { get; set; }
        /// <summary>
        /// 歌曲ID
        /// </summary>
        public int SongId { get; set; }
    }
}
