﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.Rms
{
    public class shopinfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public int ShopId { get; set; } = 0;

        public string ShopName { get; set; }

        public string ShopAddress { get; set; }

      
        public string ShopTel { get; set; }

        public int ShopOrder { get; set; } = 0;

        public int ShopAreaNo { get; set; } = 0;

        public int ShopArea_x { get; set; } = 0;

        public int ShopArea_y { get; set; } = 0;

        public string Route { get; set; }

        public bool IsDel { get; set; } = false;
    }
}
