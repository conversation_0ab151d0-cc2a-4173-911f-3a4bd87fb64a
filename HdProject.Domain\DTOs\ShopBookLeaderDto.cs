﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.DTOs
{
    internal class ShopBookLeaderDto
    {
    }
    // 定义新的 DTO 类
    public class LeaderInfoWithCommissionDto
    {
        // 团长基础信息
        /// <summary>
        /// 团长码
        /// </summary>
        public string LeaderCode { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        // 佣金信息
        /// <summary>
        /// 累计佣金
        /// </summary>
        public decimal TotalAmount { get; set; }
        /// <summary>
        /// 已提现金额
        /// </summary>
        public decimal WithdrawnAmount { get; set; }
        /// <summary>
        /// 可提现金额
        /// </summary>
        public decimal AvailableAmount { get; set; }
    }
    // 自定义的 DTO 类，用于传输需要的字段
    public class AppointmentRecordDto
    {
        /// <summary>
        /// 主标题
        /// </summary>
        public string Body { get; set; }
        /// <summary>
        /// 预约状态
        /// </summary>
        public int BookStatus { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 预约到达日期
        /// </summary>
        public string ComeDate { get; set; }
        /// <summary>
        /// 预约到达时间
        /// </summary>
        public string ComeTime { get; set; }
    }
    public class LeaderInfoStatusDto
    {
        /// <summary>
        /// 团长id
        /// </summary>
        public System.Guid LeaderId { get; set; }
        /// <summary>
        /// 返回状态
        /// </summary>
        public string Status { get; set; }
    }
    // 汇总报表接口对应的模型类
    public class LeaderSummaryReportDto
    {
        public int OrderTotal { get; set; } // 订单总数
        public decimal OrderTotalAmount { get; set; } // 订单总额
        public int LeaderTotal { get; set; } // 团长总数
        public int SellingLeaderCount { get; set; } // 产生销售团长数
    }

    // 团长信息接口对应的模型类
    public class LeaderInfoDto
    {
        public string LeaderSerialNumber { get; set; } // 团长序号
        public string LeaderName { get; set; } = string.Empty; // 团长姓名
        public string LeaderPhone { get; set; } = string.Empty; // 团长电话
        public DateTime? RegistrationTime { get; set; } // 注册时间
        public int PosterViewCount { get; set; } // 海报浏览量
        public int PosterOrderTotal { get; set; } // 海报下单总量
        public decimal PosterOrderTotalAmount { get; set; } // 海报下单总额
        public int ConsumptionCount { get; set; } // 消费数量
        public decimal ConsumptionAmount { get; set; } // 消费金额
    }

    // 订单信息接口对应的模型类
    public class LeaderOrderInfoDto
    {
        public DateTime? OrderTime { get; set; } // 下单时间
        public DateTime? ConsumptionTime { get; set; } // 消费时间，可为空
        public string OrderNumber { get; set; } = string.Empty; // 订单编号
        public string OrderInfo { get; set; } = string.Empty; // 订单信息
        public decimal OrderAmount { get; set; } // 订单金额
        public string OrderStatus { get; set; } = string.Empty; // 订单状态（已消费/退款/未消费）
        public decimal SettlementCommission { get; set; } // 结算佣金
        public string LeaderName { get; set; } = string.Empty; // 团长姓名
        public string LeaderPhone { get; set; } = string.Empty; // 团长电话
        public string SettlementStatus { get; set; } = string.Empty; // 结算状态（已结算/未结算）
        public string LeaderBankCardNumber { get; set; } = string.Empty; // 团长银行卡账号
    }

    // 佣金计提接口对应的模型类
    public class LeaderCommissionAccrualDto
    {
        public string LeaderName { get; set; } = string.Empty; // 团长姓名
        public string LeaderPhone { get; set; } = string.Empty; // 团长电话
        public DateTime? ConsumptionTime { get; set; } // 消费时间
        public string SettlementOrderNumber { get; set; } = string.Empty; // 结算订单编号
        public string SettlementOrderInfo { get; set; } = string.Empty; // 结算订单信息
        public decimal SettlementOrderAmount { get; set; } // 结算订单金额
        public string SettlementStatus { get; set; } = string.Empty; // 结算状态（已结算/未结算）
        public string LeaderBankCardNumber { get; set; } = string.Empty; // 团长银行卡号
    }

}
