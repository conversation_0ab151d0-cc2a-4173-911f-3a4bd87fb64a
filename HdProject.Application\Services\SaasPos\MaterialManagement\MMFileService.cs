﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Furion.DatabaseAccessor;
using HdProject.Application.Services.Interfaces.SaasPos.MaterialManagement;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.DTOs.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.VisualBasic.FileIO;
using SixLabors.ImageSharp;
using SqlSugar;

namespace HdProject.Application.Services.SaasPos.MaterialManagement
{
    /// <summary>
    /// 素材接口实现类
    /// </summary>
    public class MMFileService : IMMFileService
    {
        private readonly IRepositorySaas<MMFile> _repositoryMMFile;
        private readonly IMapper _mapper;
        //private readonly IWebHostEnvironment _env;
        //private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient _sqlSugarClient;
        public MMFileService(IRepositorySaas<MMFile> repositoryMMFile,
            IMapper mapper, ISqlSugarClient sqlSugarClient)
        {
            _repositoryMMFile = repositoryMMFile;
            _mapper = mapper;
            _sqlSugarClient = sqlSugarClient;
        }

        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }

        /// <summary>
        /// 上传素材
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMFileUploadResponseDto> UploadAsync(MMFileUploadRequestDto requestDto)
        {
            var responseDto = new MMFileUploadResponseDto();
            // 保存到数据库
            var FileList = new List<MMFile>();
            foreach (var item in requestDto.Model)
            {
                var model = new MMFile()
                {
                    FileName = item.FileName,
                    FilePath = item.FilePath,
                    FormatType = item.FormatType,
                    FileSize = item.FileSize,
                    UploadedBy = "张三",
                    UploadedTime = DateTime.Now,
                    Width = item.Width,
                    Height = item.Height,
                    VideoDuration = item.VideoDuration,
                    ThumbnailPath = item.ThumbnailPath,
                };
                FileList.Add(model);
            }
            try
            {
                _repositoryMMFile.BeginTran();
                var index = await _repositoryMMFile.InsertRangeAsync(FileList);
                _repositoryMMFile.CommitTran();
                responseDto.Index = index;
            }
            catch (Exception ex)
            {
                _repositoryMMFile.RollbackTran();
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 删除素材
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMFileDeleteResponseDto> DeleteAsync(MMFileDeleteRequestDto requestDto)
        {
            MMFileDeleteResponseDto responseDto = new MMFileDeleteResponseDto();
            try
            {
                var resultModel = await _repositoryMMFile.GetFirstAsync(a => a.FileID == requestDto.FileID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法删除，请检查该素材是否存在！");
                }
                resultModel.IsActive = true;
                resultModel.DisabledBy = "张三";
                resultModel.DisabledTime = DateTime.Now;
                var result = await _repositoryMMFile.UpdateAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>

        public async Task<MMFileGetAllAsyncResponseDto> GetAllAsync(MMFileGetAllAsyncRequestDto requestDto)
        {
            MMFileGetAllAsyncResponseDto responseDto = new MMFileGetAllAsyncResponseDto();
            try
            {
                //var predicate = PredicateBuilder.New<MMFile>(true);
                //predicate = predicate.And(it => it.IsActive == false);//过滤非禁用的数据
                //                                                      // 动态添加条件
                //if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
                //{
                //    predicate = predicate.And(it => it.FileName.Contains(requestDto.QueryCriteria));
                //}
                //var result = await _repositoryMMFile.GetPageListAsync(requestDto, predicate);
                var result = await _repositoryMMFile.GetListAsync();
                var model = _mapper.Map<List<MMFileDto>>(result.OrderByDescending(it => it.FileID));//使用AutoMapper进行对象属性映射
                responseDto.Model = model;
            }
            catch (Exception ex)
            {
                throw new Exception("操作执行过程中出错：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMFileGetByIdAsyncResponseDto> GetByIdAsync(MMFileGetByIdAsyncRequestDto requestDto)
        {
            MMFileGetByIdAsyncResponseDto responseDto = new MMFileGetByIdAsyncResponseDto();
            try
            {
                //var result = await _repositoryMMFile.GetByIdAsync(requestDto.FileID);//因为根据ID查询时，需要增加条件，所以GetByIdAsync在此处弃用。
                var result = await _repositoryMMFile.GetFirstAsync(a => a.FileID == requestDto.FileID && a.IsActive == false);
                if (result != null)
                {
                    var model = _mapper.Map<MMFileDto>(result);//使用AutoMapper进行对象属性映射
                    responseDto.Model = model;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;

        }
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MMFileUpdateResponseDto> UpdateAsync(MMFileUpdateRequestDto requestDto)
        {
            MMFileUpdateResponseDto responseDto = new MMFileUpdateResponseDto();
            try
            {
                //var resultModel = await _repositoryMMFile.GetByIdAsync(requestDto.Model.FileID);
                var resultModel = await _repositoryMMFile.GetFirstAsync(a => a.FileID == requestDto.Model.FileID && a.IsActive == false);
                if (resultModel == null)
                {
                    throw new Exception("无法查询到任何素材信息！");
                }
                var model = _mapper.Map<MMFile>(requestDto.Model);
                model.UploadedBy = resultModel.UploadedBy;
                model.UploadedTime = resultModel.UploadedTime;
                model.CreatedBy = resultModel.CreatedBy;
                model.CreatedTime = resultModel.CreatedTime;
                model.DisabledBy = resultModel.DisabledBy;
                model.DisabledTime = resultModel.DisabledTime;
                model.ModifiedBy = "张三";
                model.ModifiedTime = DateTime.Now;
                var result = await _repositoryMMFile.UpdateAsync(model);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }
    }
}
