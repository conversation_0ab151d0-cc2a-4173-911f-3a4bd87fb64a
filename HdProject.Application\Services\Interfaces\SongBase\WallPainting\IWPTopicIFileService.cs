﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIFile;
using HdProject.Domain.DTOs.SongBase.WallPainting.WPTopicIFile;

namespace HdProject.Application.Services.Interfaces.SongBase.WallPainting
{
    /// <summary>
    /// 主题素材接口
    /// </summary>
    public interface IWPTopicIFileService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicIFileGetByIdResponseDto> GetByIdAsync(WPTopicIFileGetByIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<WPTopicIFileGetAllResponseDto> GetAllAsync(WPTopicIFileGetAllRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicIFileAddResponseDto> AddAsync(WPTopicIFileAddRequestDto requestDto);
        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<WPTopicIFileUpdateResponseDto> UpdateAsync(WPTopicIFileUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<WPTopicIFileDeleteResponseDto> DeleteAsync(WPTopicIFileDeleteRequestDto requestDto);
    }
}
