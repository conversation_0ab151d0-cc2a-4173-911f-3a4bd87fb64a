﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SongBase.WallPainting
{
    /// <summary>
    /// 模板表
    /// </summary>
    /// 
    [SugarTable("WP_TopicITemplate")]
    public class WPTopicITemplate
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int TemplateID { get; set; }
        public string TemplateName { get; set; }
        public int? TSupportFileNum { get; set; }
        public int FileID { get; set; }
        public bool IsActive { get; set; }

        // 导航属性
        [SugarColumn(IsIgnore = true)]//特性标记,表示在数据库表中没有该的列
        public WPTopicIFile wPTopicIFile { get; set; }
    }
}
