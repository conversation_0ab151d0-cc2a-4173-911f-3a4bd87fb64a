﻿using HdProject.Application.Services.Interfaces.MainFood;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.MainFood.Room
{
    /// <summary>
    /// 房间配送接口控制器
    /// </summary>
    public class RtAutoManageController : PublicControllerBase
    {
        private readonly IRtAutoService _rtAutoService;
        private readonly ILogger<RtAutoManageController> _logger;
        public RtAutoManageController(IRtAutoService rtAutoService, ILogger<RtAutoManageController> logger)
        {
            _rtAutoService = rtAutoService;
            _logger = logger;
        }
        /// <summary>
        /// 查询房间配送全部信息的接口(分页)
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllRtAuto([FromQuery] RtAutoGetAllAsyncRequestDto request)
        {
            var result = await _rtAutoService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }
        /// <summary>
        /// 根据ID查询房间配送信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdRtAuto([FromQuery] RtAutoGetByIdAsyncRequestDto request)
        {
            var result = await _rtAutoService.GetByIdAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 新增房间配送信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> RtAutoAddValue([FromBody] RtAutoAddRequestDto request)
        {
            var result = await _rtAutoService.AddAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 修改房间配送信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> RtAutoUpdateValue([FromBody] RtAutoUpdateRequestDto request)
        {
            var result = await _rtAutoService.UpdateAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 删除房间配送信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> RtAutoDeleteValue([FromBody] RtAutoDeleteRequestDto request)
        {
            var result = await _rtAutoService.DeleteAsync(request);
            return ApiData(result);
        }
    }
}
