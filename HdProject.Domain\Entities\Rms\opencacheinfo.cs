﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.Rms
{
    public class opencacheinfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string Ikey { get; set; }

        public string BookNo { get; set; }
        public int ShopId { get; set; }
        public string? CustKey { get; set; }
        public string CustName { get; set; }
        public string CustTel { get; set; }
        public string ComeDate { get; set; }
        public string ComeTime { get; set; }
        public string Beg_Key { get; set; }
        public string Beg_Name { get; set; }
        public string End_Key { get; set; }
        public string End_Name { get; set; }
        public int Numbers { get; set; }
        public string RtNo { get; set; }
        public string RtName { get; set; }
        public int CtNo { get; set; }
        public string CtName { get; set; }
        public int PtNo { get; set; }
        public string PtName { get; set; }
        public string BookMemory { get; set; }
        public int BookStatus { get; set; }
        public int CheckinStatus { get; set; }
        public int BookShopId { get; set; }
        public string BookUserId { get; set; }
        public string BookUserName { get; set; }
        public DateTime BookDateTime { get; set; }
        public string? Invno { get; set; }
        public string? Openmemory { get; set; }
        public string OrderUserID { get; set; }
        public string OrderUserName { get; set; }
        public string RmNo { get; set; }
        public int Val1 { get; set; }
        public string FromRmNo { get; set; }
        public bool IsBirthday { get; set; }
    }
}
