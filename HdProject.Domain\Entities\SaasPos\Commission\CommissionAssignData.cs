﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.Commission
{
    [SugarTable("Commission_AssignData")]
    public class CommissionAssignData
    {
        [SugarColumn(ColumnName = "AssignDataID", IsPrimaryKey = true, IsIdentity = true)]
        public int AssignDataID { get; set; }
        public int? ShopID { get; set; }
        public string? RmNo { get; set; }
        public string? BillNumber { get; set; }
        public string? BookingAgentID { get; set; }
        public string? BookingAgentName { get; set; }
        public string? OperatorID { get; set; }
        public string? OperatorName { get; set; }
        //public DateTime? CreationDate { get; set; }
    }
}
