﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.MainFood.Room
{
    /// <summary>
    /// 房间区域
    /// </summary>
    [SugarTable("RmArea")]
    public class RmArea
    {
        [SugarColumn(IsPrimaryKey = true, Length = 1)]
        public string AreaNo { get; set; }
        [SugarColumn(Length = 10)]
        public string AreaName { get; set; }
        [SugarColumn(ColumnName = "msrepl_tran_version", IsNullable = false)]
        public Guid MsreplTranVersion { get; set; }

    }
}
