﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SysService;
using HdProject.Common;
using HdProject.Common.SysService;
using HdProject.Domain.Context.SysService;
using HdProject.Domain.DTOs.SysService;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using StackExchange.Redis;

namespace HdProject.Application.Services.SysService
{
    /// <summary>
    /// 叫号系统业务逻辑类
    /// </summary>
    public class QueuingSystemService : IQueuingSystemService
    {
        private readonly IDatabase _redis;//引入Redis
        public QueuingSystemService(IConnectionMultiplexer redis)
        {
            _redis = redis.GetDatabase();
        }
        /// <summary>
        /// 获取设备key和叫号日期
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<CallNumberResponseDto> GenerateNumberAsync(CallNumberRequestDto request)
        {
            if (string.IsNullOrEmpty(request.Key) || string.IsNullOrEmpty(request.Date))
            {
                throw new ArgumentException("设备码与叫号日期不能为空！");
            }

            string dt = "";
            foreach (char d in request.Date)
            {
                if (char.IsDigit(d))
                {
                    dt += d;
                }
            }
            // 验证日期格式
            if (!DateTime.TryParseExact(dt, "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out var callDateTime))
            {
                throw new ArgumentException("叫号日期格式异常，请检查正确格式！");
            }
            var StandardModel = CallNumberStandard.GetByIdStandard(request.TypeId);//根据叫号规则获取规则信息
            if (StandardModel == null)
            {

                throw new ArgumentException("无法查询到规则！");
            }

            var callDate = callDateTime.ToString("yyyy-MM-dd");

            var redisKey = $"CallNumber:{request.Key}:{callDate}:{request.TypeId}";
            // 设置过期时间
            var expiry = Convert.ToDateTime(callDate).AddDays(1) - DateTime.Now;
            long number = StandardModel.StartNumber;
            string stepNumber = "";
            if (!await _redis.KeyExistsAsync(redisKey))
            {
                await _redis.StringSetAsync(redisKey, StandardModel.StartNumber, expiry);
            }
            else
            {
                var numberValue = await _redis.StringGetAsync(redisKey);
                number = StandardModel.LogicalOperation(int.Parse(numberValue));


                if (number.ToString().Length > StandardModel.CallNumberLength)
                {
                    throw new Exception("预生成编码的长度已超出规则长度，无法继续生成!");

                }
                else if (number.ToString().Length < StandardModel.CallNumberLength)
                {
                    stepNumber = number.ToString().PadLeft(StandardModel.CallNumberLength, '0');//往左填充0
                }

                await _redis.StringSetAsync(redisKey, number, expiry);//重新将特殊处理的值，更新到Redis中
            }
            //返回生成编码
            return new CallNumberResponseDto
            {
                Number = number,
                StepNumber = stepNumber
            };
        }





    }
}
