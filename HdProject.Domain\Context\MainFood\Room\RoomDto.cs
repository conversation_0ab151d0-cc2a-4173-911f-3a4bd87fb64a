﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Result.Page;
using SqlSugar;

namespace HdProject.Domain.Context.MainFood.Room
{
    /// <summary>
    /// 房间
    /// </summary>
    public class RoomDto
    {
        public string RmNo { get; set; }

        public string? RmName { get; set; }

        public string RtNo { get; set; }

        public string AreaNo { get; set; }

        public string? InvNo { get; set; }

        public string PriceNo { get; set; }

        public string? WorkDate { get; set; }

        public string? RsPos { get; set; }

        public string RmStatus { get; set; }

        public bool IsSDate { get; set; }

        public string? Rem { get; set; }

        public string? BookDate { get; set; }

        public string? BookTime { get; set; }

        public string? InDate { get; set; }

        public string? InTime { get; set; }

        public short? InNumbers { get; set; }

        public string? OpenUserId { get; set; }

        public string? AccUserId { get; set; }

        public string? AccDate { get; set; }

        public string? AccTime { get; set; }

        public string? ContinueUserId { get; set; }

        public string? ContinueTime { get; set; }

        public string? MemberNo { get; set; }
        public string? CustName { get; set; }
        public string? OrderUserId { get; set; }

        public int DiscRate { get; set; }
        public int? Serv { get; set; }
        public int? FdCost { get; set; }
        public int? RmCost { get; set; }
        public int? Disc { get; set; }
        public int? ZD { get; set; }
        public int? BeerZD { get; set; }
        public int? BeerCash { get; set; }
        public int? Tax { get; set; }
        public int MorePayed { get; set; }
        public int? Tot { get; set; }
        public bool WC { get; set; }
        public bool Dance { get; set; }

        public string PrnFIndex { get; set; }
        public string PrnDIndex { get; set; }

        public short? PInvCount { get; set; }

        [SugarColumn(Length = 4)]
        public string? FromRmNo { get; set; }

        public short? OpenCount { get; set; }
        public bool ForceNoServ { get; set; }
        public int? Tag { get; set; }
        public int FixedDisc { get; set; }

        [SugarColumn(Length = 15)]
        public string? CarId { get; set; }

        public int? FdCost_InRmCost { get; set; }
        public int? FdCost_NotInRmCost { get; set; }
        public bool MembDisc { get; set; }
        public int? MembCard { get; set; }

        [SugarColumn(ColumnName = "Card_MNo", Length = 10)]
        public string? CardMNo { get; set; }

        public int? CardAmount { get; set; }

        [SugarColumn(Length = 16)]
        public string? CloseTime { get; set; }

        public bool CallAccount { get; set; }

        [SugarColumn(Length = 30)]
        public string? BadReason { get; set; }

        [SugarColumn(Length = 4)]
        public string? BadUserId { get; set; }

        public int AutoZD { get; set; }

        [SugarColumn(ColumnName = "rowguid", IndexGroupNameList = new[] { "index_100195407" })]
        public Guid Rowguid { get; set; }

        [SugarColumn(ColumnName = "msrepl_tran_version")]
        public Guid MsreplTranVersion { get; set; }
    }

    /// <summary>
    /// 查询
    /// </summary>
    public class RoomGetAllAsyncRequestDto : Pagination
    {
        /// <summary>
        /// 房间分页查询请求参数
        /// </summary>
        //public string UserId { get; set; }
        public int? Status { get; set; }
        public DateTime? AllDates { get; set; }
        /// <summary>
        /// 查询条件字符串
        /// </summary>
        public string? QueryCriteria { get; set; }
        //public Pagination Paging { get; set; }
    }

    /// <summary>
    /// 根据房间编号查询房间信息
    /// </summary>
    public class RoomGetByIdAsyncRequestDto
    {
        public string RmNo { get; set; }
    }
    /// <summary>
    /// 新增房间信息请求类
    /// </summary>
    public class RoomAddRequestDto
    {
        public RoomDto Model { get; set; }
    }
    /// <summary>
    /// 删除房间信息请求类
    /// </summary>
    public class RoomDeleteRequestDto
    {
        public string? RoomID { get; set; }
    }
    /// <summary>
    /// 修改房间信息请求类
    /// </summary>
    public class RoomUpdateRequestDto
    {
        public RoomDto Model { get; set; }
    }
}
