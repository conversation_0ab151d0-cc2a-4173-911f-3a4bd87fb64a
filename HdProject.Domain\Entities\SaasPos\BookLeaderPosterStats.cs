﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos
{
    public partial class BookLeaderPosterStats
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid StatsId { get; set; }
        public Guid LeaderId { get; set; }
        public int ViewCount { get; set; }
        public int OrderCount { get; set; }
        public decimal OrderAmount { get; set; }
        public int ConsumeCount { get; set; }
        public decimal ConsumeAmount { get; set; }
        public DateTime? UpdateTime { get; set; }
        public bool IsDeleted { get; set; }
    }
}
