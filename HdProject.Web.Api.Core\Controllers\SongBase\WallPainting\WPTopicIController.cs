﻿using System.Net.Http;
using HdProject.Application.Services.Interfaces.SongBase.WallPainting;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicI;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using NetTaste;
using Newtonsoft.Json;

namespace HdProject.Web.Api.Core.Controllers.SongBase.WallPainting
{
    /// <summary>
    /// 主题接口控制器
    /// </summary>
    public class WPTopicIController : PublicControllerBase
    {
        private readonly IWPTopicIService _wPTopicIService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<WPTopicIController> _logger;

        public WPTopicIController(IWPTopicIService wPTopicIService, IHttpClientFactory httpClientFactory, ILogger<WPTopicIController> logger)
        {
            _wPTopicIService = wPTopicIService;
            _httpClientFactory = httpClientFactory;
            _logger = logger;
        }

        /// <summary>
        /// 根据用户ID查询主题列表的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetByUserOpenId")]
        public async Task<IActionResult> GetByUserOpenIdTopicI([FromQuery] WPTopicIGetByUserOpenIdRequestDto request)
        {
            var result = await _wPTopicIService.GetByUserOpenIdAsync(request);
            return ApiData(result.Model);
        }


        /// <summary>
        /// 根据ID查询主题信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdTopicI([FromQuery] WPTopicIGetByIdRequestDto request)
        {
            var result = await _wPTopicIService.GetByIdAsync(request);
            return ApiData(result.Model);
        }



        /// <summary>
        /// 新增主题的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> TopicIAddValue([FromBody] WPTopicIAddRequestDto request)
        {
            var result = await _wPTopicIService.AddAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 修改主题的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> TopicIUpdateValue([FromBody] WPTopicIUpdateRequestDto request)
        {
            var result = await _wPTopicIService.UpdateAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 删除主题信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> TopicIDeletedValue([FromBody] WPTopicIDeleteRequestDto request)
        {
            var result = await _wPTopicIService.DeleteAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }


        /// <summary>
        /// 获取到门店以及合图路径（中转）接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetStoreAndPath")]
        public async Task<IActionResult> GetStoreAndPath([FromQuery] WPTopicGetStoreAndPathRequestDto request)
        {
            var url = "";
            int index1 = request.Store.IndexOf("--");
            int index2 = request.Store.IndexOf(",");
            if (index1 >= 0)
            {
                string[] s = request.Store.Split(new[] { "--" }, StringSplitOptions.None);
                if (s.Length != 3)
                {
                    return ApiError("无效二维码！");
                }
                //门店接口地址
                url = $"http://ktv{s[0]}.tang-hui.com.cn:89/SongJson/WallPainting/GetStoreAndPathDownload.aspx?" +
            $"StoreKey={s[0]}&RoomKey={s[1]}&StoreToken={s[2]}&GuId={request.GUID}&PathUrl={request.Path}";

            }
            else if (index2 >= 0)
            {
                string[] s = request.Store.Split(new[] { "," }, StringSplitOptions.None);
                if (s.Length != 3)
                {
                    return ApiError("无效二维码！");
                }
                //门店接口地址
                url = $"http://ktv{s[1]}.tang-hui.com.cn:89/SongJson/WallPainting/GetStoreAndPathDownload.aspx?" +
            $"StoreKey={s[1]}&RoomKey={s[2]}&StoreToken={s[0]}&GuId={request.GUID}&PathUrl={request.Path}";

            }

            if (string.IsNullOrEmpty(url)) 
            {
                return ApiError("无效二维码！");
            }

            //本地测试接口地址
            // var url = $"http://localhost:7100/SongJson/WallPainting/GetStoreAndPathDownload.aspx?" +
            //  $"StoreKey={s[0]}&RoomKey={s[1]}&StoreToken={s[2]}&GuId={request.GUID}&PathUrl={request.Path}";

            var client = _httpClientFactory.CreateClient();
            client.BaseAddress = new Uri(url);
            var response = await client.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var result = response.Content.ReadAsStringAsync();
                dynamic model = JsonConvert.DeserializeObject(result.Result);
                bool success = model.Success;
                if (success)
                {
                    return ApiSuccess();

                }
                else
                {
                    return ApiError("包房点播失败！");
                }
            }
            else
            {
                return ApiError("包房网络异常！");
            }


        }

    }
}
